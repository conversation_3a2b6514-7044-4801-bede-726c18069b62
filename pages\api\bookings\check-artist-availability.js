import { createClient } from '@supabase/supabase-js'
import { findBestArtist, ASSIGNMENT_STRATEGIES } from '@/lib/artist-assignment'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * API endpoint for checking artist availability for bookings
 * Provides real-time availability checking with artist assignment suggestions
 */
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] Artist availability check API called`)

  try {
    const {
      start_time,
      end_time,
      service_id,
      preferred_artist_id,
      booking_source = 'online',
      check_all_artists = false
    } = req.body

    if (!start_time || !end_time) {
      return res.status(400).json({ 
        error: 'Start time and end time are required' 
      })
    }

    console.log(`[${requestId}] Checking availability for ${start_time} to ${end_time}`)

    const availabilityResult = {
      available: false,
      artists: [],
      recommended_artist: null,
      conflicts: [],
      message: ''
    }

    // If specific artist is preferred, check their availability first
    if (preferred_artist_id) {
      const preferredAvailability = await checkSpecificArtistAvailability(
        preferred_artist_id, 
        start_time, 
        end_time, 
        service_id,
        requestId
      )

      if (preferredAvailability.available) {
        availabilityResult.available = true
        availabilityResult.recommended_artist = preferredAvailability.artist
        availabilityResult.message = 'Preferred artist is available'
        
        if (!check_all_artists) {
          return res.status(200).json(availabilityResult)
        }
      } else {
        availabilityResult.conflicts.push({
          artist_id: preferred_artist_id,
          reason: preferredAvailability.reason
        })
      }
    }

    // Find best available artist using assignment logic
    const bookingDetails = {
      start_time,
      end_time,
      service_id,
      booking_source,
      preferred_artist_id
    }

    const assignmentResult = await findBestArtist(
      bookingDetails, 
      ASSIGNMENT_STRATEGIES.AUTO_BEST_MATCH
    )

    if (assignmentResult.success) {
      availabilityResult.available = true
      
      if (!availabilityResult.recommended_artist) {
        availabilityResult.recommended_artist = assignmentResult.artist
      }
      
      availabilityResult.message = `${assignmentResult.availableCount} artist(s) available`
    }

    // If requested, get all available artists
    if (check_all_artists) {
      const allArtists = await getAllAvailableArtists(
        start_time, 
        end_time, 
        service_id, 
        booking_source,
        requestId
      )
      availabilityResult.artists = allArtists
    }

    // Set final message
    if (!availabilityResult.available) {
      availabilityResult.message = 'No artists available for the requested time slot'
    }

    console.log(`[${requestId}] Availability check completed: ${availabilityResult.available}`)

    return res.status(200).json(availabilityResult)

  } catch (error) {
    console.error(`[${requestId}] Error checking artist availability:`, error)
    return res.status(500).json({ 
      error: 'Internal server error',
      available: false 
    })
  }
}

/**
 * Check availability for a specific artist
 * @param {string} artistId - Artist ID
 * @param {string} startTime - Start time
 * @param {string} endTime - End time
 * @param {string} serviceId - Service ID
 * @param {string} requestId - Request ID for logging
 * @returns {Promise<Object>} - Availability result
 */
async function checkSpecificArtistAvailability(artistId, startTime, endTime, serviceId, requestId) {
  try {
    console.log(`[${requestId}] Checking specific artist availability: ${artistId}`)

    // Get artist profile
    const { data: artist, error: artistError } = await supabase
      .from('artist_profiles')
      .select('*')
      .eq('id', artistId)
      .single()

    if (artistError || !artist) {
      return {
        available: false,
        reason: 'Artist not found'
      }
    }

    // Check if artist is active and available today
    if (!artist.is_active) {
      return {
        available: false,
        reason: 'Artist is not active'
      }
    }

    if (!artist.is_available_today) {
      return {
        available: false,
        reason: 'Artist is not available today'
      }
    }

    // Check for booking conflicts
    const { data: conflicts, error: conflictError } = await supabase
      .from('bookings')
      .select('id, start_time, end_time, status')
      .eq('assigned_artist_id', artistId)
      .neq('status', 'canceled')
      .or(`and(start_time.lte.${startTime},end_time.gt.${startTime}),and(start_time.lt.${endTime},end_time.gte.${endTime}),and(start_time.gte.${startTime},end_time.lte.${endTime})`)

    if (conflictError) {
      console.error(`[${requestId}] Error checking conflicts:`, conflictError)
      return {
        available: false,
        reason: 'Error checking availability'
      }
    }

    if (conflicts && conflicts.length > 0) {
      return {
        available: false,
        reason: 'Artist has conflicting bookings',
        conflicts: conflicts
      }
    }

    // Check daily booking limit
    const dayStart = new Date(startTime)
    dayStart.setHours(0, 0, 0, 0)
    const dayEnd = new Date(dayStart)
    dayEnd.setDate(dayEnd.getDate() + 1)

    const { count: dailyBookings, error: countError } = await supabase
      .from('bookings')
      .select('*', { count: 'exact', head: true })
      .eq('assigned_artist_id', artistId)
      .gte('start_time', dayStart.toISOString())
      .lt('start_time', dayEnd.toISOString())
      .neq('status', 'canceled')

    if (countError) {
      console.error(`[${requestId}] Error checking daily bookings:`, countError)
    }

    const currentBookings = dailyBookings || 0
    if (currentBookings >= artist.max_daily_bookings) {
      return {
        available: false,
        reason: `Artist has reached daily booking limit (${artist.max_daily_bookings})`
      }
    }

    // Check service specialization if service is specified
    if (serviceId) {
      const hasSpecialization = await checkServiceSpecialization(artistId, serviceId)
      if (!hasSpecialization) {
        return {
          available: false,
          reason: 'Artist does not specialize in this service'
        }
      }
    }

    return {
      available: true,
      artist: {
        id: artist.id,
        artist_name: artist.artist_name,
        display_name: artist.display_name,
        specializations: artist.specializations,
        skill_level: artist.skill_level,
        current_bookings: currentBookings,
        max_daily_bookings: artist.max_daily_bookings
      }
    }

  } catch (error) {
    console.error(`[${requestId}] Error in checkSpecificArtistAvailability:`, error)
    return {
      available: false,
      reason: 'Error checking availability'
    }
  }
}

/**
 * Get all available artists for the time slot
 * @param {string} startTime - Start time
 * @param {string} endTime - End time
 * @param {string} serviceId - Service ID
 * @param {string} bookingSource - Booking source
 * @param {string} requestId - Request ID for logging
 * @returns {Promise<Array>} - Available artists
 */
async function getAllAvailableArtists(startTime, endTime, serviceId, bookingSource, requestId) {
  try {
    console.log(`[${requestId}] Getting all available artists`)

    // Get all active artists
    const { data: artists, error: artistsError } = await supabase
      .from('artist_profiles')
      .select('*')
      .eq('is_active', true)
      .eq('is_available_today', true)

    if (artistsError || !artists) {
      console.error(`[${requestId}] Error fetching artists:`, artistsError)
      return []
    }

    const availableArtists = []

    for (const artist of artists) {
      // Check booking source compatibility
      if (bookingSource === 'online' && !artist.accepts_online_bookings) continue
      if (bookingSource === 'walk_in' && !artist.accepts_walk_ins) continue

      // Check availability for this artist
      const availability = await checkSpecificArtistAvailability(
        artist.id, 
        startTime, 
        endTime, 
        serviceId, 
        requestId
      )

      if (availability.available) {
        availableArtists.push(availability.artist)
      }
    }

    console.log(`[${requestId}] Found ${availableArtists.length} available artists`)
    return availableArtists

  } catch (error) {
    console.error(`[${requestId}] Error in getAllAvailableArtists:`, error)
    return []
  }
}

/**
 * Check if artist specializes in a service
 * @param {string} artistId - Artist ID
 * @param {string} serviceId - Service ID
 * @returns {Promise<boolean>} - Whether artist specializes in service
 */
async function checkServiceSpecialization(artistId, serviceId) {
  try {
    // Check specific service preferences
    const { data: preference, error: prefError } = await supabase
      .from('artist_booking_preferences')
      .select('is_available_for_service')
      .eq('artist_id', artistId)
      .eq('service_id', serviceId)
      .single()

    if (prefError && prefError.code !== 'PGRST116') {
      console.error('Error checking service preference:', prefError)
      return true // Default to available on error
    }

    if (preference) {
      return preference.is_available_for_service
    }

    // Check general specializations
    const { data: service, error: serviceError } = await supabase
      .from('services')
      .select('category')
      .eq('id', serviceId)
      .single()

    if (serviceError) {
      return true // Default to available if can't determine service category
    }

    const { data: artist, error: artistError } = await supabase
      .from('artist_profiles')
      .select('specializations')
      .eq('id', artistId)
      .single()

    if (artistError) {
      return true // Default to available if can't determine specializations
    }

    const serviceCategory = service.category?.toLowerCase()
    const artistSpecializations = artist.specializations || []

    return artistSpecializations.some(spec => 
      spec.toLowerCase().includes(serviceCategory) || 
      serviceCategory.includes(spec.toLowerCase())
    )

  } catch (error) {
    console.error('Error in checkServiceSpecialization:', error)
    return true // Default to available on error
  }
}
