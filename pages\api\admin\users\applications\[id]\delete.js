import { getAdminClient } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'

/**
 * API endpoint for deleting artist/braider applications
 * DELETE /api/admin/users/applications/[id]/delete - Delete application (only for pending/incomplete applications)
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7)
  const { id: applicationId } = req.query

  console.log(`[${requestId}] Application delete API called for application: ${applicationId}`)

  if (req.method !== 'DELETE') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Verify admin authentication
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.authorized) {
      return res.status(401).json({ error: authResult.error?.message || 'Unauthorized' })
    }

    const { user: adminUser, role: adminRole } = authResult

    // Ensure user has admin privileges
    if (!['dev', 'admin'].includes(adminRole)) {
      return res.status(403).json({ error: 'Admin privileges required' })
    }

    if (!applicationId) {
      return res.status(400).json({ error: 'Application ID is required' })
    }

    console.log(`[${requestId}] Deleting application: ${applicationId}`)

    // Get admin client
    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Failed to initialize admin client' })
    }

    // Get application details first
    const { data: application, error: fetchError } = await adminClient
      .from('artist_braider_applications')
      .select(`
        id,
        user_id,
        application_type,
        status,
        created_at,
        reviewed_at
      `)
      .eq('id', applicationId)
      .single()

    if (fetchError) {
      console.error(`[${requestId}] Error fetching application:`, fetchError)
      if (fetchError.code === 'PGRST116') {
        return res.status(404).json({ error: 'Application not found' })
      }
      return res.status(500).json({ error: 'Failed to fetch application' })
    }

    // Check if application can be deleted
    const deletableStatuses = ['pending', 'under_review']
    if (!deletableStatuses.includes(application.status)) {
      return res.status(400).json({ 
        error: 'Cannot delete application',
        message: `Applications with status "${application.status}" cannot be deleted. Only pending or under review applications can be deleted.`
      })
    }

    // Get user details for logging
    const { data: userData, error: userError } = await adminClient.auth.admin.getUserById(application.user_id)
    if (userError) {
      console.warn(`[${requestId}] Could not fetch user details:`, userError)
    }

    // Delete related application tokens first
    console.log(`[${requestId}] Deleting related application tokens`)
    const { error: tokensDeleteError } = await adminClient
      .from('application_tokens')
      .delete()
      .eq('application_id', applicationId)

    if (tokensDeleteError) {
      console.warn(`[${requestId}] Warning: Could not delete application tokens:`, tokensDeleteError)
      // Continue anyway - application deletion is more important
    }

    // Delete the application
    console.log(`[${requestId}] Deleting application from database`)
    const { error: deleteError } = await adminClient
      .from('artist_braider_applications')
      .delete()
      .eq('id', applicationId)

    if (deleteError) {
      console.error(`[${requestId}] Error deleting application:`, deleteError)
      return res.status(500).json({ error: 'Failed to delete application' })
    }

    // Log the action
    try {
      await adminClient
        .from('admin_activity_log')
        .insert([
          {
            admin_user_id: adminUser.id,
            action: 'delete_application',
            target_type: 'application',
            target_id: applicationId,
            details: {
              application_type: application.application_type,
              application_status: application.status,
              user_email: userData?.user?.email || 'Unknown',
              user_id: application.user_id,
              deleted_at: new Date().toISOString()
            }
          }
        ])
    } catch (logError) {
      console.error(`[${requestId}] Error logging activity:`, logError)
      // Continue anyway
    }

    console.log(`[${requestId}] Application deleted successfully`)

    return res.status(200).json({
      success: true,
      message: 'Application deleted successfully',
      data: {
        applicationId: applicationId,
        applicationType: application.application_type,
        userEmail: userData?.user?.email || 'Unknown',
        deletedAt: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error(`[${requestId}] Unexpected error in delete application:`, error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
