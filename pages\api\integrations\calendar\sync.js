/**
 * Calendar Sync API Endpoint for Ocean Soul Sparkles
 * Handles calendar synchronization operations
 * 
 * Phase 7: Advanced Integrations & Ecosystem
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { syncRateLimit } from '@/lib/integrations/rate-limiter'
import { RequestValida<PERSON>, AuditLogger } from '@/lib/integrations/security-utils'
import CalendarSyncEngine from '@/lib/integrations/calendar/sync-engine'

/**
 * Calendar Sync Handler
 * POST /api/integrations/calendar/sync - Trigger calendar synchronization
 * GET /api/integrations/calendar/sync - Get sync status
 */
export default async function handler(req, res) {
  // Apply rate limiting
  await new Promise((resolve, reject) => {
    syncRateLimit(req, res, (error) => {
      if (error) reject(error)
      else resolve()
    })
  })

  const startTime = Date.now()

  try {
    // Validate request method
    RequestValidator.validateEndpointAccess(req, ['GET', 'POST'])

    // Authenticate user
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.success) {
      await AuditLogger.logSecurityEvent(
        null,
        'calendar_sync_unauthorized',
        {
          endpoint: req.url,
          method: req.method,
          userAgent: req.headers['user-agent'],
          ipAddress: req.headers['x-forwarded-for'] || req.connection?.remoteAddress
        },
        'warning'
      )

      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required for calendar sync'
      })
    }

    const { user } = authResult
    const userId = user.id

    // Initialize sync engine
    const syncEngine = new CalendarSyncEngine(userId)

    if (req.method === 'GET') {
      // Get sync status
      const syncStatus = await getSyncStatus(userId)
      
      await AuditLogger.logApiAccess(
        userId,
        req.url,
        req.method,
        200,
        Date.now() - startTime,
        { action: 'get_sync_status' }
      )

      return res.status(200).json({
        success: true,
        syncStatus
      })

    } else if (req.method === 'POST') {
      // Trigger sync
      const { syncType = 'full' } = req.body

      let syncResults
      switch (syncType) {
        case 'full':
          syncResults = await syncEngine.performFullSync()
          break
        case 'bookings_only':
          syncResults = await syncEngine.syncBookingsToCalendars()
          break
        case 'calendars_only':
          syncResults = await syncEngine.syncCalendarsToBookings()
          break
        default:
          return res.status(400).json({
            error: 'Invalid sync type',
            message: 'Sync type must be: full, bookings_only, or calendars_only'
          })
      }

      await AuditLogger.logIntegrationActivity(
        userId,
        'calendar_sync',
        `sync_triggered_${syncType}`,
        'success',
        syncResults
      )

      await AuditLogger.logApiAccess(
        userId,
        req.url,
        req.method,
        200,
        Date.now() - startTime,
        { action: 'trigger_sync', syncType, results: syncResults }
      )

      return res.status(200).json({
        success: true,
        message: 'Calendar sync completed successfully',
        results: syncResults
      })
    }

  } catch (error) {
    console.error('Calendar sync error:', error)

    await AuditLogger.logSecurityEvent(
      req.user?.id || null,
      'calendar_sync_error',
      {
        error: error.message,
        stack: error.stack,
        endpoint: req.url,
        method: req.method,
        userAgent: req.headers['user-agent'],
        ipAddress: req.headers['x-forwarded-for'] || req.connection?.remoteAddress
      },
      'error'
    )

    await AuditLogger.logApiAccess(
      req.user?.id || null,
      req.url,
      req.method,
      500,
      Date.now() - startTime,
      { error: error.message }
    )

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to perform calendar sync'
    })
  }
}

/**
 * Get sync status for user
 */
async function getSyncStatus(userId) {
  try {
    const { createClient } = await import('@supabase/supabase-js')
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )

    // Get sync status from database
    const { data: syncStatus, error } = await supabase
      .from('integration_sync_status')
      .select('*')
      .eq('user_id', userId)
      .eq('sync_type', 'calendar')

    if (error) {
      console.error('Failed to get sync status:', error)
      return {
        lastSync: null,
        nextSync: null,
        status: 'unknown',
        stats: {
          bookingsSynced: 0,
          eventsImported: 0,
          conflicts: 0
        }
      }
    }

    // Get recent sync logs
    const { data: logs, error: logsError } = await supabase
      .from('integration_logs')
      .select('*')
      .eq('user_id', userId)
      .in('action', ['full_sync_completed', 'booking_sync_completed', 'calendar_sync_completed'])
      .order('created_at', { ascending: false })
      .limit(10)

    if (logsError) {
      console.error('Failed to get sync logs:', logsError)
    }

    // Calculate stats from logs
    const stats = calculateSyncStats(logs || [])

    return {
      lastSync: syncStatus?.[0]?.last_sync_at || null,
      nextSync: syncStatus?.[0]?.next_sync_at || null,
      status: syncStatus?.[0]?.status || 'pending',
      stats
    }

  } catch (error) {
    console.error('Error getting sync status:', error)
    return {
      lastSync: null,
      nextSync: null,
      status: 'error',
      stats: {
        bookingsSynced: 0,
        eventsImported: 0,
        conflicts: 0
      }
    }
  }
}

/**
 * Calculate sync statistics from logs
 */
function calculateSyncStats(logs) {
  const stats = {
    bookingsSynced: 0,
    eventsImported: 0,
    conflicts: 0
  }

  logs.forEach(log => {
    if (log.details) {
      if (log.details.bookingsToCalendar) {
        stats.bookingsSynced += log.details.bookingsToCalendar.success || 0
      }
      if (log.details.calendarToBookings) {
        stats.eventsImported += log.details.calendarToBookings.success || 0
      }
      if (log.details.conflicts) {
        stats.conflicts += log.details.conflicts.length || 0
      }
    }
  })

  return stats
}

/**
 * API Route Configuration
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
