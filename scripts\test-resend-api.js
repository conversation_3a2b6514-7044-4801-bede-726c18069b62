/**
 * Test script for resend welcome email and token management APIs
 * Run this script to verify the APIs are working correctly
 * Usage: node scripts/test-resend-api.js
 */

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

async function testAPIs() {
  console.log('🧪 Testing Resend Welcome Email and Token Management APIs...\n');

  const baseUrl = 'http://localhost:3001';
  
  // Test headers for development mode
  const headers = {
    'Content-Type': 'application/json',
    // In development mode with auth bypass, we don't need a real token
    'Authorization': 'Bearer dev-bypass-token'
  };

  console.log('📋 Environment Check:');
  console.log('NODE_ENV:', process.env.NODE_ENV || 'development');
  console.log('ENABLE_AUTH_BYPASS:', process.env.ENABLE_AUTH_BYPASS);
  console.log('FORCE_EMAIL_IN_DEV:', process.env.FORCE_EMAIL_IN_DEV);
  console.log('');

  // Test 1: Token Management API (GET)
  console.log('🔧 Test 1: Token Management API (GET)');
  try {
    const response = await fetch(`${baseUrl}/api/admin/tokens/manage?userId=test-user-id`, {
      method: 'GET',
      headers
    });

    console.log('Status:', response.status);
    const result = await response.json();
    console.log('Response:', JSON.stringify(result, null, 2));
    
    if (response.ok) {
      console.log('✅ Token Management API (GET) is working');
    } else {
      console.log('❌ Token Management API (GET) failed');
    }
  } catch (error) {
    console.log('❌ Token Management API (GET) error:', error.message);
  }
  console.log('');

  // Test 2: Resend Welcome Email API
  console.log('🔧 Test 2: Resend Welcome Email API');
  try {
    const response = await fetch(`${baseUrl}/api/admin/users/resend-welcome`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        userId: 'test-user-id',
        forceResend: true
      })
    });

    console.log('Status:', response.status);
    const result = await response.json();
    console.log('Response:', JSON.stringify(result, null, 2));
    
    if (response.ok) {
      console.log('✅ Resend Welcome Email API is working');
    } else {
      console.log('❌ Resend Welcome Email API failed');
    }
  } catch (error) {
    console.log('❌ Resend Welcome Email API error:', error.message);
  }
  console.log('');

  // Test 3: Check if APIs are accessible (should not get auth function errors)
  console.log('🔧 Test 3: API Accessibility Check');
  
  const testEndpoints = [
    { name: 'Token Management', url: '/api/admin/tokens/manage?userId=test', method: 'GET' },
    { name: 'Resend Welcome', url: '/api/admin/users/resend-welcome', method: 'POST', body: { userId: 'test' } }
  ];

  for (const endpoint of testEndpoints) {
    try {
      const options = {
        method: endpoint.method,
        headers
      };

      if (endpoint.body) {
        options.body = JSON.stringify(endpoint.body);
      }

      const response = await fetch(`${baseUrl}${endpoint.url}`, options);
      const result = await response.json();

      console.log(`${endpoint.name}:`);
      console.log(`  Status: ${response.status}`);
      console.log(`  Has Error: ${!!result.error}`);
      
      // Check if we're getting the auth function error
      if (result.error && result.error.includes('verifyAuthToken') && result.error.includes('not a function')) {
        console.log('  ❌ Still has verifyAuthToken error');
      } else if (result.error && result.error.includes('Unauthorized')) {
        console.log('  ✅ Auth working (expected unauthorized in test)');
      } else if (result.error) {
        console.log(`  ⚠️  Other error: ${result.error}`);
      } else {
        console.log('  ✅ No auth function errors');
      }
    } catch (error) {
      console.log(`${endpoint.name}: ❌ Request failed - ${error.message}`);
    }
  }

  console.log('');
  console.log('🏁 API testing complete!');
  console.log('');
  console.log('📋 Next Steps:');
  console.log('1. If APIs are working, test in the admin interface');
  console.log('2. Navigate to /admin/users and try the new buttons');
  console.log('3. Check console logs for detailed error messages');
  console.log('4. Verify email functionality with a real user');
}

// Run the test
testAPIs().catch(console.error);
