/**
 * Calendar Conflict Resolution API Endpoint for Ocean Soul Sparkles
 * Handles resolution of calendar conflicts
 * 
 * Phase 7: Advanced Integrations & Ecosystem
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { integrationRateLimit } from '@/lib/integrations/rate-limiter'
import { Request<PERSON><PERSON><PERSON><PERSON>, AuditLogger } from '@/lib/integrations/security-utils'
import CalendarManager from '@/lib/integrations/calendar/calendar-manager'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * Calendar Conflict Resolution Handler
 * POST /api/integrations/calendar/resolve-conflict - Resolve calendar conflict
 */
export default async function handler(req, res) {
  // Apply rate limiting
  await new Promise((resolve, reject) => {
    integrationRateLimit(req, res, (error) => {
      if (error) reject(error)
      else resolve()
    })
  })

  const startTime = Date.now()

  try {
    // Validate request method
    RequestValidator.validateEndpointAccess(req, ['POST'])

    // Authenticate user
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    const { user } = authResult
    const userId = user.id

    const { conflictId, resolution, bookingId, newDateTime } = req.body

    if (!conflictId || !resolution) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'conflictId and resolution are required'
      })
    }

    // Validate resolution type
    const validResolutions = ['keep_booking', 'reschedule', 'cancel', 'ignore']
    if (!validResolutions.includes(resolution)) {
      return res.status(400).json({
        error: 'Invalid resolution',
        message: `Resolution must be one of: ${validResolutions.join(', ')}`
      })
    }

    // Get booking details if bookingId provided
    let booking = null
    if (bookingId) {
      const { data: bookingData, error } = await supabase
        .from('bookings')
        .select(`
          *,
          customers(name, email),
          services(name, duration)
        `)
        .eq('id', bookingId)
        .eq('customer_id', userId)
        .single()

      if (error) {
        return res.status(404).json({
          error: 'Booking not found',
          message: 'The specified booking was not found or you do not have access to it'
        })
      }

      booking = bookingData
    }

    const calendarManager = new CalendarManager(userId)
    let resolutionResult = {}

    switch (resolution) {
      case 'keep_booking':
        resolutionResult = await resolveKeepBooking(userId, booking, calendarManager)
        break

      case 'reschedule':
        if (!newDateTime) {
          return res.status(400).json({
            error: 'Missing new date/time',
            message: 'newDateTime is required for reschedule resolution'
          })
        }
        resolutionResult = await resolveReschedule(userId, booking, newDateTime, calendarManager)
        break

      case 'cancel':
        resolutionResult = await resolveCancel(userId, booking, calendarManager)
        break

      case 'ignore':
        resolutionResult = await resolveIgnore(userId, conflictId)
        break

      default:
        return res.status(400).json({
          error: 'Invalid resolution',
          message: 'Unknown resolution type'
        })
    }

    await AuditLogger.logIntegrationActivity(
      userId,
      'calendar_conflict',
      `conflict_resolved_${resolution}`,
      'success',
      {
        conflictId,
        bookingId,
        resolution,
        result: resolutionResult
      }
    )

    await AuditLogger.logApiAccess(
      userId,
      req.url,
      req.method,
      200,
      Date.now() - startTime,
      { action: 'resolve_conflict', resolution, conflictId }
    )

    return res.status(200).json({
      success: true,
      message: `Conflict resolved: ${resolution}`,
      result: resolutionResult
    })

  } catch (error) {
    console.error('Conflict resolution error:', error)

    await AuditLogger.logApiAccess(
      req.user?.id || null,
      req.url,
      req.method,
      500,
      Date.now() - startTime,
      { error: error.message }
    )

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to resolve conflict'
    })
  }
}

/**
 * Resolve conflict by keeping the Ocean Soul Sparkles booking
 */
async function resolveKeepBooking(userId, booking, calendarManager) {
  try {
    // Update external calendars to reflect the booking
    const syncResults = await calendarManager.syncBookingToCalendars(booking, 'create')
    
    // Mark conflict as resolved
    await markConflictResolved(userId, booking.id, 'keep_booking')

    return {
      action: 'keep_booking',
      bookingKept: true,
      calendarUpdated: syncResults.filter(r => r.success).length > 0,
      syncResults
    }

  } catch (error) {
    console.error('Failed to resolve keep booking:', error)
    throw error
  }
}

/**
 * Resolve conflict by rescheduling the booking
 */
async function resolveReschedule(userId, booking, newDateTime, calendarManager) {
  try {
    // Validate new date/time
    const newDate = new Date(newDateTime)
    if (isNaN(newDate.getTime())) {
      throw new Error('Invalid new date/time format')
    }

    // Check for conflicts at new time
    const duration = booking.services?.duration || 60
    const newEndTime = new Date(newDate.getTime() + duration * 60000)
    
    const conflicts = await calendarManager.checkConflicts(newDate, newEndTime, [])
    if (conflicts.length > 0) {
      throw new Error('New time slot also has conflicts')
    }

    // Update booking in database
    const { error: updateError } = await supabase
      .from('bookings')
      .update({
        booking_date: newDate.toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', booking.id)
      .eq('customer_id', userId)

    if (updateError) {
      throw new Error(`Failed to update booking: ${updateError.message}`)
    }

    // Update external calendars
    const updatedBooking = { ...booking, booking_date: newDate.toISOString() }
    const syncResults = await calendarManager.syncBookingToCalendars(updatedBooking, 'update')

    // Mark conflict as resolved
    await markConflictResolved(userId, booking.id, 'reschedule')

    return {
      action: 'reschedule',
      oldDateTime: booking.booking_date,
      newDateTime: newDate.toISOString(),
      bookingUpdated: true,
      calendarUpdated: syncResults.filter(r => r.success).length > 0,
      syncResults
    }

  } catch (error) {
    console.error('Failed to resolve reschedule:', error)
    throw error
  }
}

/**
 * Resolve conflict by canceling the booking
 */
async function resolveCancel(userId, booking, calendarManager) {
  try {
    // Update booking status to cancelled
    const { error: updateError } = await supabase
      .from('bookings')
      .update({
        status: 'cancelled',
        cancelled_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', booking.id)
      .eq('customer_id', userId)

    if (updateError) {
      throw new Error(`Failed to cancel booking: ${updateError.message}`)
    }

    // Remove from external calendars
    const syncResults = await calendarManager.syncBookingToCalendars(booking, 'delete')

    // Mark conflict as resolved
    await markConflictResolved(userId, booking.id, 'cancel')

    return {
      action: 'cancel',
      bookingCancelled: true,
      calendarUpdated: syncResults.filter(r => r.success).length > 0,
      syncResults
    }

  } catch (error) {
    console.error('Failed to resolve cancel:', error)
    throw error
  }
}

/**
 * Resolve conflict by ignoring it
 */
async function resolveIgnore(userId, conflictId) {
  try {
    // Mark conflict as ignored in database
    await markConflictResolved(userId, conflictId, 'ignore')

    return {
      action: 'ignore',
      conflictIgnored: true
    }

  } catch (error) {
    console.error('Failed to resolve ignore:', error)
    throw error
  }
}

/**
 * Mark conflict as resolved in database
 */
async function markConflictResolved(userId, identifier, resolution) {
  try {
    // Store conflict resolution in logs
    await AuditLogger.logIntegrationActivity(
      userId,
      'calendar_conflict',
      'conflict_marked_resolved',
      'success',
      {
        identifier,
        resolution,
        resolvedAt: new Date().toISOString()
      }
    )

  } catch (error) {
    console.error('Failed to mark conflict as resolved:', error)
  }
}

/**
 * API Route Configuration
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
