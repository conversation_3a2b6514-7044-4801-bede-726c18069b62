/* Mobile Test Page Styles */

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 16px;
  background: #f8fafc;
  min-height: 100vh;
  padding-bottom: 120px; /* Space for mobile navigation */
}

.header {
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.header h1 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 1.75rem;
  font-weight: 700;
}

.header p {
  margin: 0;
  color: #6b7280;
  font-size: 1rem;
}

.deviceInfo {
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.deviceInfo h2 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.infoItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.infoLabel {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.infoValue {
  font-size: 0.875rem;
  color: #1f2937;
  font-weight: 600;
}

.sections {
  position: relative;
  min-height: 400px;
}

.testButton {
  width: 100%;
  padding: 12px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 12px;
  min-height: 48px;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.testButton:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.testButton:active {
  transform: scale(0.98);
  background: #1d4ed8;
}

.testButton:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.optimalSettings {
  margin-top: 16px;
  padding: 12px;
  background: #f0f9ff;
  border-radius: 6px;
  border: 1px solid #bae6fd;
}

.optimalSettings h4 {
  margin: 0 0 8px 0;
  color: #0c4a6e;
  font-size: 0.875rem;
  font-weight: 600;
}

.optimalSettings p {
  margin: 4px 0;
  font-size: 0.8125rem;
  color: #0369a1;
}

.performanceInfo {
  margin-top: 16px;
  padding: 12px;
  background: #fef3c7;
  border-radius: 6px;
  border: 1px solid #fbbf24;
}

.performanceInfo h4 {
  margin: 0 0 8px 0;
  color: #92400e;
  font-size: 0.875rem;
  font-weight: 600;
}

.performanceInfo p {
  margin: 4px 0;
  font-size: 0.8125rem;
  color: #b45309;
}

.testResults {
  margin-top: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.testResults h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 600;
}

.resultsList {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.resultItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  font-size: 0.8125rem;
}

.resultTime {
  color: #6b7280;
  font-weight: 500;
  min-width: 70px;
}

.resultCategory {
  color: #3b82f6;
  font-weight: 600;
  min-width: 100px;
}

.resultMessage {
  color: #1f2937;
  flex: 1;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .container {
    padding: 12px;
    padding-bottom: 140px;
  }

  .header {
    padding: 16px;
    margin-bottom: 16px;
  }

  .header h1 {
    font-size: 1.5rem;
  }

  .header p {
    font-size: 0.875rem;
  }

  .deviceInfo {
    padding: 16px;
    margin-bottom: 16px;
  }

  .infoGrid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .infoItem {
    padding: 10px 12px;
  }

  .testResults {
    padding: 16px;
    margin-top: 16px;
  }

  .resultItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .resultTime,
  .resultCategory {
    min-width: auto;
  }
}

/* Phone optimizations */
@media (max-width: 480px) {
  .container {
    padding: 8px;
    padding-bottom: 160px;
  }

  .header {
    padding: 12px;
  }

  .header h1 {
    font-size: 1.25rem;
  }

  .header p {
    font-size: 0.8125rem;
  }

  .deviceInfo {
    padding: 12px;
  }

  .testButton {
    padding: 14px 16px;
    font-size: 0.9375rem;
    min-height: 52px;
  }

  .testResults {
    padding: 12px;
  }

  .resultsList {
    max-height: 200px;
  }

  .resultItem {
    padding: 8px 10px;
    font-size: 0.75rem;
  }
}

/* Landscape mobile optimizations */
@media (max-width: 768px) and (orientation: landscape) and (max-height: 500px) {
  .container {
    padding-bottom: 100px;
  }

  .header {
    padding: 8px 12px;
  }

  .header h1 {
    font-size: 1.125rem;
  }

  .header p {
    font-size: 0.75rem;
  }

  .deviceInfo {
    padding: 12px;
  }

  .testResults {
    padding: 12px;
  }

  .resultsList {
    max-height: 150px;
  }
}

/* Scrollbar styling */
.resultsList::-webkit-scrollbar {
  width: 4px;
}

.resultsList::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.resultsList::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.resultsList::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .testButton {
    transition: none;
  }

  .testButton:hover:not(:disabled) {
    transform: none;
  }

  .testButton:active {
    transform: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .container {
    background: #111827;
  }

  .header,
  .deviceInfo,
  .testResults {
    background: #1f2937;
    border-color: #374151;
  }

  .header h1,
  .deviceInfo h2,
  .testResults h3 {
    color: #f9fafb;
  }

  .header p {
    color: #d1d5db;
  }

  .infoItem,
  .resultItem {
    background: #374151;
    border-color: #4b5563;
  }

  .infoLabel,
  .resultTime {
    color: #9ca3af;
  }

  .infoValue,
  .resultMessage {
    color: #f3f4f6;
  }

  .resultCategory {
    color: #60a5fa;
  }

  .optimalSettings {
    background: #1e3a8a;
    border-color: #3b82f6;
  }

  .optimalSettings h4,
  .optimalSettings p {
    color: #dbeafe;
  }

  .performanceInfo {
    background: #92400e;
    border-color: #f59e0b;
  }

  .performanceInfo h4,
  .performanceInfo p {
    color: #fef3c7;
  }
}
