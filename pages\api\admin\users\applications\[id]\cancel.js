import { getAdminClient } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'

/**
 * API endpoint for canceling artist/braider applications
 * PATCH /api/admin/users/applications/[id]/cancel - Cancel application (sets status to cancelled)
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7)
  const { id: applicationId } = req.query

  console.log(`[${requestId}] Application cancel API called for application: ${applicationId}`)

  if (req.method !== 'PATCH') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Verify admin authentication
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.authorized) {
      return res.status(401).json({ error: authResult.error?.message || 'Unauthorized' })
    }

    const { user: adminUser, role: adminRole } = authResult

    // Ensure user has admin privileges
    if (!['dev', 'admin'].includes(adminRole)) {
      return res.status(403).json({ error: 'Admin privileges required' })
    }

    if (!applicationId) {
      return res.status(400).json({ error: 'Application ID is required' })
    }

    const { reason = 'Cancelled by administrator' } = req.body

    console.log(`[${requestId}] Cancelling application: ${applicationId}`)

    // Get admin client
    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Failed to initialize admin client' })
    }

    // Get application details first
    const { data: application, error: fetchError } = await adminClient
      .from('artist_braider_applications')
      .select(`
        id,
        user_id,
        application_type,
        status,
        created_at
      `)
      .eq('id', applicationId)
      .single()

    if (fetchError) {
      console.error(`[${requestId}] Error fetching application:`, fetchError)
      if (fetchError.code === 'PGRST116') {
        return res.status(404).json({ error: 'Application not found' })
      }
      return res.status(500).json({ error: 'Failed to fetch application' })
    }

    // Check if application can be cancelled
    const cancellableStatuses = ['pending', 'under_review']
    if (!cancellableStatuses.includes(application.status)) {
      return res.status(400).json({ 
        error: 'Cannot cancel application',
        message: `Applications with status "${application.status}" cannot be cancelled. Only pending or under review applications can be cancelled.`
      })
    }

    // Get user details for logging
    const { data: userData, error: userError } = await adminClient.auth.admin.getUserById(application.user_id)
    if (userError) {
      console.warn(`[${requestId}] Could not fetch user details:`, userError)
    }

    // Update application status to cancelled
    console.log(`[${requestId}] Updating application status to cancelled`)
    const { data: updatedApplication, error: updateError } = await adminClient
      .from('artist_braider_applications')
      .update({
        status: 'cancelled',
        reviewed_at: new Date().toISOString(),
        reviewed_by: adminUser.id,
        review_notes: reason,
        updated_at: new Date().toISOString()
      })
      .eq('id', applicationId)
      .select()
      .single()

    if (updateError) {
      console.error(`[${requestId}] Error updating application:`, updateError)
      return res.status(500).json({ error: 'Failed to cancel application' })
    }

    // Invalidate any active application tokens
    console.log(`[${requestId}] Invalidating application tokens`)
    const { error: tokensUpdateError } = await adminClient
      .from('application_tokens')
      .update({
        is_used: true,
        used_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('application_id', applicationId)
      .eq('is_used', false)

    if (tokensUpdateError) {
      console.warn(`[${requestId}] Warning: Could not invalidate application tokens:`, tokensUpdateError)
      // Continue anyway - application cancellation is more important
    }

    // Log the action
    try {
      await adminClient
        .from('admin_activity_log')
        .insert([
          {
            admin_user_id: adminUser.id,
            action: 'cancel_application',
            target_type: 'application',
            target_id: applicationId,
            details: {
              application_type: application.application_type,
              previous_status: application.status,
              cancellation_reason: reason,
              user_email: userData?.user?.email || 'Unknown',
              user_id: application.user_id,
              cancelled_at: new Date().toISOString()
            }
          }
        ])
    } catch (logError) {
      console.error(`[${requestId}] Error logging activity:`, logError)
      // Continue anyway
    }

    console.log(`[${requestId}] Application cancelled successfully`)

    return res.status(200).json({
      success: true,
      message: 'Application cancelled successfully',
      data: {
        applicationId: applicationId,
        applicationType: application.application_type,
        previousStatus: application.status,
        newStatus: 'cancelled',
        reason: reason,
        userEmail: userData?.user?.email || 'Unknown',
        cancelledAt: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error(`[${requestId}] Unexpected error in cancel application:`, error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
