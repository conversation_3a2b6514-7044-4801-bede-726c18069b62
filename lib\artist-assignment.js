/**
 * Artist Assignment Logic for Booking System
 * Handles automatic and manual artist assignment to bookings
 */

import { supabase } from '@/lib/supabase'

/**
 * Assignment strategies
 */
export const ASSIGNMENT_STRATEGIES = {
  AUTO_BEST_MATCH: 'auto_best_match',
  AUTO_ROUND_ROBIN: 'auto_round_robin',
  AUTO_LEAST_BUSY: 'auto_least_busy',
  MANUAL: 'manual',
  CUSTOMER_PREFERENCE: 'customer_preference'
}

/**
 * Find the best artist for a booking using automatic assignment
 * @param {Object} booking - Booking details
 * @param {string} strategy - Assignment strategy
 * @returns {Promise<Object>} - Assignment result
 */
export async function findBestArtist(booking, strategy = ASSIGNMENT_STRATEGIES.AUTO_BEST_MATCH) {
  try {
    console.log(`Finding best artist for booking ${booking.id} using strategy: ${strategy}`)

    // Get all active artists
    const { data: artists, error: artistsError } = await supabase
      .from('artist_profiles')
      .select(`
        id,
        user_id,
        artist_name,
        display_name,
        specializations,
        skill_level,
        is_active,
        is_available_today,
        max_daily_bookings,
        booking_buffer_time,
        accepts_online_bookings,
        accepts_walk_ins
      `)
      .eq('is_active', true)
      .eq('is_available_today', true)

    if (artistsError) {
      throw artistsError
    }

    if (!artists || artists.length === 0) {
      return {
        success: false,
        error: 'No active artists available'
      }
    }

    // Filter artists based on booking requirements
    const availableArtists = await filterAvailableArtists(artists, booking)

    if (availableArtists.length === 0) {
      return {
        success: false,
        error: 'No artists available for the requested time slot'
      }
    }

    // Apply assignment strategy
    let selectedArtist
    switch (strategy) {
      case ASSIGNMENT_STRATEGIES.AUTO_BEST_MATCH:
        selectedArtist = await selectBestMatchArtist(availableArtists, booking)
        break
      case ASSIGNMENT_STRATEGIES.AUTO_ROUND_ROBIN:
        selectedArtist = await selectRoundRobinArtist(availableArtists, booking)
        break
      case ASSIGNMENT_STRATEGIES.AUTO_LEAST_BUSY:
        selectedArtist = await selectLeastBusyArtist(availableArtists, booking)
        break
      default:
        selectedArtist = availableArtists[0] // Fallback to first available
    }

    return {
      success: true,
      artist: selectedArtist,
      strategy: strategy,
      availableCount: availableArtists.length
    }

  } catch (error) {
    console.error('Error finding best artist:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * Filter artists based on availability and booking requirements
 * @param {Array} artists - List of artists
 * @param {Object} booking - Booking details
 * @returns {Promise<Array>} - Filtered available artists
 */
async function filterAvailableArtists(artists, booking) {
  const availableArtists = []

  for (const artist of artists) {
    // Check if artist accepts this type of booking
    const bookingSource = booking.booking_source || 'online'
    if (bookingSource === 'online' && !artist.accepts_online_bookings) continue
    if (bookingSource === 'walk_in' && !artist.accepts_walk_ins) continue

    // Check service specialization
    if (booking.service_id) {
      const hasSpecialization = await checkServiceSpecialization(artist.id, booking.service_id)
      if (!hasSpecialization) continue
    }

    // Check time availability
    const isTimeAvailable = await checkTimeAvailability(artist.id, booking.start_time, booking.end_time)
    if (!isTimeAvailable) continue

    // Check daily booking limit
    const dailyBookingCount = await getDailyBookingCount(artist.id, booking.start_time)
    if (dailyBookingCount >= artist.max_daily_bookings) continue

    // Add availability score for ranking
    artist.availabilityScore = await calculateAvailabilityScore(artist, booking)
    availableArtists.push(artist)
  }

  return availableArtists
}

/**
 * Check if artist has specialization for the service
 * @param {string} artistId - Artist ID
 * @param {string} serviceId - Service ID
 * @returns {Promise<boolean>} - Whether artist can perform service
 */
async function checkServiceSpecialization(artistId, serviceId) {
  try {
    // Check artist_booking_preferences for specific service
    const { data: preference, error } = await supabase
      .from('artist_booking_preferences')
      .select('is_available_for_service')
      .eq('artist_id', artistId)
      .eq('service_id', serviceId)
      .single()

    if (error && error.code !== 'PGRST116') {
      console.error('Error checking service specialization:', error)
      return false
    }

    // If specific preference exists, use it
    if (preference) {
      return preference.is_available_for_service
    }

    // Otherwise, check general specializations
    const { data: service, error: serviceError } = await supabase
      .from('services')
      .select('category')
      .eq('id', serviceId)
      .single()

    if (serviceError) {
      console.error('Error fetching service category:', serviceError)
      return true // Default to available if can't determine
    }

    const { data: artist, error: artistError } = await supabase
      .from('artist_profiles')
      .select('specializations')
      .eq('id', artistId)
      .single()

    if (artistError) {
      console.error('Error fetching artist specializations:', artistError)
      return true // Default to available if can't determine
    }

    // Check if artist's specializations match service category
    const serviceCategory = service.category?.toLowerCase()
    const artistSpecializations = artist.specializations || []

    return artistSpecializations.some(spec => 
      spec.toLowerCase().includes(serviceCategory) || 
      serviceCategory.includes(spec.toLowerCase())
    )

  } catch (error) {
    console.error('Error in checkServiceSpecialization:', error)
    return true // Default to available on error
  }
}

/**
 * Check if artist is available at the requested time
 * @param {string} artistId - Artist ID
 * @param {string} startTime - Booking start time
 * @param {string} endTime - Booking end time
 * @returns {Promise<boolean>} - Whether time slot is available
 */
async function checkTimeAvailability(artistId, startTime, endTime) {
  try {
    // Check for conflicting bookings
    const { data: conflicts, error } = await supabase
      .from('bookings')
      .select('id')
      .eq('assigned_artist_id', artistId)
      .neq('status', 'canceled')
      .or(`and(start_time.lte.${startTime},end_time.gt.${startTime}),and(start_time.lt.${endTime},end_time.gte.${endTime}),and(start_time.gte.${startTime},end_time.lte.${endTime})`)

    if (error) {
      console.error('Error checking time availability:', error)
      return false
    }

    return !conflicts || conflicts.length === 0

  } catch (error) {
    console.error('Error in checkTimeAvailability:', error)
    return false
  }
}

/**
 * Get daily booking count for an artist
 * @param {string} artistId - Artist ID
 * @param {string} date - Date to check (ISO string)
 * @returns {Promise<number>} - Number of bookings for the day
 */
async function getDailyBookingCount(artistId, date) {
  try {
    const dayStart = new Date(date)
    dayStart.setHours(0, 0, 0, 0)
    const dayEnd = new Date(dayStart)
    dayEnd.setDate(dayEnd.getDate() + 1)

    const { count, error } = await supabase
      .from('bookings')
      .select('*', { count: 'exact', head: true })
      .eq('assigned_artist_id', artistId)
      .gte('start_time', dayStart.toISOString())
      .lt('start_time', dayEnd.toISOString())
      .neq('status', 'canceled')

    if (error) {
      console.error('Error getting daily booking count:', error)
      return 0
    }

    return count || 0

  } catch (error) {
    console.error('Error in getDailyBookingCount:', error)
    return 0
  }
}

/**
 * Calculate availability score for ranking artists
 * @param {Object} artist - Artist object
 * @param {Object} booking - Booking object
 * @returns {Promise<number>} - Availability score (higher is better)
 */
async function calculateAvailabilityScore(artist, booking) {
  let score = 0

  // Base score from skill level
  const skillScores = { beginner: 1, intermediate: 2, advanced: 3, expert: 4 }
  score += skillScores[artist.skill_level] || 2

  // Bonus for service specialization match
  if (booking.service_id) {
    const hasSpecialization = await checkServiceSpecialization(artist.id, booking.service_id)
    if (hasSpecialization) score += 3
  }

  // Penalty for high booking load
  const dailyBookings = await getDailyBookingCount(artist.id, booking.start_time)
  const loadRatio = dailyBookings / artist.max_daily_bookings
  score -= loadRatio * 2

  return score
}

/**
 * Select best match artist based on specialization and availability
 * @param {Array} artists - Available artists
 * @param {Object} booking - Booking details
 * @returns {Object} - Selected artist
 */
async function selectBestMatchArtist(artists, booking) {
  // Sort by availability score (highest first)
  artists.sort((a, b) => (b.availabilityScore || 0) - (a.availabilityScore || 0))
  return artists[0]
}

/**
 * Select artist using round-robin strategy
 * @param {Array} artists - Available artists
 * @param {Object} booking - Booking details
 * @returns {Object} - Selected artist
 */
async function selectRoundRobinArtist(artists, booking) {
  // Get recent assignment counts
  const artistCounts = await Promise.all(
    artists.map(async (artist) => {
      const count = await getDailyBookingCount(artist.id, booking.start_time)
      return { artist, count }
    })
  )

  // Sort by assignment count (lowest first)
  artistCounts.sort((a, b) => a.count - b.count)
  return artistCounts[0].artist
}

/**
 * Select least busy artist
 * @param {Array} artists - Available artists
 * @param {Object} booking - Booking details
 * @returns {Object} - Selected artist
 */
async function selectLeastBusyArtist(artists, booking) {
  return await selectRoundRobinArtist(artists, booking) // Same logic as round-robin
}

/**
 * Assign artist to booking
 * @param {string} bookingId - Booking ID
 * @param {string} artistId - Artist ID
 * @param {string} assignmentType - Assignment type
 * @param {string} notes - Assignment notes
 * @returns {Promise<Object>} - Assignment result
 */
export async function assignArtistToBooking(bookingId, artistId, assignmentType = 'manual', notes = '') {
  try {
    console.log(`Assigning artist ${artistId} to booking ${bookingId}`)

    // Update booking with artist assignment
    const { data: updatedBooking, error: updateError } = await supabase
      .from('bookings')
      .update({
        assigned_artist_id: artistId,
        artist_assignment_type: assignmentType,
        artist_assignment_notes: notes,
        updated_at: new Date().toISOString()
      })
      .eq('id', bookingId)
      .select()
      .single()

    if (updateError) {
      throw updateError
    }

    // Log assignment history
    const { error: historyError } = await supabase
      .from('booking_assignment_history')
      .insert([{
        booking_id: bookingId,
        new_artist_id: artistId,
        assignment_reason: assignmentType,
        notes: notes
      }])

    if (historyError) {
      console.error('Error logging assignment history:', historyError)
      // Don't fail the assignment for history logging errors
    }

    return {
      success: true,
      booking: updatedBooking
    }

  } catch (error) {
    console.error('Error assigning artist to booking:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
