import { getAdminClient } from '@/lib/supabase'
import { sendNotificationEmail } from '@/lib/notifications-server'
import { generateApprovalEmail } from '@/lib/email-templates'
import { authenticateAdminRequest } from '@/lib/admin-auth'

/**
 * API endpoint for reviewing artist/braider applications
 * POST /api/admin/users/applications/[id]/review - Review application (approve/reject)
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7)
  const { id: applicationId } = req.query

  console.log(`[${requestId}] Application review API called for application: ${applicationId}`)

  try {
    // Authenticate admin request
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.authorized) {
      console.log(`[${requestId}] Authentication failed: ${authResult.error?.message}`)
      return res.status(401).json({
        error: 'Unauthorized',
        message: authResult.error?.message || 'Authentication required'
      })
    }

    const { user: adminUser, role: adminRole } = authResult
    console.log(`[${requestId}] Authenticated user: ${adminUser.email} (${adminRole})`)

    // Ensure user has admin privileges for application review
    if (!['dev', 'admin'].includes(adminRole)) {
      return res.status(403).json({ error: 'Admin privileges required for application review' })
    }

    if (req.method === 'POST') {
      return await handleReviewApplication(req, res, applicationId, adminUser, requestId)
    } else {
      return res.status(405).json({ error: 'Method not allowed' })
    }
  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}

/**
 * Handle POST request - review application (approve/reject)
 */
async function handleReviewApplication(req, res, applicationId, reviewer, requestId) {
  try {
    const { status, notes } = req.body

    console.log(`[${requestId}] Reviewing application:`, { applicationId, status, notes })

    // Validate required fields
    if (!status || !['approved', 'rejected', 'under_review'].includes(status)) {
      return res.status(400).json({ 
        error: 'Invalid status',
        message: 'Status must be one of: approved, rejected, under_review'
      })
    }

    const adminClient = getAdminClient()
    if (!adminClient) {
      throw new Error('Failed to initialize admin client')
    }

    // First, fetch the application with user details
    const { data: application, error: fetchError } = await adminClient
      .from('artist_braider_applications')
      .select(`
        id,
        user_id,
        application_type,
        experience_years,
        portfolio_url,
        availability_preferences,
        service_specializations,
        previous_experience,
        professional_references,
        status,
        reviewed_by,
        reviewed_at,
        review_notes,
        welcome_email_sent,
        welcome_email_sent_at,
        created_at,
        updated_at
      `)
      .eq('id', applicationId)
      .single()

    if (fetchError || !application) {
      console.error(`[${requestId}] Application not found:`, fetchError)
      return res.status(404).json({ error: 'Application not found' })
    }

    // Get user profile data separately
    const { data: userProfile, error: profileError } = await adminClient
      .from('user_profiles')
      .select('name, phone')
      .eq('id', application.user_id)
      .single()

    console.log(`[${requestId}] Found application for user: ${userProfile?.name || 'Unknown'}`)

    // Update application status
    const { data: updatedApplication, error: updateError } = await adminClient
      .from('artist_braider_applications')
      .update({
        status,
        review_notes: notes,
        reviewed_by: reviewer.id,
        reviewed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', applicationId)
      .select()
      .single()

    if (updateError) {
      console.error(`[${requestId}] Error updating application:`, updateError)
      throw updateError
    }

    console.log(`[${requestId}] Application status updated to: ${status}`)

    // If approved, create artist profile and send welcome email
    if (status === 'approved') {
      await handleApprovalWorkflow(adminClient, application, userProfile, requestId)
    }

    return res.status(200).json({
      success: true,
      application: updatedApplication,
      message: `Application ${status} successfully`
    })
  } catch (error) {
    console.error(`[${requestId}] Error in handleReviewApplication:`, error)
    return res.status(500).json({ 
      error: 'Failed to review application',
      message: error.message 
    })
  }
}

/**
 * Handle approval workflow - create artist profile and send welcome email
 */
async function handleApprovalWorkflow(adminClient, application, userProfile, requestId) {
  try {
    console.log(`[${requestId}] Starting approval workflow for user: ${application.user_id}`)

    // 1. Update user role to artist/braider
    const { error: roleError } = await adminClient
      .from('user_roles')
      .upsert({
        id: application.user_id,
        role: application.application_type
      })

    if (roleError) {
      console.error(`[${requestId}] Error updating user role:`, roleError)
      // Continue anyway - role update is not critical
    } else {
      console.log(`[${requestId}] User role updated to: ${application.application_type}`)
    }

    // 2. Create artist profile
    const artistProfileData = {
      user_id: application.user_id,
      artist_name: userProfile?.name || 'New Artist',
      display_name: userProfile?.name || 'New Artist',
      bio: application.previous_experience || '',
      specializations: application.service_specializations || [],
      skill_level: application.experience_years >= 5 ? 'advanced' :
                   application.experience_years >= 2 ? 'intermediate' : 'beginner',
      is_active: true,
      is_available_today: true,
      portfolio_urls: application.portfolio_url ? [application.portfolio_url] : []
    }

    const { data: artistProfile, error: profileError } = await adminClient
      .from('artist_profiles')
      .upsert(artistProfileData, { onConflict: 'user_id' })
      .select()
      .single()

    if (profileError) {
      console.error(`[${requestId}] Error creating artist profile:`, profileError)
      // Continue anyway - profile creation can be done manually later
    } else {
      console.log(`[${requestId}] Artist profile created: ${artistProfile.id}`)
    }

    // 3. Generate activation token for account setup
    let activationToken = null
    try {
      console.log(`[${requestId}] Generating activation token`)

      // Generate secure activation token
      const { data: tokenData, error: tokenError } = await adminClient
        .rpc('generate_application_token')

      if (tokenError || !tokenData) {
        console.error(`[${requestId}] Error generating activation token:`, tokenError)
        throw new Error('Failed to generate activation token')
      }

      activationToken = tokenData
      console.log(`[${requestId}] Generated activation token: ${activationToken.substring(0, 8)}...`)

      // Store activation token in database
      const expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + 7) // Token expires in 7 days

      const { error: tokenInsertError } = await adminClient
        .from('application_tokens')
        .insert({
          user_id: application.user_id,
          application_id: application.id,
          token: activationToken,
          token_type: 'account_activation',
          expires_at: expiresAt.toISOString()
        })

      if (tokenInsertError) {
        console.error(`[${requestId}] Error storing activation token:`, tokenInsertError)
        throw new Error('Failed to store activation token')
      }

      console.log(`[${requestId}] Activation token stored successfully`)
    } catch (tokenError) {
      console.error(`[${requestId}] Error in activation token generation:`, tokenError)
      // Continue without token - we'll still send approval email
    }

    // 4. Send approval email with activation link
    try {
      // Get user email from auth.users table
      const { data: userData, error: userError } = await adminClient.auth.admin.getUserById(application.user_id)

      if (userError || !userData.user) {
        console.error(`[${requestId}] Error fetching user data:`, userError)
        return
      }

      const userEmail = userData.user.email
      const userName = userProfile?.name || userEmail

      console.log(`[${requestId}] Sending approval email to: ${userEmail}`)

      const emailTemplate = generateApprovalEmail({
        name: userName,
        email: userEmail,
        role: application.application_type,
        activationToken: activationToken
      })

      const emailResult = await sendNotificationEmail({
        to: userEmail,
        subject: emailTemplate.subject,
        message: emailTemplate.text,
        htmlBody: emailTemplate.htmlBody
      })

      if (emailResult.success) {
        console.log(`[${requestId}] Approval email sent successfully`)

        // Update application to mark approval email as sent
        await adminClient
          .from('artist_braider_applications')
          .update({
            welcome_email_sent: true,
            welcome_email_sent_at: new Date().toISOString(),
            last_email_sent_at: new Date().toISOString()
          })
          .eq('id', application.id)
      } else {
        console.error(`[${requestId}] Failed to send approval email:`, emailResult.error)
      }
    } catch (emailError) {
      console.error(`[${requestId}] Error in approval email process:`, emailError)
    }

    console.log(`[${requestId}] Approval workflow completed`)
  } catch (error) {
    console.error(`[${requestId}] Error in approval workflow:`, error)
    // Don't throw - we want the application status update to succeed even if workflow fails
  }
}
