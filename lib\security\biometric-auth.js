/**
 * Biometric Authentication Manager for Ocean Soul Sparkles
 * Provides WebAuthn-based biometric authentication (fingerprint, face recognition)
 * 
 * Phase 9.1: Enhanced Authentication System
 */

import { createClient } from '@supabase/supabase-js'
import { 
  generateRegistrationOptions,
  verifyRegistrationResponse,
  generateAuthenticationOptions,
  verifyAuthenticationResponse
} from '@simplewebauthn/server'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * Biometric Authentication Manager Class
 * Handles WebAuthn registration and authentication
 */
export class BiometricAuthManager {
  constructor() {
    this.rpName = 'Ocean Soul Sparkles'
    this.rpID = process.env.NEXT_PUBLIC_SITE_URL ? 
      new URL(process.env.NEXT_PUBLIC_SITE_URL).hostname : 
      'localhost'
    this.origin = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
  }

  /**
   * Generate registration options for WebAuthn
   * @param {string} userId - User ID
   * @param {string} userEmail - User email
   * @param {string} userName - User name
   * @returns {Promise<Object>} - Registration options
   */
  async generateRegistrationOptions(userId, userEmail, userName) {
    try {
      // Get existing credentials for this user
      const { data: existingCredentials, error } = await supabase
        .from('webauthn_credentials')
        .select('credential_id')
        .eq('user_id', userId)
        .eq('is_active', true)
      
      if (error) {
        console.error('Error fetching existing credentials:', error)
      }
      
      const excludeCredentials = existingCredentials?.map(cred => ({
        id: Buffer.from(cred.credential_id, 'base64'),
        type: 'public-key'
      })) || []
      
      const options = await generateRegistrationOptions({
        rpName: this.rpName,
        rpID: this.rpID,
        userID: Buffer.from(userId),
        userName: userEmail,
        userDisplayName: userName || userEmail,
        timeout: 60000,
        attestationType: 'none',
        excludeCredentials,
        authenticatorSelection: {
          authenticatorAttachment: 'platform', // Prefer platform authenticators (built-in)
          userVerification: 'preferred',
          residentKey: 'preferred'
        },
        supportedAlgorithmIDs: [-7, -257] // ES256 and RS256
      })
      
      // Store challenge temporarily (in production, use Redis or similar)
      await this.storeChallenge(userId, options.challenge)
      
      return options
    } catch (error) {
      console.error('Error generating registration options:', error)
      throw error
    }
  }

  /**
   * Verify registration response and store credential
   * @param {string} userId - User ID
   * @param {Object} response - WebAuthn registration response
   * @param {string} authenticatorName - User-friendly name for the authenticator
   * @returns {Promise<Object>} - Verification result
   */
  async verifyRegistrationResponse(userId, response, authenticatorName) {
    try {
      // Get stored challenge
      const expectedChallenge = await this.getChallenge(userId)
      if (!expectedChallenge) {
        throw new Error('No challenge found for user')
      }
      
      const verification = await verifyRegistrationResponse({
        response,
        expectedChallenge,
        expectedOrigin: this.origin,
        expectedRPID: this.rpID,
        requireUserVerification: false
      })
      
      if (!verification.verified || !verification.registrationInfo) {
        await this.logBiometricAttempt(userId, null, false, 'Registration verification failed')
        return { success: false, error: 'Registration verification failed' }
      }
      
      const { credentialID, credentialPublicKey, counter, aaguid } = verification.registrationInfo
      
      // Store the credential in the database
      const { data: credential, error } = await supabase
        .from('webauthn_credentials')
        .insert({
          user_id: userId,
          credential_id: Buffer.from(credentialID).toString('base64'),
          public_key: Buffer.from(credentialPublicKey).toString('base64'),
          counter,
          device_type: 'platform', // Assume platform for now
          authenticator_name: authenticatorName || 'Biometric Device',
          aaguid: aaguid ? Buffer.from(aaguid).toString('hex') : null
        })
        .select()
        .single()
      
      if (error) {
        console.error('Error storing credential:', error)
        throw new Error('Failed to store credential')
      }
      
      // Clean up challenge
      await this.removeChallenge(userId)
      
      // Log successful registration
      await this.logBiometricAttempt(userId, credential.credential_id, true, 'Biometric credential registered')
      await this.logSecurityEvent(userId, 'biometric_registered', 'User registered biometric authentication', 'low')
      
      return { 
        success: true, 
        credentialId: credential.id,
        authenticatorName: credential.authenticator_name
      }
    } catch (error) {
      console.error('Error verifying registration response:', error)
      await this.logBiometricAttempt(userId, null, false, `Registration error: ${error.message}`)
      throw error
    }
  }

  /**
   * Generate authentication options for WebAuthn
   * @param {string} userId - User ID (optional, for usernameless flow)
   * @returns {Promise<Object>} - Authentication options
   */
  async generateAuthenticationOptions(userId = null) {
    try {
      let allowCredentials = []
      
      if (userId) {
        // Get user's credentials
        const { data: credentials, error } = await supabase
          .from('webauthn_credentials')
          .select('credential_id')
          .eq('user_id', userId)
          .eq('is_active', true)
        
        if (error) {
          console.error('Error fetching user credentials:', error)
        }
        
        allowCredentials = credentials?.map(cred => ({
          id: Buffer.from(cred.credential_id, 'base64'),
          type: 'public-key'
        })) || []
      }
      
      const options = await generateAuthenticationOptions({
        timeout: 60000,
        allowCredentials: allowCredentials.length > 0 ? allowCredentials : undefined,
        userVerification: 'preferred',
        rpID: this.rpID
      })
      
      // Store challenge temporarily
      if (userId) {
        await this.storeChallenge(userId, options.challenge)
      } else {
        // For usernameless flow, store challenge with a temporary key
        await this.storeChallenge('temp_' + Date.now(), options.challenge)
      }
      
      return options
    } catch (error) {
      console.error('Error generating authentication options:', error)
      throw error
    }
  }

  /**
   * Verify authentication response
   * @param {string} userId - User ID (optional for usernameless flow)
   * @param {Object} response - WebAuthn authentication response
   * @returns {Promise<Object>} - Verification result with user info
   */
  async verifyAuthenticationResponse(userId, response) {
    try {
      const credentialId = Buffer.from(response.id, 'base64url').toString('base64')
      
      // Get credential from database
      const { data: credential, error } = await supabase
        .from('webauthn_credentials')
        .select('*')
        .eq('credential_id', credentialId)
        .eq('is_active', true)
        .single()
      
      if (error || !credential) {
        await this.logBiometricAttempt(userId, credentialId, false, 'Credential not found')
        return { success: false, error: 'Credential not found' }
      }
      
      // If userId not provided, use the one from credential (usernameless flow)
      const actualUserId = userId || credential.user_id
      
      // Get stored challenge
      const expectedChallenge = await this.getChallenge(actualUserId)
      if (!expectedChallenge) {
        await this.logBiometricAttempt(actualUserId, credentialId, false, 'No challenge found')
        return { success: false, error: 'No challenge found' }
      }
      
      const verification = await verifyAuthenticationResponse({
        response,
        expectedChallenge,
        expectedOrigin: this.origin,
        expectedRPID: this.rpID,
        authenticator: {
          credentialID: Buffer.from(credential.credential_id, 'base64'),
          credentialPublicKey: Buffer.from(credential.public_key, 'base64'),
          counter: credential.counter
        },
        requireUserVerification: false
      })
      
      if (!verification.verified) {
        await this.logBiometricAttempt(actualUserId, credentialId, false, 'Authentication verification failed')
        await this.logSecurityEvent(actualUserId, 'biometric_failed', 'Failed biometric authentication attempt', 'medium')
        return { success: false, error: 'Authentication verification failed' }
      }
      
      // Update counter
      await supabase
        .from('webauthn_credentials')
        .update({ 
          counter: verification.authenticationInfo.newCounter,
          last_used_at: new Date().toISOString()
        })
        .eq('credential_id', credentialId)
      
      // Clean up challenge
      await this.removeChallenge(actualUserId)
      
      // Log successful authentication
      await this.logBiometricAttempt(actualUserId, credentialId, true, 'Biometric authentication successful')
      
      return { 
        success: true, 
        userId: actualUserId,
        credentialId: credential.id
      }
    } catch (error) {
      console.error('Error verifying authentication response:', error)
      await this.logBiometricAttempt(userId, null, false, `Authentication error: ${error.message}`)
      throw error
    }
  }

  /**
   * Get user's biometric credentials
   * @param {string} userId - User ID
   * @returns {Promise<Array>} - User's credentials
   */
  async getUserCredentials(userId) {
    try {
      const { data: credentials, error } = await supabase
        .from('webauthn_credentials')
        .select('id, authenticator_name, device_type, created_at, last_used_at')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('created_at', { ascending: false })
      
      if (error) {
        throw error
      }
      
      return credentials || []
    } catch (error) {
      console.error('Error getting user credentials:', error)
      return []
    }
  }

  /**
   * Remove a biometric credential
   * @param {string} userId - User ID
   * @param {string} credentialId - Credential ID
   * @returns {Promise<boolean>} - Success status
   */
  async removeCredential(userId, credentialId) {
    try {
      const { error } = await supabase
        .from('webauthn_credentials')
        .update({ is_active: false })
        .eq('user_id', userId)
        .eq('id', credentialId)
      
      if (error) {
        throw error
      }
      
      // Log security event
      await this.logSecurityEvent(userId, 'biometric_removed', 'User removed biometric credential', 'low')
      
      return true
    } catch (error) {
      console.error('Error removing credential:', error)
      return false
    }
  }

  /**
   * Check if biometric authentication is supported
   * @returns {boolean} - Support status
   */
  isSupported() {
    return typeof window !== 'undefined' && 
           window.PublicKeyCredential && 
           typeof window.PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable === 'function'
  }

  /**
   * Store challenge temporarily (in production, use Redis)
   * @param {string} userId - User ID
   * @param {string} challenge - Challenge string
   */
  async storeChallenge(userId, challenge) {
    // In production, store in Redis with expiration
    // For now, store in memory or database temporarily
    if (typeof window !== 'undefined') {
      sessionStorage.setItem(`webauthn_challenge_${userId}`, challenge)
    }
  }

  /**
   * Get stored challenge
   * @param {string} userId - User ID
   * @returns {string} - Challenge string
   */
  async getChallenge(userId) {
    if (typeof window !== 'undefined') {
      return sessionStorage.getItem(`webauthn_challenge_${userId}`)
    }
    return null
  }

  /**
   * Remove stored challenge
   * @param {string} userId - User ID
   */
  async removeChallenge(userId) {
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem(`webauthn_challenge_${userId}`)
    }
  }

  /**
   * Log biometric authentication attempt
   * @param {string} userId - User ID
   * @param {string} credentialId - Credential ID
   * @param {boolean} success - Success status
   * @param {string} reason - Failure reason or success message
   */
  async logBiometricAttempt(userId, credentialId, success, reason) {
    try {
      await supabase
        .from('biometric_auth_logs')
        .insert({
          user_id: userId,
          credential_id: credentialId,
          success,
          failure_reason: success ? null : reason,
          ip_address: null, // Will be set by API endpoint
          user_agent: null  // Will be set by API endpoint
        })
    } catch (error) {
      console.error('Error logging biometric attempt:', error)
    }
  }

  /**
   * Log security event
   * @param {string} userId - User ID
   * @param {string} eventType - Event type
   * @param {string} description - Event description
   * @param {string} severity - Event severity
   */
  async logSecurityEvent(userId, eventType, description, severity) {
    try {
      await supabase
        .from('security_events')
        .insert({
          user_id: userId,
          event_type: eventType,
          event_description: description,
          severity,
          additional_data: {}
        })
    } catch (error) {
      console.error('Error logging security event:', error)
    }
  }
}

// Export singleton instance
export const biometricAuthManager = new BiometricAuthManager()
export default biometricAuthManager
