/**
 * Terminal Settings Page - Comprehensive Square Terminal device management
 * Provides device pairing, status monitoring, testing, and configuration
 */

import { useState, useEffect } from 'react'
import AdminLayout from '@/components/admin/AdminLayout'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import AuthenticationGuard from '@/components/admin/AuthenticationGuard'
import TerminalDeviceManager from '@/components/admin/pos/TerminalDeviceManager'
import TerminalStatusMonitor from '@/components/admin/pos/TerminalStatusMonitor'
import TerminalTestSuite from '@/components/admin/pos/TerminalTestSuite'
import styles from '@/styles/admin/SettingsPage.module.css'

export default function TerminalSettingsPage() {
  const [activeTab, setActiveTab] = useState('devices')
  const [deviceStatusChanges, setDeviceStatusChanges] = useState([])
  const [squareConfig, setSquareConfig] = useState({
    environment: 'sandbox',
    applicationId: '',
    locationId: '',
    webhookUrl: ''
  })

  useEffect(() => {
    // Load Square configuration from environment or settings
    setSquareConfig({
      environment: process.env.NEXT_PUBLIC_SQUARE_ENVIRONMENT || 'sandbox',
      applicationId: process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID || '',
      locationId: process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID || '',
      webhookUrl: `${window.location.origin}/api/webhooks/square-terminal`
    })
  }, [])

  const handleDeviceStatusChange = (device, oldStatus) => {
    const change = {
      deviceId: device.id,
      deviceName: device.name,
      oldStatus,
      newStatus: device.status,
      timestamp: new Date().toISOString()
    }
    
    setDeviceStatusChanges(prev => [change, ...prev.slice(0, 9)]) // Keep last 10 changes
    
    // Show notification for important status changes
    if (device.status === 'PAIRED' && oldStatus === 'UNPAIRED') {
      showNotification(`Device "${device.name}" successfully paired!`, 'success')
    } else if (device.status === 'UNPAIRED' && oldStatus === 'PAIRED') {
      showNotification(`Device "${device.name}" disconnected`, 'warning')
    }
  }

  const showNotification = (message, type) => {
    // Could integrate with a toast notification system
    console.log(`[${type.toUpperCase()}] ${message}`)
  }

  const tabs = [
    { id: 'devices', label: 'Device Management', icon: '📱' },
    { id: 'monitor', label: 'Status Monitor', icon: '📊' },
    { id: 'testing', label: 'Test Suite', icon: '🧪' },
    { id: 'config', label: 'Configuration', icon: '⚙️' }
  ]

  return (
    <AuthenticationGuard>
      <ProtectedRoute adminOnly>
        <AdminLayout title="Terminal Settings">
          <div className={styles.settingsPage}>
            <div className={styles.header}>
              <h1>Square Terminal Settings</h1>
              <div className={styles.headerInfo}>
                <span className={styles.environmentBadge}>
                  {squareConfig.environment.toUpperCase()}
                </span>
                <span className={styles.locationInfo}>
                  Location: {squareConfig.locationId || 'Not configured'}
                </span>
              </div>
            </div>

            {/* Tab Navigation */}
            <div className={styles.tabNavigation}>
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  className={`${styles.tabButton} ${activeTab === tab.id ? styles.active : ''}`}
                  onClick={() => setActiveTab(tab.id)}
                >
                  <span className={styles.tabIcon}>{tab.icon}</span>
                  <span className={styles.tabLabel}>{tab.label}</span>
                </button>
              ))}
            </div>

            {/* Tab Content */}
            <div className={styles.tabContent}>
              {activeTab === 'devices' && (
                <div className={styles.devicesTab}>
                  <div className={styles.tabHeader}>
                    <h2>Device Management</h2>
                    <p>Create device codes, pair terminals, and manage device status.</p>
                  </div>
                  <TerminalDeviceManager />
                </div>
              )}

              {activeTab === 'monitor' && (
                <div className={styles.monitorTab}>
                  <div className={styles.tabHeader}>
                    <h2>Status Monitor</h2>
                    <p>Real-time monitoring of terminal device status and connectivity.</p>
                  </div>
                  
                  <div className={styles.monitorGrid}>
                    <div className={styles.statusMonitorSection}>
                      <TerminalStatusMonitor 
                        onDeviceStatusChange={handleDeviceStatusChange}
                        autoRefresh={true}
                        refreshInterval={30000}
                      />
                    </div>
                    
                    <div className={styles.statusChangesSection}>
                      <h3>Recent Status Changes</h3>
                      {deviceStatusChanges.length === 0 ? (
                        <div className={styles.noChanges}>
                          <p>No recent status changes</p>
                        </div>
                      ) : (
                        <div className={styles.changesList}>
                          {deviceStatusChanges.map((change, index) => (
                            <div key={index} className={styles.statusChange}>
                              <div className={styles.changeHeader}>
                                <span className={styles.deviceName}>{change.deviceName}</span>
                                <span className={styles.changeTime}>
                                  {new Date(change.timestamp).toLocaleTimeString()}
                                </span>
                              </div>
                              <div className={styles.changeDetails}>
                                <span className={styles.statusTransition}>
                                  {change.oldStatus} → {change.newStatus}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'testing' && (
                <div className={styles.testingTab}>
                  <div className={styles.tabHeader}>
                    <h2>Test Suite</h2>
                    <p>Comprehensive testing for Terminal API integration and functionality.</p>
                  </div>
                  <TerminalTestSuite />
                </div>
              )}

              {activeTab === 'config' && (
                <div className={styles.configTab}>
                  <div className={styles.tabHeader}>
                    <h2>Configuration</h2>
                    <p>Square API configuration and webhook settings.</p>
                  </div>
                  
                  <div className={styles.configSections}>
                    <div className={styles.configSection}>
                      <h3>Square API Configuration</h3>
                      <div className={styles.configGrid}>
                        <div className={styles.configItem}>
                          <label>Environment:</label>
                          <div className={styles.configValue}>
                            <span className={`${styles.environmentTag} ${styles[squareConfig.environment]}`}>
                              {squareConfig.environment.toUpperCase()}
                            </span>
                          </div>
                        </div>
                        
                        <div className={styles.configItem}>
                          <label>Application ID:</label>
                          <div className={styles.configValue}>
                            {squareConfig.applicationId ? (
                              <code>{squareConfig.applicationId}</code>
                            ) : (
                              <span className={styles.notConfigured}>Not configured</span>
                            )}
                          </div>
                        </div>
                        
                        <div className={styles.configItem}>
                          <label>Location ID:</label>
                          <div className={styles.configValue}>
                            {squareConfig.locationId ? (
                              <code>{squareConfig.locationId}</code>
                            ) : (
                              <span className={styles.notConfigured}>Not configured</span>
                            )}
                          </div>
                        </div>
                        
                        <div className={styles.configItem}>
                          <label>Webhook URL:</label>
                          <div className={styles.configValue}>
                            <code>{squareConfig.webhookUrl}</code>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className={styles.configSection}>
                      <h3>Security & Compliance</h3>
                      <div className={styles.securityInfo}>
                        <div className={styles.securityItem}>
                          <span className={styles.securityIcon}>🔒</span>
                          <div className={styles.securityDetails}>
                            <h4>PCI DSS Compliance</h4>
                            <p>Hardware-based card processing ensures PCI compliance</p>
                          </div>
                        </div>
                        
                        <div className={styles.securityItem}>
                          <span className={styles.securityIcon}>🔐</span>
                          <div className={styles.securityDetails}>
                            <h4>Webhook Security</h4>
                            <p>Signature verification protects webhook endpoints</p>
                          </div>
                        </div>
                        
                        <div className={styles.securityItem}>
                          <span className={styles.securityIcon}>🛡️</span>
                          <div className={styles.securityDetails}>
                            <h4>Data Protection</h4>
                            <p>No sensitive card data stored locally</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className={styles.configSection}>
                      <h3>Integration Status</h3>
                      <div className={styles.integrationStatus}>
                        <div className={styles.statusItem}>
                          <span className={styles.statusIcon}>✅</span>
                          <span>Square Terminal API</span>
                        </div>
                        <div className={styles.statusItem}>
                          <span className={styles.statusIcon}>✅</span>
                          <span>Device Management</span>
                        </div>
                        <div className={styles.statusItem}>
                          <span className={styles.statusIcon}>✅</span>
                          <span>Checkout Processing</span>
                        </div>
                        <div className={styles.statusItem}>
                          <span className={styles.statusIcon}>✅</span>
                          <span>Webhook Handling</span>
                        </div>
                        <div className={styles.statusItem}>
                          <span className={styles.statusIcon}>✅</span>
                          <span>Real-time Updates</span>
                        </div>
                        <div className={styles.statusItem}>
                          <span className={styles.statusIcon}>⏳</span>
                          <span>Square Reader Integration (Phase 2)</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    </AuthenticationGuard>
  )
}
