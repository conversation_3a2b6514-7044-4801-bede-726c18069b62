/**
 * Social Media Profiles API Endpoint for Ocean Soul Sparkles
 * Handles social media profile information retrieval
 * 
 * Phase 7.3: Social Media Integration Layer
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { integrationRateLimit } from '@/lib/integrations/rate-limiter'
import { RequestValidator, AuditLogger } from '@/lib/integrations/security-utils'
import SocialMediaManager from '@/lib/integrations/social/social-manager'

/**
 * Social Media Profiles Handler
 * GET /api/integrations/social/profiles - Get profiles from connected social media accounts
 */
export default async function handler(req, res) {
  // Apply rate limiting
  await new Promise((resolve, reject) => {
    integrationRateLimit(req, res, (error) => {
      if (error) reject(error)
      else resolve()
    })
  })

  const startTime = Date.now()

  try {
    // Validate request method
    RequestValidator.validateEndpointAccess(req, ['GET'])

    // Authenticate user
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    const { user } = authResult
    const userId = user.id

    // Initialize social media manager
    const socialManager = new SocialMediaManager(userId)

    // Get profiles from all connected providers
    const profiles = await socialManager.getAllProfiles()

    await AuditLogger.logApiAccess(
      userId,
      req.url,
      req.method,
      200,
      Date.now() - startTime,
      { 
        action: 'get_social_profiles',
        profileCount: profiles.length
      }
    )

    return res.status(200).json({
      success: true,
      profiles
    })

  } catch (error) {
    console.error('Social media profiles error:', error)

    await AuditLogger.logApiAccess(
      req.user?.id || null,
      req.url,
      req.method,
      500,
      Date.now() - startTime,
      { error: error.message }
    )

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get social media profiles'
    })
  }
}

/**
 * API Route Configuration
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
