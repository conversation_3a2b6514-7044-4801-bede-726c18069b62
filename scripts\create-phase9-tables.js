/**
 * Create Phase 9.1 Enhanced Authentication Tables
 * Ocean Soul Sparkles - Security & Compliance Enhancement
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'
import path from 'path'
import { fileURLToPath } from 'url'

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env.local') })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

async function createTables() {
  console.log('🚀 Creating Phase 9.1 Enhanced Authentication Tables...')
  
  const tables = [
    {
      name: 'user_mfa_settings',
      sql: `
        CREATE TABLE IF NOT EXISTS public.user_mfa_settings (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
          mfa_enabled BOOLEAN DEFAULT FALSE,
          totp_secret TEXT,
          backup_codes TEXT[],
          sms_phone TEXT,
          recovery_email TEXT,
          mfa_enforced BOOLEAN DEFAULT FALSE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(user_id)
        );
      `
    },
    {
      name: 'mfa_verification_logs',
      sql: `
        CREATE TABLE IF NOT EXISTS public.mfa_verification_logs (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
          verification_type VARCHAR(20) NOT NULL CHECK (verification_type IN ('totp', 'sms', 'backup_code', 'recovery')),
          success BOOLEAN NOT NULL,
          ip_address INET,
          user_agent TEXT,
          failure_reason TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: 'webauthn_credentials',
      sql: `
        CREATE TABLE IF NOT EXISTS public.webauthn_credentials (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
          credential_id TEXT NOT NULL UNIQUE,
          public_key TEXT NOT NULL,
          counter BIGINT DEFAULT 0,
          device_type VARCHAR(50),
          authenticator_name TEXT,
          aaguid TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          last_used_at TIMESTAMP WITH TIME ZONE,
          is_active BOOLEAN DEFAULT TRUE
        );
      `
    },
    {
      name: 'biometric_auth_logs',
      sql: `
        CREATE TABLE IF NOT EXISTS public.biometric_auth_logs (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
          credential_id TEXT,
          success BOOLEAN NOT NULL,
          ip_address INET,
          user_agent TEXT,
          failure_reason TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: 'user_sessions',
      sql: `
        CREATE TABLE IF NOT EXISTS public.user_sessions (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
          session_token TEXT NOT NULL UNIQUE,
          refresh_token TEXT UNIQUE,
          device_fingerprint TEXT,
          ip_address INET,
          user_agent TEXT,
          location_country TEXT,
          location_city TEXT,
          is_active BOOLEAN DEFAULT TRUE,
          last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: 'device_fingerprints',
      sql: `
        CREATE TABLE IF NOT EXISTS public.device_fingerprints (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
          fingerprint_hash TEXT NOT NULL,
          device_info JSONB DEFAULT '{}',
          is_trusted BOOLEAN DEFAULT FALSE,
          first_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          login_count INTEGER DEFAULT 1,
          UNIQUE(user_id, fingerprint_hash)
        );
      `
    },
    {
      name: 'security_events',
      sql: `
        CREATE TABLE IF NOT EXISTS public.security_events (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          event_type VARCHAR(50) NOT NULL,
          event_description TEXT,
          severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
          ip_address INET,
          user_agent TEXT,
          device_fingerprint TEXT,
          additional_data JSONB DEFAULT '{}',
          resolved BOOLEAN DEFAULT FALSE,
          resolved_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          resolved_at TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: 'social_login_accounts',
      sql: `
        CREATE TABLE IF NOT EXISTS public.social_login_accounts (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
          provider VARCHAR(50) NOT NULL,
          provider_user_id TEXT NOT NULL,
          provider_email TEXT,
          provider_name TEXT,
          profile_picture_url TEXT,
          account_linked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          last_login_at TIMESTAMP WITH TIME ZONE,
          is_active BOOLEAN DEFAULT TRUE,
          UNIQUE(provider, provider_user_id)
        );
      `
    }
  ]

  let successCount = 0
  let errorCount = 0

  for (const table of tables) {
    try {
      console.log(`📋 Creating table: ${table.name}`)
      
      const { error } = await supabase.rpc('execute_sql', {
        sql: table.sql
      })
      
      if (error) {
        console.error(`❌ Error creating ${table.name}:`, error.message)
        errorCount++
      } else {
        console.log(`✅ Table ${table.name} created successfully`)
        successCount++
      }
    } catch (err) {
      console.error(`❌ Exception creating ${table.name}:`, err.message)
      errorCount++
    }
  }

  console.log('\n📊 Table Creation Summary:')
  console.log(`✅ Successful tables: ${successCount}`)
  console.log(`❌ Failed tables: ${errorCount}`)

  if (errorCount === 0) {
    console.log('🎉 All Phase 9.1 tables created successfully!')
  } else {
    console.log('⚠️  Some tables failed to create. Please review the logs.')
  }

  // Verify tables
  console.log('\n🔍 Verifying tables...')
  await verifyTables()
}

async function verifyTables() {
  const tableNames = [
    'user_mfa_settings',
    'mfa_verification_logs', 
    'webauthn_credentials',
    'biometric_auth_logs',
    'user_sessions',
    'device_fingerprints',
    'security_events',
    'social_login_accounts'
  ]

  for (const tableName of tableNames) {
    try {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1)
      
      if (error) {
        console.log(`❌ Table ${tableName}: ${error.message}`)
      } else {
        console.log(`✅ Table ${tableName}: Verified successfully`)
      }
    } catch (err) {
      console.log(`❌ Table ${tableName}: ${err.message}`)
    }
  }
}

// Run the table creation
createTables()
