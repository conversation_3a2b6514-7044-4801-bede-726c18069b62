/* Token Management Modal Styles */

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.header {
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.header p {
  margin: 0.25rem 0 0 0;
  font-size: 0.875rem;
  opacity: 0.9;
}

.closeButton {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.1);
}

.content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  gap: 1rem;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading p {
  color: #4a5568;
  font-size: 1rem;
}

.emptyState {
  text-align: center;
  padding: 3rem;
  color: #4a5568;
}

.emptyState svg {
  margin-bottom: 1rem;
  color: #cbd5e0;
}

.emptyState h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a202c;
}

.emptyState p {
  margin: 0;
  font-size: 0.875rem;
}

.tokensList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.tokenCard {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
  transition: box-shadow 0.2s ease;
}

.tokenCard:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tokenHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.tokenInfo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.tokenId {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.label {
  font-size: 0.75rem;
  font-weight: 600;
  color: #4a5568;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.tokenValue {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  background: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  color: #1a202c;
}

.copyButton {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.copyButton:hover {
  background: #667eea;
  color: white;
}

.statusBadge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.statusActive {
  background: #c6f6d5;
  color: #22543d;
}

.statusExpired {
  background: #fed7d7;
  color: #c53030;
}

.statusUsed {
  background: #e2e8f0;
  color: #4a5568;
}

.statusDefault {
  background: #f7fafc;
  color: #4a5568;
}

.tokenActions {
  display: flex;
  gap: 0.5rem;
}

.actionButton {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  color: #4a5568;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.actionButton:hover:not(:disabled) {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.actionButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.dangerButton {
  color: #c53030;
  border-color: #feb2b2;
}

.dangerButton:hover:not(:disabled) {
  background: #c53030;
  color: white;
  border-color: #c53030;
}

.miniSpinner {
  width: 12px;
  height: 12px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.tokenDetails {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.detailRow {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detailRow .label {
  font-size: 0.75rem;
  font-weight: 600;
  color: #4a5568;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.detailRow .value {
  font-size: 0.875rem;
  color: #1a202c;
  font-weight: 500;
}

.footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e2e8f0;
  background: #f7fafc;
  display: flex;
  justify-content: flex-end;
}

.closeFooterButton {
  padding: 0.5rem 1rem;
  background: #4a5568;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.closeFooterButton:hover {
  background: #2d3748;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }

  .header {
    padding: 1rem;
  }

  .content {
    padding: 1rem;
  }

  .tokenHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .tokenActions {
    width: 100%;
    justify-content: flex-end;
  }

  .tokenDetails {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .overlay {
    padding: 0.5rem;
  }

  .tokenInfo {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .tokenActions {
    flex-direction: column;
    width: 100%;
  }

  .actionButton {
    justify-content: center;
  }
}
