/* Ocean Soul Sparkles Admin Dashboard - Responsive Utilities */
/* Shared responsive patterns for consistent mobile experience */

/* ===== VIEWPORT CONSTRAINTS ===== */
.viewportConstrained {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* ===== RESPONSIVE CONTAINERS ===== */
.responsiveContainer {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
}

@media (max-width: 768px) {
  .responsiveContainer {
    padding: 15px;
  }
}

@media (max-width: 428px) {
  .responsiveContainer {
    padding: 12px;
  }
}

@media (max-width: 375px) {
  .responsiveContainer {
    padding: 10px;
  }
}

@media (max-width: 320px) {
  .responsiveContainer {
    padding: 8px;
  }
}

/* ===== RESPONSIVE TABLES ===== */
.responsiveTableContainer {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.responsiveTable {
  width: 100%;
  border-collapse: collapse;
  min-width: 600px; /* Minimum width for horizontal scroll */
}

.responsiveTable th,
.responsiveTable td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.responsiveTable th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #495057;
  white-space: nowrap;
}

@media (max-width: 768px) {
  .responsiveTable {
    min-width: 500px;
  }
  
  .responsiveTable th,
  .responsiveTable td {
    padding: 10px 12px;
    font-size: 0.9rem;
  }
}

@media (max-width: 428px) {
  .responsiveTable {
    min-width: 400px;
  }
  
  .responsiveTable th,
  .responsiveTable td {
    padding: 8px 6px;
    font-size: 0.85rem;
  }
}

@media (max-width: 375px) {
  .responsiveTable {
    min-width: 350px;
  }
  
  .responsiveTable th,
  .responsiveTable td {
    padding: 6px 4px;
    font-size: 0.8rem;
  }
}

@media (max-width: 320px) {
  .responsiveTable {
    min-width: 300px;
  }
  
  .responsiveTable th,
  .responsiveTable td {
    padding: 4px 3px;
    font-size: 0.75rem;
  }
}

/* ===== RESPONSIVE GRIDS ===== */
.responsiveGrid {
  display: grid;
  gap: 1rem;
  width: 100%;
}

.responsiveGrid.twoColumn {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.responsiveGrid.threeColumn {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.responsiveGrid.fourColumn {
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
}

@media (max-width: 768px) {
  .responsiveGrid.twoColumn {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .responsiveGrid.threeColumn {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .responsiveGrid.fourColumn {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 428px) {
  .responsiveGrid.twoColumn,
  .responsiveGrid.threeColumn,
  .responsiveGrid.fourColumn {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

/* ===== RESPONSIVE FORMS ===== */
.responsiveForm {
  width: 100%;
}

.responsiveFormRow {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.responsiveFormGroup {
  flex: 1;
  min-width: 0;
}

.responsiveFormGroup label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.responsiveFormGroup input,
.responsiveFormGroup select,
.responsiveFormGroup textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px; /* Prevent zoom on iOS */
  min-height: 44px; /* Touch target size */
  box-sizing: border-box;
}

@media (max-width: 768px) {
  .responsiveFormRow {
    flex-direction: column;
    gap: 0.75rem;
  }
}

/* ===== RESPONSIVE BUTTONS ===== */
.responsiveButton {
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px; /* Touch target size */
  min-width: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.responsiveButton.primary {
  background-color: #6a0dad;
  color: white;
}

.responsiveButton.primary:hover {
  background-color: #5a0c8f;
}

.responsiveButton.secondary {
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
}

.responsiveButton.secondary:hover {
  background-color: #e9ecef;
}

.responsiveButtonGroup {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

@media (max-width: 428px) {
  .responsiveButtonGroup {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .responsiveButton {
    width: 100%;
  }
}

/* ===== RESPONSIVE HEADERS ===== */
.responsiveHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

.responsiveHeader h1,
.responsiveHeader h2 {
  margin: 0;
  color: #333;
}

.responsiveHeaderActions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

@media (max-width: 768px) {
  .responsiveHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .responsiveHeaderActions {
    width: 100%;
    justify-content: stretch;
  }
}

@media (max-width: 428px) {
  .responsiveHeader h1 {
    font-size: 1.5rem;
  }
  
  .responsiveHeader h2 {
    font-size: 1.4rem;
  }
  
  .responsiveHeaderActions {
    flex-direction: column;
  }
}

/* ===== RESPONSIVE CARDS ===== */
.responsiveCard {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  width: 100%;
  box-sizing: border-box;
}

@media (max-width: 768px) {
  .responsiveCard {
    padding: 1rem;
  }
}

@media (max-width: 428px) {
  .responsiveCard {
    padding: 0.75rem;
  }
}

@media (max-width: 375px) {
  .responsiveCard {
    padding: 0.5rem;
  }
}

/* ===== RESPONSIVE FILTERS ===== */
.responsiveFilters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.responsiveFilterItem {
  display: flex;
  flex-direction: column;
  min-width: 150px;
}

.responsiveFilterItem label {
  font-size: 0.8rem;
  margin-bottom: 4px;
  color: #666;
}

.responsiveFilterItem select,
.responsiveFilterItem input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 16px;
  min-height: 44px;
}

@media (max-width: 768px) {
  .responsiveFilters {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .responsiveFilterItem {
    min-width: auto;
  }
}

/* ===== RESPONSIVE PAGINATION ===== */
.responsivePagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.responsivePaginationButton {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.responsivePaginationButton:hover:not(:disabled) {
  background-color: #6a0dad;
  color: white;
}

.responsivePaginationButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.responsivePaginationInfo {
  margin: 0 10px;
  font-size: 0.9rem;
  color: #666;
}

@media (max-width: 428px) {
  .responsivePagination {
    flex-direction: column;
    gap: 15px;
  }
  
  .responsivePaginationInfo {
    order: -1;
    margin: 0;
  }
}
