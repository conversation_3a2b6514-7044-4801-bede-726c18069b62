import { authenticateAdminRequest } from '@/lib/admin-auth'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * API endpoint for synchronizing Artist/Braider profiles with booking system
 * Ensures profile data consistency between authentication and booking systems
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] Artist Profile Sync API called: ${req.method}`)

  try {
    // Authenticate admin request
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.authorized) {
      return res.status(401).json({ error: 'Unauthorized' })
    }

    if (req.method === 'POST') {
      return await syncAllProfiles(req, res, requestId)
    } else if (req.method === 'PUT') {
      return await syncSingleProfile(req, res, requestId)
    } else {
      return res.status(405).json({ error: 'Method not allowed' })
    }

  } catch (error) {
    console.error(`[${requestId}] Error in artist profile sync:`, error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}

/**
 * Sync all Artist/Braider profiles with booking system
 */
async function syncAllProfiles(req, res, requestId) {
  console.log(`[${requestId}] Starting full profile synchronization`)

  const syncResults = {
    processed: 0,
    created: 0,
    updated: 0,
    errors: []
  }

  try {
    // Get all users with artist/braider roles
    const { data: artistUsers, error: usersError } = await supabase
      .from('user_roles')
      .select('id, role')
      .in('role', ['artist', 'braider'])

    if (usersError) {
      throw usersError
    }

    console.log(`[${requestId}] Found ${artistUsers.length} artist/braider users`)

    // Process each user
    for (const user of artistUsers) {
      try {
        syncResults.processed++
        
        // Check if artist profile exists
        const { data: existingProfile, error: profileError } = await supabase
          .from('artist_profiles')
          .select('*')
          .eq('user_id', user.id)
          .single()

        if (profileError && profileError.code !== 'PGRST116') {
          throw profileError
        }

        // Get user data from auth.users
        const { data: userData, error: userDataError } = await supabase.auth.admin.getUserById(user.id)

        if (userDataError) {
          console.error(`[${requestId}] Error fetching user data for ${user.id}:`, userDataError)
          syncResults.errors.push({
            userId: user.id,
            error: `Failed to fetch user data: ${userDataError.message}`
          })
          continue
        }

        const userName = userData.user?.user_metadata?.name || userData.user?.email || 'Unknown Artist'

        if (!existingProfile) {
          // Create new artist profile
          const newProfile = {
            user_id: user.id,
            artist_name: userName,
            display_name: userName,
            bio: `${user.role === 'artist' ? 'Beauty Artist' : 'Hair Braiding Specialist'} at Ocean Soul Sparkles`,
            specializations: user.role === 'artist' ? ['painting', 'glitter'] : ['braiding', 'hair'],
            skill_level: 'intermediate',
            is_active: true,
            is_available_today: true,
            booking_buffer_time: 15,
            max_daily_bookings: 6,
            accepts_walk_ins: true,
            accepts_online_bookings: true,
            minimum_advance_booking: 120,
            maximum_advance_booking: 20160,
            auto_confirm_bookings: false
          }

          const { error: createError } = await supabase
            .from('artist_profiles')
            .insert([newProfile])

          if (createError) {
            throw createError
          }

          syncResults.created++
          console.log(`[${requestId}] Created profile for user: ${userData.user?.email}`)

        } else {
          // Update existing profile with booking integration fields
          const updates = {
            artist_name: userName,
            booking_buffer_time: existingProfile.booking_buffer_time || 15,
            max_daily_bookings: existingProfile.max_daily_bookings || 6,
            accepts_walk_ins: existingProfile.accepts_walk_ins ?? true,
            accepts_online_bookings: existingProfile.accepts_online_bookings ?? true,
            minimum_advance_booking: existingProfile.minimum_advance_booking || 120,
            maximum_advance_booking: existingProfile.maximum_advance_booking || 20160,
            auto_confirm_bookings: existingProfile.auto_confirm_bookings ?? false,
            updated_at: new Date().toISOString()
          }

          const { error: updateError } = await supabase
            .from('artist_profiles')
            .update(updates)
            .eq('id', existingProfile.id)

          if (updateError) {
            throw updateError
          }

          syncResults.updated++
          console.log(`[${requestId}] Updated profile for user: ${userData.user?.email}`)
        }

      } catch (userError) {
        console.error(`[${requestId}] Error processing user ${user.id}:`, userError)
        syncResults.errors.push({
          userId: user.id,
          error: userError.message
        })
      }
    }

    console.log(`[${requestId}] Profile sync completed:`, syncResults)

    return res.status(200).json({
      success: true,
      message: 'Profile synchronization completed',
      results: syncResults
    })

  } catch (error) {
    console.error(`[${requestId}] Error in full profile sync:`, error)
    return res.status(500).json({
      error: 'Failed to sync profiles',
      results: syncResults
    })
  }
}

/**
 * Sync a single artist profile
 */
async function syncSingleProfile(req, res, requestId) {
  const { userId } = req.body

  if (!userId) {
    return res.status(400).json({ error: 'User ID is required' })
  }

  console.log(`[${requestId}] Syncing single profile for user: ${userId}`)

  try {
    // Get user role
    const { data: userRole, error: roleError } = await supabase
      .from('user_roles')
      .select('id, role')
      .eq('id', userId)
      .in('role', ['artist', 'braider'])
      .single()

    if (roleError) {
      if (roleError.code === 'PGRST116') {
        return res.status(404).json({ error: 'User not found or not an artist/braider' })
      }
      throw roleError
    }

    // Check existing profile
    const { data: existingProfile, error: profileError } = await supabase
      .from('artist_profiles')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (profileError && profileError.code !== 'PGRST116') {
      throw profileError
    }

    // Get user data from auth.users
    const { data: userData, error: userDataError } = await supabase.auth.admin.getUserById(userId)

    if (userDataError) {
      return res.status(500).json({ error: 'Failed to fetch user data' })
    }

    const userName = userData.user?.user_metadata?.name || userData.user?.email || 'Unknown Artist'

    let result = {}

    if (!existingProfile) {
      // Create new profile
      const newProfile = {
        user_id: userId,
        artist_name: userName,
        display_name: userName,
        bio: `${userRole.role === 'artist' ? 'Beauty Artist' : 'Hair Braiding Specialist'} at Ocean Soul Sparkles`,
        specializations: userRole.role === 'artist' ? ['painting', 'glitter'] : ['braiding', 'hair'],
        skill_level: 'intermediate',
        is_active: true,
        is_available_today: true,
        booking_buffer_time: 15,
        max_daily_bookings: 6,
        accepts_walk_ins: true,
        accepts_online_bookings: true,
        minimum_advance_booking: 120,
        maximum_advance_booking: 20160,
        auto_confirm_bookings: false
      }

      const { data: createdProfile, error: createError } = await supabase
        .from('artist_profiles')
        .insert([newProfile])
        .select()
        .single()

      if (createError) {
        throw createError
      }

      result = { action: 'created', profile: createdProfile }

    } else {
      // Update existing profile
      const updates = {
        artist_name: userName,
        updated_at: new Date().toISOString()
      }

      const { data: updatedProfile, error: updateError } = await supabase
        .from('artist_profiles')
        .update(updates)
        .eq('id', existingProfile.id)
        .select()
        .single()

      if (updateError) {
        throw updateError
      }

      result = { action: 'updated', profile: updatedProfile }
    }

    console.log(`[${requestId}] Successfully ${result.action} profile for user: ${userData.user?.email}`)

    return res.status(200).json({
      success: true,
      message: `Profile ${result.action} successfully`,
      result
    })

  } catch (error) {
    console.error(`[${requestId}] Error syncing single profile:`, error)
    return res.status(500).json({ error: 'Failed to sync profile' })
  }
}
