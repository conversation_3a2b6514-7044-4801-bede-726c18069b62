-- Ocean Soul Sparkles Post-Migration Validation Script
-- Run this script after executing the service consolidation migration
-- This will verify the migration was successful and all systems are working correctly

-- ============================================================================
-- PARENT SERVICES VALIDATION
-- ============================================================================

-- 1. Verify all expected parent services were created
SELECT 
  'PARENT SERVICES CREATED' as validation_type,
  name,
  category,
  duration,
  price,
  status,
  visible_on_public,
  visible_on_pos,
  visible_on_events
FROM services 
WHERE name IN (
  'Body Painting',
  'Face Painting', 
  'Airbrush Face & Body Painting',
  'Glitter & Gem Application',
  'Hair Braiding & Styling'
)
ORDER BY name;

-- 2. Count of parent services vs expected
SELECT 
  'PARENT SERVICE COUNT CHECK' as validation_type,
  COUNT(*) as actual_parent_services,
  5 as expected_parent_services,
  CASE 
    WHEN COUNT(*) = 5 THEN '✅ Correct count'
    ELSE '❌ Incorrect count'
  END as status
FROM services 
WHERE name IN (
  'Body Painting',
  'Face Painting', 
  'Airbrush Face & Body Painting',
  'Glitter & Gem Application',
  'Hair Braiding & Styling'
);

-- ============================================================================
-- PRICING TIERS VALIDATION
-- ============================================================================

-- 3. Verify pricing tiers were created for each parent service
SELECT 
  'PRICING TIERS BY PARENT' as validation_type,
  s.name as parent_service,
  COUNT(spt.id) as tier_count,
  COUNT(CASE WHEN spt.is_default = true THEN 1 END) as default_tiers
FROM services s
LEFT JOIN service_pricing_tiers spt ON s.id = spt.service_id
WHERE s.name IN (
  'Body Painting',
  'Face Painting', 
  'Airbrush Face & Body Painting',
  'Glitter & Gem Application',
  'Hair Braiding & Styling'
)
GROUP BY s.id, s.name
ORDER BY s.name;

-- 4. Check that each parent service has exactly one default tier
SELECT 
  'DEFAULT TIER VALIDATION' as validation_type,
  s.name as parent_service,
  COUNT(CASE WHEN spt.is_default = true THEN 1 END) as default_count,
  CASE 
    WHEN COUNT(CASE WHEN spt.is_default = true THEN 1 END) = 1 THEN '✅ One default tier'
    WHEN COUNT(CASE WHEN spt.is_default = true THEN 1 END) = 0 THEN '❌ No default tier'
    ELSE '❌ Multiple default tiers'
  END as status
FROM services s
LEFT JOIN service_pricing_tiers spt ON s.id = spt.service_id
WHERE s.name IN (
  'Body Painting',
  'Face Painting', 
  'Airbrush Face & Body Painting',
  'Glitter & Gem Application',
  'Hair Braiding & Styling'
)
GROUP BY s.id, s.name
ORDER BY s.name;

-- 5. Detailed pricing tier information
SELECT 
  'PRICING TIER DETAILS' as validation_type,
  s.name as parent_service,
  spt.name as tier_name,
  spt.duration,
  spt.price,
  spt.is_default,
  spt.sort_order
FROM services s
JOIN service_pricing_tiers spt ON s.id = spt.service_id
WHERE s.name IN (
  'Body Painting',
  'Face Painting', 
  'Airbrush Face & Body Painting',
  'Glitter & Gem Application',
  'Hair Braiding & Styling'
)
ORDER BY s.name, spt.sort_order, spt.name;

-- ============================================================================
-- OLD SERVICES STATUS
-- ============================================================================

-- 6. Check status of old services (should be archived or deleted)
SELECT 
  'OLD SERVICES STATUS' as validation_type,
  status,
  COUNT(*) as service_count
FROM services
WHERE id IN (SELECT old_service_id FROM service_consolidation_mapping)
GROUP BY status;

-- 7. Verify mapping table exists and has correct data
SELECT 
  'MAPPING TABLE VALIDATION' as validation_type,
  COUNT(*) as total_mappings,
  COUNT(DISTINCT old_service_id) as unique_old_services,
  COUNT(DISTINCT new_parent_service_id) as unique_parent_services
FROM service_consolidation_mapping;

-- ============================================================================
-- BOOKING INTEGRITY VALIDATION
-- ============================================================================

-- 8. Check if any bookings reference old service IDs
SELECT 
  'BOOKING REFERENCE CHECK' as validation_type,
  COUNT(*) as bookings_with_old_service_ids
FROM bookings b
WHERE b.service_id IN (SELECT old_service_id FROM service_consolidation_mapping);

-- 9. Verify bookings now reference parent services
SELECT 
  'BOOKING PARENT SERVICE CHECK' as validation_type,
  s.name as parent_service,
  COUNT(b.id) as booking_count
FROM bookings b
JOIN services s ON b.service_id = s.id
WHERE s.name IN (
  'Body Painting',
  'Face Painting', 
  'Airbrush Face & Body Painting',
  'Glitter & Gem Application',
  'Hair Braiding & Styling'
)
GROUP BY s.id, s.name
ORDER BY booking_count DESC;

-- ============================================================================
-- ADMIN INTERFACE VALIDATION
-- ============================================================================

-- 10. Services that should pass admin validation (have pricing tiers)
SELECT 
  'ADMIN VALIDATION CHECK' as validation_type,
  s.name,
  CASE 
    WHEN COUNT(spt.id) > 0 THEN '✅ Has pricing tiers'
    ELSE '❌ No pricing tiers'
  END as admin_validation_status
FROM services s
LEFT JOIN service_pricing_tiers spt ON s.id = spt.service_id
WHERE s.status = 'active'
GROUP BY s.id, s.name
HAVING COUNT(spt.id) = 0  -- Show only services without pricing tiers
ORDER BY s.name;

-- 11. Count of services that will pass/fail admin validation
SELECT 
  'ADMIN VALIDATION SUMMARY' as validation_type,
  COUNT(CASE WHEN tier_count > 0 THEN 1 END) as services_with_tiers,
  COUNT(CASE WHEN tier_count = 0 THEN 1 END) as services_without_tiers,
  COUNT(*) as total_active_services
FROM (
  SELECT 
    s.id,
    COUNT(spt.id) as tier_count
  FROM services s
  LEFT JOIN service_pricing_tiers spt ON s.id = spt.service_id
  WHERE s.status = 'active'
  GROUP BY s.id
) tier_counts;

-- ============================================================================
-- POS TERMINAL VALIDATION
-- ============================================================================

-- 12. Services visible on POS with their tier counts
SELECT 
  'POS VISIBILITY CHECK' as validation_type,
  s.name,
  s.category,
  COUNT(spt.id) as tier_count,
  s.visible_on_pos
FROM services s
LEFT JOIN service_pricing_tiers spt ON s.id = spt.service_id
WHERE s.visible_on_pos = true AND s.status = 'active'
GROUP BY s.id, s.name, s.category, s.visible_on_pos
ORDER BY s.category, s.name;

-- 13. Category distribution for POS services
SELECT 
  'POS CATEGORY DISTRIBUTION' as validation_type,
  COALESCE(category, 'No Category') as category,
  COUNT(*) as service_count
FROM services
WHERE visible_on_pos = true AND status = 'active'
GROUP BY category
ORDER BY service_count DESC;

-- ============================================================================
-- PUBLIC BOOKING VALIDATION
-- ============================================================================

-- 14. Services visible on public booking with duration check
SELECT 
  'PUBLIC BOOKING VALIDATION' as validation_type,
  s.name,
  s.duration,
  CASE 
    WHEN s.duration BETWEEN 120 AND 360 THEN '✅ Valid duration (2-6 hours)'
    ELSE '❌ Invalid duration for public booking'
  END as duration_status,
  s.visible_on_public
FROM services s
WHERE s.visible_on_public = true AND s.status = 'active'
ORDER BY s.duration;

-- ============================================================================
-- DATA INTEGRITY CHECKS
-- ============================================================================

-- 15. Check for orphaned pricing tiers
SELECT 
  'ORPHANED PRICING TIERS' as validation_type,
  spt.id,
  spt.service_id,
  spt.name
FROM service_pricing_tiers spt
LEFT JOIN services s ON spt.service_id = s.id
WHERE s.id IS NULL;

-- 16. Check for services with invalid pricing tier references
SELECT 
  'INVALID TIER REFERENCES' as validation_type,
  s.name,
  COUNT(spt.id) as tier_count
FROM services s
LEFT JOIN service_pricing_tiers spt ON s.id = spt.service_id
WHERE s.status = 'active'
  AND s.name IN (
    'Body Painting',
    'Face Painting', 
    'Airbrush Face & Body Painting',
    'Glitter & Gem Application',
    'Hair Braiding & Styling'
  )
GROUP BY s.id, s.name
HAVING COUNT(spt.id) = 0;

-- ============================================================================
-- MIGRATION SUCCESS SUMMARY
-- ============================================================================

-- 17. Overall migration success summary
SELECT 
  'MIGRATION SUCCESS SUMMARY' as summary_type,
  CASE 
    WHEN (SELECT COUNT(*) FROM services WHERE name IN ('Body Painting', 'Face Painting', 'Airbrush Face & Body Painting', 'Glitter & Gem Application', 'Hair Braiding & Styling')) = 5
    THEN '✅ All parent services created'
    ELSE '❌ Missing parent services'
  END as parent_services_status,
  
  CASE 
    WHEN (SELECT COUNT(*) FROM service_pricing_tiers) > 0
    THEN '✅ Pricing tiers created'
    ELSE '❌ No pricing tiers found'
  END as pricing_tiers_status,
  
  CASE 
    WHEN (SELECT COUNT(*) FROM services s LEFT JOIN service_pricing_tiers spt ON s.id = spt.service_id WHERE s.status = 'active' AND s.name IN ('Body Painting', 'Face Painting', 'Airbrush Face & Body Painting', 'Glitter & Gem Application', 'Hair Braiding & Styling') GROUP BY s.id HAVING COUNT(spt.id) = 0) = 0
    THEN '✅ All parent services have pricing tiers'
    ELSE '❌ Some parent services missing pricing tiers'
  END as validation_status,
  
  CASE 
    WHEN (SELECT COUNT(*) FROM bookings WHERE service_id IN (SELECT old_service_id FROM service_consolidation_mapping)) = 0
    THEN '✅ No bookings reference old services'
    ELSE '❌ Bookings still reference old services'
  END as booking_migration_status;

-- ============================================================================
-- CLEANUP RECOMMENDATIONS
-- ============================================================================

-- 18. Cleanup recommendations
SELECT 
  'CLEANUP RECOMMENDATIONS' as recommendation_type,
  CASE 
    WHEN (SELECT COUNT(*) FROM services WHERE id IN (SELECT old_service_id FROM service_consolidation_mapping) AND status != 'archived') > 0
    THEN 'Archive old services: UPDATE services SET status = ''archived'' WHERE id IN (SELECT old_service_id FROM service_consolidation_mapping);'
    ELSE 'Old services already archived'
  END as old_services_cleanup,
  
  CASE 
    WHEN (SELECT COUNT(*) FROM service_consolidation_mapping) > 0
    THEN 'Remove mapping table after validation: DROP TABLE service_consolidation_mapping;'
    ELSE 'Mapping table already removed'
  END as mapping_table_cleanup;

-- ============================================================================
-- INSTRUCTIONS
-- ============================================================================

/*
POST-MIGRATION VALIDATION INSTRUCTIONS:

1. Run this entire script after migration completion
2. Review each section carefully:
   ✅ All parent services created (section 1-2)
   ✅ Pricing tiers properly configured (section 3-5)
   ✅ Old services handled correctly (section 6-7)
   ✅ Booking integrity maintained (section 8-9)
   ✅ Admin interface will work (section 10-11)
   ✅ POS Terminal functionality (section 12-13)
   ✅ Public booking compatibility (section 14)
   ✅ Data integrity checks pass (section 15-16)

3. Check the migration success summary (section 17)
4. Follow cleanup recommendations (section 18)

5. If any validation fails:
   - Do not proceed with cleanup
   - Investigate the specific issue
   - Fix the problem before continuing
   - Re-run validation

6. Only proceed with system testing when all validations pass

7. Test the following after validation:
   - Admin service management interface
   - POS Terminal service selection
   - Public booking system
   - Service editing with pricing tiers
*/
