/**
 * AI Customer Recommendations API Endpoint
 * Provides intelligent artist-customer matching and recommendations
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { CustomerMatchingEngine } from '@/lib/ai/customer-matching'

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false,
      error: 'Method not allowed. Use POST.' 
    })
  }

  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] AI Customer Recommendations API called`)

  try {
    // Authenticate admin user
    const user = await authenticateAdminRequest(req)
    if (!user) {
      console.log(`[${requestId}] Authentication failed`)
      return res.status(401).json({ 
        success: false,
        error: 'Unauthorized. Admin access required.' 
      })
    }

    console.log(`[${requestId}] Authenticated user: ${user.email} (${user.role})`)

    // Validate request body
    const { customerId, serviceId, limit = 5, includeCompatibilityDetails = false } = req.body

    if (!customerId) {
      console.log(`[${requestId}] Missing required field: customerId`)
      return res.status(400).json({ 
        success: false,
        error: 'Missing required field: customerId' 
      })
    }

    // Validate customerId format (should be UUID)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(customerId)) {
      console.log(`[${requestId}] Invalid customerId format: ${customerId}`)
      return res.status(400).json({ 
        success: false,
        error: 'Invalid customerId format. Must be a valid UUID.' 
      })
    }

    // Validate serviceId if provided
    if (serviceId && !uuidRegex.test(serviceId)) {
      console.log(`[${requestId}] Invalid serviceId format: ${serviceId}`)
      return res.status(400).json({ 
        success: false,
        error: 'Invalid serviceId format. Must be a valid UUID.' 
      })
    }

    // Validate limit
    const recommendationLimit = Math.min(Math.max(parseInt(limit) || 5, 1), 20) // Between 1 and 20

    console.log(`[${requestId}] Generating recommendations for customer ${customerId}`, {
      serviceId: serviceId || 'any',
      limit: recommendationLimit,
      includeDetails: includeCompatibilityDetails
    })

    // Initialize customer matching engine
    const matchingEngine = new CustomerMatchingEngine()
    
    const startTime = Date.now()
    const recommendations = await matchingEngine.getRecommendedArtists(
      customerId, 
      serviceId, 
      recommendationLimit
    )
    const processingTime = Date.now() - startTime

    console.log(`[${requestId}] Generated ${recommendations.length} recommendations in ${processingTime}ms`)

    // Process recommendations for response
    const processedRecommendations = recommendations.map((artist, index) => {
      const baseRecommendation = {
        rank: index + 1,
        artistId: artist.id,
        artistName: artist.name,
        compatibilityScore: parseFloat(artist.compatibilityScore.toFixed(3)),
        compatibilityLevel: artist.recommendation?.level || 'unknown',
        recommendationMessage: artist.recommendation?.message || 'No specific recommendation available',
        confidence: parseFloat(artist.compatibilityConfidence.toFixed(3)),
        artistProfile: {
          skillLevel: artist.skill_level,
          experienceYears: artist.experience_years,
          specializations: artist.specializations || [],
          workingStyle: artist.working_style,
          portfolioUrl: artist.portfolio_url,
          averageRating: artist.average_rating || 0,
          totalBookings: artist.total_bookings || 0
        }
      }

      // Include detailed compatibility analysis if requested
      if (includeCompatibilityDetails) {
        baseRecommendation.compatibilityAnalysis = {
          factors: artist.compatibilityFactors || [],
          reasoning: artist.matchReasoning || 'No detailed reasoning available',
          recommendation: artist.recommendation || {}
        }
      }

      return baseRecommendation
    })

    // Calculate recommendation quality metrics
    const qualityMetrics = calculateRecommendationQuality(processedRecommendations)

    // Prepare response
    const response = {
      success: true,
      requestId,
      processingTimeMs: processingTime,
      recommendations: processedRecommendations,
      metadata: {
        customerId,
        serviceId: serviceId || null,
        requestedLimit: recommendationLimit,
        actualCount: processedRecommendations.length,
        generatedAt: new Date().toISOString(),
        apiVersion: '1.0.0',
        qualityMetrics,
        includesCompatibilityDetails: includeCompatibilityDetails
      }
    }

    // Log recommendation summary
    console.log(`[${requestId}] Recommendation summary:`, {
      topScore: processedRecommendations[0]?.compatibilityScore || 0,
      averageScore: qualityMetrics.averageCompatibilityScore,
      highConfidenceCount: processedRecommendations.filter(r => r.confidence > 0.7).length,
      excellentMatches: processedRecommendations.filter(r => r.compatibilityLevel === 'excellent').length
    })

    res.status(200).json(response)

  } catch (error) {
    console.error(`[${requestId}] Customer recommendations API error:`, error)
    
    // Determine error type and status code
    let statusCode = 500
    let errorMessage = 'Internal server error during recommendation generation'
    
    if (error.message.includes('Failed to fetch customer profile')) {
      statusCode = 404
      errorMessage = 'Customer not found in database'
    } else if (error.message.includes('Failed to fetch artists')) {
      statusCode = 503
      errorMessage = 'Unable to fetch artist data from database'
    } else if (error.message.includes('Database error')) {
      statusCode = 503
      errorMessage = 'Database connection error'
    } else if (error.message.includes('No artists available')) {
      statusCode = 422
      errorMessage = 'No artists available for recommendations'
    }

    res.status(statusCode).json({ 
      success: false,
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
      requestId,
      timestamp: new Date().toISOString(),
      suggestions: [
        'Verify customer ID exists in database',
        'Ensure artists are available and active',
        'Check database connectivity'
      ]
    })
  }
}

/**
 * Calculate recommendation quality metrics
 * @param {Array} recommendations - Array of recommendations
 * @returns {Object} Quality metrics
 */
function calculateRecommendationQuality(recommendations) {
  if (recommendations.length === 0) {
    return {
      averageCompatibilityScore: 0,
      averageConfidence: 0,
      scoreDistribution: { excellent: 0, good: 0, fair: 0, poor: 0 },
      qualityRating: 'insufficient_data'
    }
  }

  const scores = recommendations.map(r => r.compatibilityScore)
  const confidences = recommendations.map(r => r.confidence)
  
  const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length
  const averageConfidence = confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length

  const scoreDistribution = {
    excellent: recommendations.filter(r => r.compatibilityLevel === 'excellent').length,
    good: recommendations.filter(r => r.compatibilityLevel === 'good').length,
    fair: recommendations.filter(r => r.compatibilityLevel === 'fair').length,
    poor: recommendations.filter(r => r.compatibilityLevel === 'poor').length
  }

  let qualityRating = 'poor'
  if (averageScore > 0.8 && averageConfidence > 0.7) {
    qualityRating = 'excellent'
  } else if (averageScore > 0.6 && averageConfidence > 0.5) {
    qualityRating = 'good'
  } else if (averageScore > 0.4 && averageConfidence > 0.3) {
    qualityRating = 'fair'
  }

  return {
    averageCompatibilityScore: parseFloat(averageScore.toFixed(3)),
    averageConfidence: parseFloat(averageConfidence.toFixed(3)),
    scoreDistribution,
    qualityRating,
    topScoreRange: scores.length > 0 ? `${Math.min(...scores).toFixed(2)} - ${Math.max(...scores).toFixed(2)}` : '0.00 - 0.00'
  }
}

/**
 * API Documentation
 * 
 * POST /api/ai/customer-recommendations
 * 
 * Request Body:
 * {
 *   "customerId": "uuid",                    // Required: Customer UUID
 *   "serviceId": "uuid",                     // Optional: Service UUID for specialized matching
 *   "limit": 5,                              // Optional: Number of recommendations (1-20, default: 5)
 *   "includeCompatibilityDetails": false     // Optional: Include detailed compatibility analysis
 * }
 * 
 * Response:
 * {
 *   "success": true,
 *   "requestId": "abc123",
 *   "processingTimeMs": 650,
 *   "recommendations": [
 *     {
 *       "rank": 1,
 *       "artistId": "uuid",
 *       "artistName": "Jane Smith",
 *       "compatibilityScore": 0.875,
 *       "compatibilityLevel": "excellent",
 *       "recommendationMessage": "Highly recommended match with strong compatibility",
 *       "confidence": 0.92,
 *       "artistProfile": {
 *         "skillLevel": "expert",
 *         "experienceYears": 8,
 *         "specializations": ["face_painting", "body_art"],
 *         "workingStyle": "professional",
 *         "portfolioUrl": "https://...",
 *         "averageRating": 4.8,
 *         "totalBookings": 156
 *       },
 *       "compatibilityAnalysis": {  // Only if includeCompatibilityDetails: true
 *         "factors": [...],
 *         "reasoning": "Strong historical performance and skill match",
 *         "recommendation": {...}
 *       }
 *     }
 *   ],
 *   "metadata": {
 *     "customerId": "uuid",
 *     "serviceId": null,
 *     "requestedLimit": 5,
 *     "actualCount": 5,
 *     "generatedAt": "2025-01-15T08:30:00Z",
 *     "apiVersion": "1.0.0",
 *     "qualityMetrics": {
 *       "averageCompatibilityScore": 0.742,
 *       "averageConfidence": 0.68,
 *       "scoreDistribution": {
 *         "excellent": 2,
 *         "good": 2,
 *         "fair": 1,
 *         "poor": 0
 *       },
 *       "qualityRating": "good",
 *       "topScoreRange": "0.58 - 0.87"
 *     },
 *     "includesCompatibilityDetails": false
 *   }
 * }
 * 
 * Error Response:
 * {
 *   "success": false,
 *   "error": "Error message",
 *   "details": "Detailed error (development only)",
 *   "requestId": "abc123",
 *   "timestamp": "2025-01-15T08:30:00Z",
 *   "suggestions": [...]
 * }
 * 
 * Status Codes:
 * - 200: Success
 * - 400: Bad Request (invalid parameters)
 * - 401: Unauthorized (authentication required)
 * - 404: Not Found (customer not found)
 * - 405: Method Not Allowed (use POST)
 * - 422: Unprocessable Entity (no artists available)
 * - 503: Service Unavailable (database issues)
 * - 500: Internal Server Error
 * 
 * Authentication:
 * - Requires admin authentication
 * - Include JWT token in Authorization header: "Bearer <token>"
 * 
 * Rate Limiting:
 * - Results are cached for 1 hour per customer/service combination
 * - No specific rate limits, but compatibility calculation is intensive
 * 
 * Compatibility Scoring:
 * - Scores range from 0.0 to 1.0 (higher is better)
 * - Based on historical performance, skill match, specializations, and communication style
 * - Confidence indicates reliability of the score based on available data
 * 
 * Quality Ratings:
 * - excellent: High scores and confidence, strong recommendations
 * - good: Solid scores with reasonable confidence
 * - fair: Moderate scores, limited confidence
 * - poor: Low scores or insufficient data
 * - insufficient_data: No recommendations available
 */
