/**
 * Apple Pay Domain Verification Utility
 * Tests the Apple Pay domain association file accessibility and format
 */

import fs from 'fs'
import path from 'path'

export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const verificationResults = {
      timestamp: new Date().toISOString(),
      domain: 'www.oceansoulsparkles.com.au',
      checks: {}
    }

    // Check 1: File exists locally
    const filePath = path.join(process.cwd(), 'public', '.well-known', 'apple-developer-merchantid-domain-association')
    verificationResults.checks.fileExists = fs.existsSync(filePath)
    
    if (verificationResults.checks.fileExists) {
      // Check 2: File content
      const fileContent = fs.readFileSync(filePath, 'utf8')
      verificationResults.checks.fileSize = fileContent.length
      verificationResults.checks.fileContent = fileContent.substring(0, 100) + (fileContent.length > 100 ? '...' : '')
      
      // Check 3: File format (should be hex-encoded)
      verificationResults.checks.isHexFormat = /^[0-9A-Fa-f]+$/.test(fileContent.trim())
      
      // Check 4: Expected length (Apple Pay domain association files are typically specific lengths)
      verificationResults.checks.hasExpectedLength = fileContent.trim().length > 0
    }

    // Check 5: Test local API endpoint
    try {
      const baseUrl = req.headers.host?.includes('localhost') 
        ? `http://${req.headers.host}` 
        : `https://${req.headers.host}`
      
      const apiResponse = await fetch(`${baseUrl}/api/apple-pay/domain-association`)
      verificationResults.checks.apiEndpointAccessible = apiResponse.ok
      verificationResults.checks.apiStatusCode = apiResponse.status
      verificationResults.checks.apiContentType = apiResponse.headers.get('content-type')
      
      if (apiResponse.ok) {
        const apiContent = await apiResponse.text()
        verificationResults.checks.apiContentLength = apiContent.length
        verificationResults.checks.apiContentMatches = verificationResults.checks.fileExists && 
          apiContent.trim() === fs.readFileSync(filePath, 'utf8').trim()
      }
    } catch (apiError) {
      verificationResults.checks.apiEndpointAccessible = false
      verificationResults.checks.apiError = apiError.message
    }

    // Check 6: HTTPS requirement
    verificationResults.checks.httpsRequired = true
    verificationResults.checks.currentProtocol = req.headers['x-forwarded-proto'] || 'http'
    verificationResults.checks.isHttps = verificationResults.checks.currentProtocol === 'https'

    // Check 7: Expected URL path
    verificationResults.checks.expectedUrl = 'https://www.oceansoulsparkles.com.au/.well-known/apple-developer-merchantid-domain-association'
    
    // Overall status
    const criticalChecks = [
      'fileExists',
      'apiEndpointAccessible',
      'isHexFormat',
      'hasExpectedLength'
    ]
    
    verificationResults.status = criticalChecks.every(check => verificationResults.checks[check]) ? 'PASS' : 'FAIL'
    
    // Recommendations
    verificationResults.recommendations = []
    
    if (!verificationResults.checks.fileExists) {
      verificationResults.recommendations.push('Download the domain association file from Square Developer Console and place it in public/.well-known/')
    }
    
    if (!verificationResults.checks.apiEndpointAccessible) {
      verificationResults.recommendations.push('Ensure the API endpoint is working correctly')
    }
    
    if (!verificationResults.checks.isHttps && process.env.NODE_ENV === 'production') {
      verificationResults.recommendations.push('Apple Pay requires HTTPS in production')
    }
    
    if (!verificationResults.checks.isHexFormat) {
      verificationResults.recommendations.push('Domain association file should contain hex-encoded data from Square')
    }

    // Next steps
    verificationResults.nextSteps = [
      '1. Download the verification file from Square Developer Console',
      '2. Replace the placeholder file in public/.well-known/',
      '3. Deploy to production with HTTPS enabled',
      '4. Test the URL: https://www.oceansoulsparkles.com.au/.well-known/apple-developer-merchantid-domain-association',
      '5. Click "Verify" in Square Developer Console'
    ]

    return res.status(200).json(verificationResults)

  } catch (error) {
    console.error('Error verifying Apple Pay domain:', error)
    return res.status(500).json({
      error: 'Verification failed',
      message: error.message,
      timestamp: new Date().toISOString()
    })
  }
}
