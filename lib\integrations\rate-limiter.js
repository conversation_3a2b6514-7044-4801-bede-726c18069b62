/**
 * Rate Limiter Middleware for Ocean Soul Sparkles Integrations
 * Implements comprehensive rate limiting for API endpoints
 * 
 * Phase 7: Advanced Integrations & Ecosystem
 */

import { rateLimiter, <PERSON>tLogger, SecurityError } from './security-utils'

/**
 * Rate Limiting Configuration
 */
const RATE_LIMITS = {
  // OAuth endpoints
  oauth: {
    maxRequests: 10,
    windowMs: 60000, // 1 minute
    message: 'Too many OAuth requests. Please try again later.'
  },
  
  // Integration sync endpoints
  sync: {
    maxRequests: 30,
    windowMs: 60000, // 1 minute
    message: 'Too many sync requests. Please try again later.'
  },
  
  // Webhook endpoints
  webhook: {
    maxRequests: 100,
    windowMs: 60000, // 1 minute
    message: 'Too many webhook requests. Please try again later.'
  },
  
  // General integration endpoints
  integration: {
    maxRequests: 60,
    windowMs: 60000, // 1 minute
    message: 'Too many integration requests. Please try again later.'
  },
  
  // Admin endpoints
  admin: {
    maxRequests: 120,
    windowMs: 60000, // 1 minute
    message: 'Too many admin requests. Please try again later.'
  }
}

/**
 * Create rate limiter middleware
 */
export function createRateLimiter(type = 'integration', customConfig = {}) {
  const config = { ...RATE_LIMITS[type], ...customConfig }
  
  return async function rateLimiterMiddleware(req, res, next) {
    try {
      // Get identifier for rate limiting
      const identifier = getIdentifier(req)
      
      // Check rate limit
      const result = await rateLimiter.isAllowed(
        `${type}:${identifier}`,
        config.maxRequests,
        config.windowMs
      )
      
      // Set rate limit headers
      res.setHeader('X-RateLimit-Limit', config.maxRequests)
      res.setHeader('X-RateLimit-Remaining', result.remaining)
      res.setHeader('X-RateLimit-Reset', Math.ceil(result.resetTime / 1000))
      
      if (!result.allowed) {
        // Log rate limit violation
        await AuditLogger.logSecurityEvent(
          req.user?.id || 'anonymous',
          'rate_limit_exceeded',
          {
            endpoint: req.url,
            method: req.method,
            identifier,
            type,
            userAgent: req.headers['user-agent'],
            ipAddress: getClientIP(req)
          },
          'warning'
        )
        
        return res.status(429).json({
          error: 'Rate limit exceeded',
          message: config.message,
          retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000)
        })
      }
      
      // Continue to next middleware
      if (next) {
        next()
      }
    } catch (error) {
      console.error('Rate limiter error:', error)
      
      // Don't block requests on rate limiter errors
      if (next) {
        next()
      }
    }
  }
}

/**
 * Get identifier for rate limiting
 */
function getIdentifier(req) {
  // Use user ID if authenticated
  if (req.user?.id) {
    return `user:${req.user.id}`
  }
  
  // Use IP address for anonymous requests
  const ip = getClientIP(req)
  return `ip:${ip}`
}

/**
 * Get client IP address
 */
function getClientIP(req) {
  return (
    req.headers['x-forwarded-for']?.split(',')[0] ||
    req.headers['x-real-ip'] ||
    req.connection?.remoteAddress ||
    req.socket?.remoteAddress ||
    req.ip ||
    'unknown'
  )
}

/**
 * Rate limiter for OAuth endpoints
 */
export const oauthRateLimit = createRateLimiter('oauth')

/**
 * Rate limiter for sync endpoints
 */
export const syncRateLimit = createRateLimiter('sync')

/**
 * Rate limiter for webhook endpoints
 */
export const webhookRateLimit = createRateLimiter('webhook')

/**
 * Rate limiter for integration endpoints
 */
export const integrationRateLimit = createRateLimiter('integration')

/**
 * Rate limiter for admin endpoints
 */
export const adminRateLimit = createRateLimiter('admin')

/**
 * Advanced Rate Limiter Class
 * Provides more sophisticated rate limiting features
 */
export class AdvancedRateLimiter {
  constructor() {
    this.buckets = new Map()
    this.penalties = new Map()
  }

  /**
   * Check rate limit with penalty system
   */
  async checkLimit(identifier, config, penaltyMultiplier = 1) {
    const penalty = this.penalties.get(identifier) || 1
    const adjustedLimit = Math.floor(config.maxRequests / (penalty * penaltyMultiplier))
    
    const result = await rateLimiter.isAllowed(
      identifier,
      Math.max(1, adjustedLimit),
      config.windowMs
    )
    
    return {
      ...result,
      penalty,
      adjustedLimit
    }
  }

  /**
   * Apply penalty for violations
   */
  applyPenalty(identifier, multiplier = 2, durationMs = 300000) {
    const currentPenalty = this.penalties.get(identifier) || 1
    const newPenalty = Math.min(currentPenalty * multiplier, 10) // Max 10x penalty
    
    this.penalties.set(identifier, newPenalty)
    
    // Remove penalty after duration
    setTimeout(() => {
      const currentPenalty = this.penalties.get(identifier) || 1
      if (currentPenalty > 1) {
        this.penalties.set(identifier, Math.max(1, currentPenalty / 2))
      } else {
        this.penalties.delete(identifier)
      }
    }, durationMs)
  }

  /**
   * Clear penalty for identifier
   */
  clearPenalty(identifier) {
    this.penalties.delete(identifier)
  }

  /**
   * Get current penalty
   */
  getPenalty(identifier) {
    return this.penalties.get(identifier) || 1
  }
}

/**
 * Distributed Rate Limiter
 * Uses database for rate limiting across multiple instances
 */
export class DistributedRateLimiter {
  constructor(supabase) {
    this.supabase = supabase
  }

  /**
   * Check distributed rate limit
   */
  async checkDistributedLimit(identifier, maxRequests, windowMs) {
    const windowStart = new Date(Date.now() - windowMs)
    
    try {
      // Count requests in window
      const { count, error } = await this.supabase
        .from('rate_limit_requests')
        .select('*', { count: 'exact', head: true })
        .eq('identifier', identifier)
        .gte('created_at', windowStart.toISOString())
      
      if (error) {
        console.error('Distributed rate limit check error:', error)
        return { allowed: true, remaining: maxRequests }
      }
      
      const requestCount = count || 0
      
      if (requestCount >= maxRequests) {
        return {
          allowed: false,
          remaining: 0,
          resetTime: Date.now() + windowMs
        }
      }
      
      // Record this request
      await this.supabase
        .from('rate_limit_requests')
        .insert({
          identifier,
          created_at: new Date().toISOString()
        })
      
      return {
        allowed: true,
        remaining: maxRequests - requestCount - 1
      }
    } catch (error) {
      console.error('Distributed rate limit error:', error)
      // Fail open - allow request if database is unavailable
      return { allowed: true, remaining: maxRequests }
    }
  }

  /**
   * Clean up old rate limit records
   */
  async cleanup() {
    const cutoff = new Date(Date.now() - 3600000) // 1 hour ago
    
    try {
      await this.supabase
        .from('rate_limit_requests')
        .delete()
        .lt('created_at', cutoff.toISOString())
    } catch (error) {
      console.error('Rate limit cleanup error:', error)
    }
  }
}

/**
 * Smart Rate Limiter
 * Adjusts limits based on user behavior and system load
 */
export class SmartRateLimiter {
  constructor() {
    this.userProfiles = new Map()
    this.systemLoad = 1.0
  }

  /**
   * Get adaptive rate limit for user
   */
  getAdaptiveLimit(userId, baseLimit) {
    const profile = this.userProfiles.get(userId) || {
      trustScore: 1.0,
      violationCount: 0,
      lastViolation: null
    }
    
    // Adjust based on trust score and system load
    const trustMultiplier = profile.trustScore
    const loadMultiplier = 1 / this.systemLoad
    
    return Math.floor(baseLimit * trustMultiplier * loadMultiplier)
  }

  /**
   * Update user trust score
   */
  updateTrustScore(userId, violation = false) {
    const profile = this.userProfiles.get(userId) || {
      trustScore: 1.0,
      violationCount: 0,
      lastViolation: null
    }
    
    if (violation) {
      profile.violationCount++
      profile.lastViolation = Date.now()
      profile.trustScore = Math.max(0.1, profile.trustScore * 0.8)
    } else {
      // Gradually improve trust score for good behavior
      profile.trustScore = Math.min(1.0, profile.trustScore * 1.01)
    }
    
    this.userProfiles.set(userId, profile)
  }

  /**
   * Update system load
   */
  updateSystemLoad(load) {
    this.systemLoad = Math.max(0.1, Math.min(10.0, load))
  }
}

// Export singleton instances
export const advancedRateLimiter = new AdvancedRateLimiter()
export const smartRateLimiter = new SmartRateLimiter()

// Export rate limit configurations
export { RATE_LIMITS }
