/**
 * Calendar Conflicts API Endpoint for Ocean Soul Sparkles
 * Handles calendar conflict detection and resolution
 * 
 * Phase 7: Advanced Integrations & Ecosystem
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { integrationRateLimit } from '@/lib/integrations/rate-limiter'
import { RequestV<PERSON>da<PERSON>, AuditLogger } from '@/lib/integrations/security-utils'
import CalendarManager from '@/lib/integrations/calendar/calendar-manager'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * Calendar Conflicts Handler
 * GET /api/integrations/calendar/conflicts - Get calendar conflicts
 * POST /api/integrations/calendar/conflicts - Check for conflicts
 */
export default async function handler(req, res) {
  // Apply rate limiting
  await new Promise((resolve, reject) => {
    integrationRateLimit(req, res, (error) => {
      if (error) reject(error)
      else resolve()
    })
  })

  const startTime = Date.now()

  try {
    // Validate request method
    RequestValidator.validateEndpointAccess(req, ['GET', 'POST'])

    // Authenticate user
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    const { user } = authResult
    const userId = user.id

    const calendarManager = new CalendarManager(userId)

    if (req.method === 'GET') {
      // Get existing conflicts
      const conflicts = await getExistingConflicts(userId)

      await AuditLogger.logApiAccess(
        userId,
        req.url,
        req.method,
        200,
        Date.now() - startTime,
        { action: 'get_conflicts', conflictCount: conflicts.length }
      )

      return res.status(200).json({
        success: true,
        conflicts
      })

    } else if (req.method === 'POST') {
      // Check for conflicts
      const { startTime: eventStart, endTime: eventEnd, excludeBookingId } = req.body

      if (!eventStart || !eventEnd) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'startTime and endTime are required'
        })
      }

      // Validate dates
      const start = new Date(eventStart)
      const end = new Date(eventEnd)

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return res.status(400).json({
          error: 'Invalid date format',
          message: 'startTime and endTime must be valid ISO date strings'
        })
      }

      if (start >= end) {
        return res.status(400).json({
          error: 'Invalid time range',
          message: 'startTime must be before endTime'
        })
      }

      // Check for conflicts in external calendars
      const externalConflicts = await calendarManager.checkConflicts(
        start,
        end,
        excludeBookingId ? [{ provider: 'ocean_soul_sparkles', eventId: excludeBookingId }] : []
      )

      // Check for conflicts in Ocean Soul Sparkles bookings
      const internalConflicts = await checkInternalConflicts(userId, start, end, excludeBookingId)

      const allConflicts = [
        ...externalConflicts.map(conflict => ({ ...conflict, type: 'external' })),
        ...internalConflicts.map(conflict => ({ ...conflict, type: 'internal' }))
      ]

      await AuditLogger.logApiAccess(
        userId,
        req.url,
        req.method,
        200,
        Date.now() - startTime,
        { 
          action: 'check_conflicts',
          conflictCount: allConflicts.length,
          timeRange: { start: eventStart, end: eventEnd }
        }
      )

      return res.status(200).json({
        success: true,
        conflicts: allConflicts,
        hasConflicts: allConflicts.length > 0
      })
    }

  } catch (error) {
    console.error('Calendar conflicts error:', error)

    await AuditLogger.logApiAccess(
      req.user?.id || null,
      req.url,
      req.method,
      500,
      Date.now() - startTime,
      { error: error.message }
    )

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to process calendar conflicts'
    })
  }
}

/**
 * Get existing conflicts from database
 */
async function getExistingConflicts(userId) {
  try {
    // Get upcoming bookings
    const { data: bookings, error } = await supabase
      .from('bookings')
      .select(`
        *,
        customers(name, email),
        services(name, duration),
        user_profiles(full_name)
      `)
      .eq('customer_id', userId)
      .gte('booking_date', new Date().toISOString())
      .order('booking_date', { ascending: true })

    if (error) {
      console.error('Failed to fetch bookings:', error)
      return []
    }

    const conflicts = []
    const calendarManager = new CalendarManager(userId)

    for (const booking of bookings) {
      try {
        // Calculate booking end time
        const startTime = new Date(booking.booking_date)
        const duration = booking.services?.duration || 60 // Default 60 minutes
        const endTime = new Date(startTime.getTime() + duration * 60000)

        // Check for external conflicts
        const externalConflicts = await calendarManager.checkConflicts(
          startTime,
          endTime,
          [{ provider: 'ocean_soul_sparkles', eventId: booking.id }]
        )

        if (externalConflicts.length > 0) {
          conflicts.push({
            id: `conflict_${booking.id}_${Date.now()}`,
            bookingId: booking.id,
            serviceName: booking.services?.name || 'Unknown Service',
            customerName: booking.customers?.name || 'Unknown Customer',
            date: booking.booking_date,
            duration,
            externalConflicts,
            type: 'external',
            severity: 'high'
          })
        }

      } catch (error) {
        console.error(`Failed to check conflicts for booking ${booking.id}:`, error)
      }
    }

    return conflicts

  } catch (error) {
    console.error('Failed to get existing conflicts:', error)
    return []
  }
}

/**
 * Check for conflicts in Ocean Soul Sparkles bookings
 */
async function checkInternalConflicts(userId, startTime, endTime, excludeBookingId = null) {
  try {
    let query = supabase
      .from('bookings')
      .select(`
        *,
        customers(name, email),
        services(name, duration)
      `)
      .eq('customer_id', userId)
      .gte('booking_date', startTime.toISOString())
      .lte('booking_date', endTime.toISOString())

    if (excludeBookingId) {
      query = query.neq('id', excludeBookingId)
    }

    const { data: bookings, error } = await query

    if (error) {
      console.error('Failed to check internal conflicts:', error)
      return []
    }

    return bookings.map(booking => {
      const bookingStart = new Date(booking.booking_date)
      const duration = booking.services?.duration || 60
      const bookingEnd = new Date(bookingStart.getTime() + duration * 60000)

      return {
        id: booking.id,
        summary: `${booking.services?.name || 'Service'} - ${booking.customers?.name || 'Customer'}`,
        start: bookingStart.toISOString(),
        end: bookingEnd.toISOString(),
        description: `Ocean Soul Sparkles Booking`,
        source: 'ocean_soul_sparkles',
        bookingId: booking.id,
        serviceName: booking.services?.name,
        customerName: booking.customers?.name
      }
    })

  } catch (error) {
    console.error('Failed to check internal conflicts:', error)
    return []
  }
}

/**
 * API Route Configuration
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
