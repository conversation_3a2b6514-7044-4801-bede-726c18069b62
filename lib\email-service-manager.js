/**
 * Email Service Manager for Ocean Soul Sparkles
 * Handles multiple email service providers with fallback support
 */

import { createClient } from '@supabase/supabase-js'

/**
 * Get email configuration from admin settings with decryption
 */
async function getEmailConfig() {
  try {
    const adminClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          persistSession: false,
          autoRefreshToken: false
        }
      }
    )

    if (!adminClient) {
      throw new Error('Failed to initialize admin client')
    }

    // Try to get email configuration from admin_settings table
    const { data: settings, error } = await adminClient
      .from('admin_settings')
      .select('setting_key, setting_value')
      .in('setting_key', [
        'gmail_smtp_user',
        'gmail_smtp_password',
        'gmail_from_email',
        'gmail_from_name',
        'workspace_smtp_user',
        'workspace_smtp_password',
        'workspace_from_email',
        'workspace_from_name',
        'onesignal_app_id',
        'onesignal_rest_api_key',
        'email_service_priority'
      ])

    if (error && error.code !== 'PGRST116') {
      console.warn('Could not fetch email config from admin_settings:', error.message)
      return getEnvironmentEmailConfig()
    }

    // Convert array to object and decrypt sensitive values
    const config = {}
    if (settings) {
      const { prepareFromStorage } = await import('./encryption')
      settings.forEach(setting => {
        config[setting.setting_key] = prepareFromStorage(setting.setting_value)
      })
    }

    // Merge with environment variables as fallback
    return {
      gmail: {
        user: config.gmail_smtp_user || process.env.GMAIL_SMTP_USER,
        password: config.gmail_smtp_password || process.env.GMAIL_SMTP_APP_PASSWORD,
        fromEmail: config.gmail_from_email || process.env.GMAIL_FROM_EMAIL,
        fromName: config.gmail_from_name || process.env.GMAIL_FROM_NAME || 'Ocean Soul Sparkles'
      },
      workspace: {
        user: config.workspace_smtp_user || process.env.WORKSPACE_SMTP_USER,
        password: config.workspace_smtp_password || process.env.WORKSPACE_SMTP_PASSWORD,
        fromEmail: config.workspace_from_email || process.env.WORKSPACE_FROM_EMAIL,
        fromName: config.workspace_from_name || process.env.WORKSPACE_FROM_NAME || 'Ocean Soul Sparkles'
      },
      onesignal: {
        appId: config.onesignal_app_id || process.env.ONESIGNAL_APP_ID,
        restApiKey: config.onesignal_rest_api_key || process.env.ONESIGNAL_REST_API_KEY
      },
      priority: config.email_service_priority || 'gmail,workspace,onesignal'
    }
  } catch (error) {
    console.warn('Error getting email config from database, using environment:', error.message)
    return getEnvironmentEmailConfig()
  }
}

/**
 * Get email configuration from environment variables only
 */
function getEnvironmentEmailConfig() {
  return {
    gmail: {
      user: process.env.GMAIL_SMTP_USER,
      password: process.env.GMAIL_SMTP_APP_PASSWORD,
      fromEmail: process.env.GMAIL_FROM_EMAIL,
      fromName: process.env.GMAIL_FROM_NAME || 'Ocean Soul Sparkles'
    },
    workspace: {
      user: process.env.WORKSPACE_SMTP_USER,
      password: process.env.WORKSPACE_SMTP_PASSWORD,
      fromEmail: process.env.WORKSPACE_FROM_EMAIL,
      fromName: process.env.WORKSPACE_FROM_NAME || 'Ocean Soul Sparkles'
    },
    onesignal: {
      appId: process.env.ONESIGNAL_APP_ID,
      restApiKey: process.env.ONESIGNAL_REST_API_KEY
    },
    priority: 'gmail,workspace,onesignal'
  }
}

/**
 * Send email via Gmail SMTP
 */
async function sendViaGmail(config, { to, subject, text, html, fromName }) {
  try {
    if (!config.gmail.user || !config.gmail.password) {
      throw new Error('Gmail SMTP credentials not configured')
    }

    // Fix nodemailer import for production compatibility
    let nodemailer
    try {
      nodemailer = await import('nodemailer')
      console.log('Nodemailer imported:', Object.keys(nodemailer))
    } catch (importError) {
      console.error('Nodemailer import error:', importError.message)
      throw new Error(`Failed to import nodemailer: ${importError.message}`)
    }

    // Handle different export patterns - nodemailer uses createTransport (not createTransporter)
    const createTransporter = nodemailer.default?.createTransport ||
                             nodemailer.createTransport ||
                             nodemailer.default

    if (typeof createTransporter !== 'function') {
      throw new Error(`createTransport is not a function. Available: ${Object.keys(nodemailer)}`)
    }
    const transporter = createTransporter({
      service: 'gmail',
      auth: {
        user: config.gmail.user,
        pass: config.gmail.password
      },
      timeout: 30000, // 30 second timeout
      connectionTimeout: 30000,
      greetingTimeout: 30000
    })

    const fromEmail = config.gmail.fromEmail || config.gmail.user
    const senderName = fromName || config.gmail.fromName

    const result = await transporter.sendMail({
      from: `"${senderName}" <${fromEmail}>`,
      to,
      subject,
      text,
      html: html || text
    })

    return {
      success: true,
      service: 'gmail-smtp',
      messageId: result.messageId,
      recipient: to
    }
  } catch (error) {
    console.error('Gmail SMTP error:', error.message)
    return {
      success: false,
      service: 'gmail-smtp',
      error: error.message
    }
  }
}

/**
 * Send email via Workspace SMTP
 */
async function sendViaWorkspace(config, { to, subject, text, html, fromName }) {
  try {
    if (!config.workspace.user || !config.workspace.password) {
      throw new Error('Workspace SMTP credentials not configured')
    }

    // Fix nodemailer import for production compatibility
    let nodemailer
    try {
      nodemailer = await import('nodemailer')
    } catch (importError) {
      console.error('Nodemailer import error:', importError.message)
      throw new Error(`Failed to import nodemailer: ${importError.message}`)
    }

    // Handle different export patterns - nodemailer uses createTransport (not createTransporter)
    const createTransporter = nodemailer.default?.createTransport ||
                             nodemailer.createTransport ||
                             nodemailer.default

    if (typeof createTransporter !== 'function') {
      throw new Error(`createTransport is not a function. Available: ${Object.keys(nodemailer)}`)
    }
    const transporter = createTransporter({
      host: 'smtp.gmail.com',
      port: 587,
      secure: false,
      auth: {
        user: config.workspace.user,
        pass: config.workspace.password
      },
      timeout: 30000,
      connectionTimeout: 30000,
      greetingTimeout: 30000
    })

    const fromEmail = config.workspace.fromEmail || config.workspace.user
    const senderName = fromName || config.workspace.fromName

    const result = await transporter.sendMail({
      from: `"${senderName}" <${fromEmail}>`,
      to,
      subject,
      text,
      html: html || text
    })

    return {
      success: true,
      service: 'workspace-smtp',
      messageId: result.messageId,
      recipient: to
    }
  } catch (error) {
    console.error('Workspace SMTP error:', error.message)
    return {
      success: false,
      service: 'workspace-smtp',
      error: error.message
    }
  }
}

/**
 * Send email via OneSignal
 */
async function sendViaOneSignal(config, { to, subject, text, html }) {
  try {
    if (!config.onesignal.appId || !config.onesignal.restApiKey) {
      throw new Error('OneSignal credentials not configured')
    }

    const response = await fetch('https://onesignal.com/api/v1/notifications', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${config.onesignal.restApiKey}`
      },
      body: JSON.stringify({
        app_id: config.onesignal.appId,
        include_email_tokens: [to],
        email_subject: subject,
        email_body: html || text
      })
    })

    if (!response.ok) {
      const errorData = await response.text()
      throw new Error(`OneSignal API error: ${response.status} - ${errorData}`)
    }

    const result = await response.json()

    return {
      success: true,
      service: 'onesignal',
      messageId: result.id,
      recipient: to
    }
  } catch (error) {
    console.error('OneSignal error:', error.message)
    return {
      success: false,
      service: 'onesignal',
      error: error.message
    }
  }
}

/**
 * Send email with automatic fallback between services
 */
export async function sendEmailWithFallback({ to, subject, text, html, fromName }) {
  const config = await getEmailConfig()
  const services = config.priority.split(',').map(s => s.trim())
  
  console.log(`Attempting to send email to ${to} using services: ${services.join(', ')}`)

  for (const service of services) {
    let result

    try {
      switch (service) {
        case 'gmail':
          result = await sendViaGmail(config, { to, subject, text, html, fromName })
          break
        case 'workspace':
          result = await sendViaWorkspace(config, { to, subject, text, html, fromName })
          break
        case 'onesignal':
          result = await sendViaOneSignal(config, { to, subject, text, html })
          break
        default:
          console.warn(`Unknown email service: ${service}`)
          continue
      }

      if (result.success) {
        console.log(`✅ Email sent successfully via ${result.service}`)
        return result
      } else {
        console.warn(`❌ ${result.service} failed: ${result.error}`)
      }
    } catch (error) {
      console.error(`💥 ${service} service error:`, error.message)
    }
  }

  // If all services failed
  throw new Error('All email services failed. Please check your email configuration.')
}

export default {
  sendEmailWithFallback,
  getEmailConfig
}
