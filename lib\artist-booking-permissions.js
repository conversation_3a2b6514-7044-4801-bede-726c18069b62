/**
 * Artist/Braider Booking Permissions Library
 * Handles role-based access control for booking system integration
 */

import { createClient } from '@supabase/supabase-js'

// Create Supabase client only if environment variables are available
let supabase = null
if (process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.SUPABASE_SERVICE_ROLE_KEY) {
  supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  )
}

/**
 * Permission levels for booking system
 */
export const BOOKING_PERMISSIONS = {
  // View permissions
  VIEW_OWN_BOOKINGS: 'view_own_bookings',
  VIEW_ALL_BOOKINGS: 'view_all_bookings',
  VIEW_CUSTOMER_DETAILS: 'view_customer_details',
  
  // Booking management permissions
  CREATE_BOOKINGS: 'create_bookings',
  EDIT_OWN_BOOKINGS: 'edit_own_bookings',
  EDIT_ALL_BOOKINGS: 'edit_all_bookings',
  CANCEL_OWN_BOOKINGS: 'cancel_own_bookings',
  CANCEL_ALL_BOOKINGS: 'cancel_all_bookings',
  
  // Artist assignment permissions
  ASSIGN_SELF_TO_BOOKINGS: 'assign_self_to_bookings',
  ASSIGN_OTHERS_TO_BOOKINGS: 'assign_others_to_bookings',
  REMOVE_ARTIST_ASSIGNMENTS: 'remove_artist_assignments',
  
  // Availability management
  MANAGE_OWN_AVAILABILITY: 'manage_own_availability',
  MANAGE_ALL_AVAILABILITY: 'manage_all_availability',
  
  // Financial permissions
  VIEW_OWN_EARNINGS: 'view_own_earnings',
  VIEW_ALL_EARNINGS: 'view_all_earnings',
  PROCESS_PAYMENTS: 'process_payments'
}

/**
 * Role-based permission matrix
 */
export const ROLE_PERMISSIONS = {
  dev: Object.values(BOOKING_PERMISSIONS), // Full access
  admin: Object.values(BOOKING_PERMISSIONS), // Full access
  artist: [
    BOOKING_PERMISSIONS.VIEW_OWN_BOOKINGS,
    BOOKING_PERMISSIONS.VIEW_CUSTOMER_DETAILS,
    BOOKING_PERMISSIONS.EDIT_OWN_BOOKINGS,
    BOOKING_PERMISSIONS.CANCEL_OWN_BOOKINGS,
    BOOKING_PERMISSIONS.ASSIGN_SELF_TO_BOOKINGS,
    BOOKING_PERMISSIONS.MANAGE_OWN_AVAILABILITY,
    BOOKING_PERMISSIONS.VIEW_OWN_EARNINGS
  ],
  braider: [
    BOOKING_PERMISSIONS.VIEW_OWN_BOOKINGS,
    BOOKING_PERMISSIONS.VIEW_CUSTOMER_DETAILS,
    BOOKING_PERMISSIONS.EDIT_OWN_BOOKINGS,
    BOOKING_PERMISSIONS.CANCEL_OWN_BOOKINGS,
    BOOKING_PERMISSIONS.ASSIGN_SELF_TO_BOOKINGS,
    BOOKING_PERMISSIONS.MANAGE_OWN_AVAILABILITY,
    BOOKING_PERMISSIONS.VIEW_OWN_EARNINGS
  ],
  user: [] // No booking system access
}

/**
 * Check if a user has a specific booking permission
 * @param {string} userRole - User's role (dev, admin, artist, braider, user)
 * @param {string} permission - Permission to check
 * @returns {boolean} - Whether user has permission
 */
export function hasBookingPermission(userRole, permission) {
  if (!userRole || !permission) return false
  
  const rolePermissions = ROLE_PERMISSIONS[userRole] || []
  return rolePermissions.includes(permission)
}

/**
 * Check multiple permissions at once
 * @param {string} userRole - User's role
 * @param {string[]} permissions - Array of permissions to check
 * @returns {Object} - Object with permission results
 */
export function checkBookingPermissions(userRole, permissions) {
  const results = {}
  
  permissions.forEach(permission => {
    results[permission] = hasBookingPermission(userRole, permission)
  })
  
  return results
}

/**
 * Get all permissions for a role
 * @param {string} userRole - User's role
 * @returns {string[]} - Array of permissions
 */
export function getRoleBookingPermissions(userRole) {
  return ROLE_PERMISSIONS[userRole] || []
}

/**
 * Check if user can access a specific booking
 * @param {string} userId - User ID
 * @param {string} userRole - User role
 * @param {Object} booking - Booking object
 * @returns {boolean} - Whether user can access booking
 */
export function canAccessBooking(userId, userRole, booking) {
  // Admins and devs can access all bookings
  if (['dev', 'admin'].includes(userRole)) {
    return true
  }
  
  // Artists/braiders can only access their own bookings
  if (['artist', 'braider'].includes(userRole)) {
    return booking.assigned_artist_id === userId || 
           booking.preferred_artist_id === userId
  }
  
  return false
}

/**
 * Filter bookings based on user permissions
 * @param {string} userId - User ID
 * @param {string} userRole - User role
 * @param {Object[]} bookings - Array of booking objects
 * @returns {Object[]} - Filtered bookings
 */
export function filterBookingsByPermissions(userId, userRole, bookings) {
  if (['dev', 'admin'].includes(userRole)) {
    return bookings // Full access
  }
  
  if (['artist', 'braider'].includes(userRole)) {
    return bookings.filter(booking => canAccessBooking(userId, userRole, booking))
  }
  
  return [] // No access
}

/**
 * Get artist profile for a user
 * @param {string} userId - User ID
 * @returns {Promise<Object|null>} - Artist profile or null
 */
export async function getArtistProfile(userId) {
  try {
    const { data: profile, error } = await supabase
      .from('artist_profiles')
      .select('*')
      .eq('user_id', userId)
      .single()
    
    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching artist profile:', error)
      return null
    }
    
    return profile
  } catch (error) {
    console.error('Error in getArtistProfile:', error)
    return null
  }
}

/**
 * Check if user is an active artist/braider
 * @param {string} userId - User ID
 * @returns {Promise<boolean>} - Whether user is active artist/braider
 */
export async function isActiveArtist(userId) {
  const profile = await getArtistProfile(userId)
  return profile && profile.is_active
}

/**
 * Get booking query filters for user role
 * @param {string} userId - User ID
 * @param {string} userRole - User role
 * @returns {Object} - Query filters for Supabase
 */
export function getBookingQueryFilters(userId, userRole) {
  if (['dev', 'admin'].includes(userRole)) {
    return {} // No filters - can see all
  }
  
  if (['artist', 'braider'].includes(userRole)) {
    return {
      or: `assigned_artist_id.eq.${userId},preferred_artist_id.eq.${userId}`
    }
  }
  
  return { id: 'never-match' } // No access
}

/**
 * Validate booking assignment permissions
 * @param {string} userId - User ID making the assignment
 * @param {string} userRole - User role
 * @param {string} targetArtistId - Artist being assigned
 * @param {Object} booking - Booking object
 * @returns {Object} - Validation result
 */
export function validateBookingAssignment(userId, userRole, targetArtistId, booking) {
  // Admins can assign anyone
  if (['dev', 'admin'].includes(userRole)) {
    return { valid: true }
  }
  
  // Artists can only assign themselves
  if (['artist', 'braider'].includes(userRole)) {
    if (targetArtistId === userId) {
      return { valid: true }
    } else {
      return { 
        valid: false, 
        error: 'You can only assign yourself to bookings' 
      }
    }
  }
  
  return { 
    valid: false, 
    error: 'Insufficient permissions to assign artists' 
  }
}

/**
 * Get available actions for a booking based on user permissions
 * @param {string} userId - User ID
 * @param {string} userRole - User role
 * @param {Object} booking - Booking object
 * @returns {string[]} - Array of available actions
 */
export function getAvailableBookingActions(userId, userRole, booking) {
  const actions = []
  
  if (canAccessBooking(userId, userRole, booking)) {
    actions.push('view')
    
    if (hasBookingPermission(userRole, BOOKING_PERMISSIONS.EDIT_OWN_BOOKINGS) ||
        hasBookingPermission(userRole, BOOKING_PERMISSIONS.EDIT_ALL_BOOKINGS)) {
      actions.push('edit')
    }
    
    if (hasBookingPermission(userRole, BOOKING_PERMISSIONS.CANCEL_OWN_BOOKINGS) ||
        hasBookingPermission(userRole, BOOKING_PERMISSIONS.CANCEL_ALL_BOOKINGS)) {
      actions.push('cancel')
    }
    
    if (hasBookingPermission(userRole, BOOKING_PERMISSIONS.ASSIGN_SELF_TO_BOOKINGS)) {
      actions.push('assign_self')
    }
    
    if (hasBookingPermission(userRole, BOOKING_PERMISSIONS.ASSIGN_OTHERS_TO_BOOKINGS)) {
      actions.push('assign_others')
    }
  }
  
  return actions
}

/**
 * Middleware function to check booking permissions in API routes
 * @param {string} requiredPermission - Required permission
 * @returns {Function} - Middleware function
 */
export function requireBookingPermission(requiredPermission) {
  return (userRole) => {
    if (!hasBookingPermission(userRole, requiredPermission)) {
      throw new Error(`Insufficient permissions. Required: ${requiredPermission}`)
    }
    return true
  }
}
