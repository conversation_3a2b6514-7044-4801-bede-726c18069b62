/**
 * Enhanced Data Encryption Utilities for Ocean Soul Sparkles
 * Provides end-to-end encryption for sensitive customer data beyond existing AES-256
 * 
 * Phase 9.2: Data Protection & Privacy Compliance
 */

import crypto from 'crypto'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * Enhanced Data Encryption Manager Class
 * Provides multiple encryption methods and key management
 */
export class DataEncryptionManager {
  constructor() {
    this.algorithms = {
      'aes-256-gcm': { keyLength: 32, ivLength: 16, tagLength: 16 },
      'aes-256-cbc': { keyLength: 32, ivLength: 16, tagLength: 0 },
      'chacha20-poly1305': { keyLength: 32, ivLength: 12, tagLength: 16 }
    }
    this.defaultAlgorithm = 'aes-256-gcm'
    this.keyRotationInterval = 90 * 24 * 60 * 60 * 1000 // 90 days
    this.dataClassifications = {
      'pii': 'aes-256-gcm',
      'financial': 'aes-256-gcm',
      'health': 'aes-256-gcm',
      'biometric': 'chacha20-poly1305',
      'confidential': 'aes-256-gcm',
      'internal': 'aes-256-cbc'
    }
  }

  /**
   * Generate a new encryption key
   * @param {string} algorithm - Encryption algorithm
   * @returns {string} - Base64 encoded key
   */
  generateKey(algorithm = this.defaultAlgorithm) {
    const keyLength = this.algorithms[algorithm]?.keyLength || 32
    return crypto.randomBytes(keyLength).toString('base64')
  }

  /**
   * Encrypt data with enhanced security
   * @param {string} plaintext - Data to encrypt
   * @param {string} dataClassification - Data classification level
   * @param {string} algorithm - Encryption algorithm (optional)
   * @param {string} key - Encryption key (optional, will generate if not provided)
   * @returns {Promise<Object>} - Encrypted data with metadata
   */
  async encryptData(plaintext, dataClassification, algorithm = null, key = null) {
    try {
      if (!plaintext || typeof plaintext !== 'string') {
        throw new Error('Invalid plaintext data')
      }

      // Determine algorithm based on data classification
      const selectedAlgorithm = algorithm || this.dataClassifications[dataClassification] || this.defaultAlgorithm
      const algorithmConfig = this.algorithms[selectedAlgorithm]

      if (!algorithmConfig) {
        throw new Error(`Unsupported algorithm: ${selectedAlgorithm}`)
      }

      // Generate or use provided key
      const encryptionKey = key ? Buffer.from(key, 'base64') : crypto.randomBytes(algorithmConfig.keyLength)
      const iv = crypto.randomBytes(algorithmConfig.ivLength)

      let encrypted, authTag

      if (selectedAlgorithm === 'aes-256-gcm' || selectedAlgorithm === 'chacha20-poly1305') {
        // Authenticated encryption
        const cipher = crypto.createCipher(selectedAlgorithm, encryptionKey)
        cipher.setAAD(Buffer.from(dataClassification, 'utf8'))

        encrypted = Buffer.concat([
          cipher.update(plaintext, 'utf8'),
          cipher.final()
        ])

        authTag = cipher.getAuthTag()
      } else {
        // Standard encryption
        const cipher = crypto.createCipher(selectedAlgorithm, encryptionKey)
        encrypted = Buffer.concat([
          cipher.update(plaintext, 'utf8'),
          cipher.final()
        ])
      }

      const encryptedData = {
        algorithm: selectedAlgorithm,
        encrypted: encrypted.toString('base64'),
        iv: iv.toString('base64'),
        authTag: authTag ? authTag.toString('base64') : null,
        keyVersion: await this.getCurrentKeyVersion(),
        dataClassification,
        encryptedAt: new Date().toISOString()
      }

      // Store encryption metadata
      await this.storeEncryptionMetadata(dataClassification, selectedAlgorithm, encryptedData.keyVersion)

      return {
        success: true,
        encryptedData,
        key: encryptionKey.toString('base64') // Return key for storage
      }
    } catch (error) {
      console.error('Error encrypting data:', error)
      throw error
    }
  }

  /**
   * Decrypt data with enhanced security
   * @param {Object} encryptedData - Encrypted data object
   * @param {string} key - Decryption key
   * @returns {Promise<string>} - Decrypted plaintext
   */
  async decryptData(encryptedData, key) {
    try {
      const {
        algorithm,
        encrypted,
        iv,
        authTag,
        dataClassification
      } = encryptedData

      const algorithmConfig = this.algorithms[algorithm]
      if (!algorithmConfig) {
        throw new Error(`Unsupported algorithm: ${algorithm}`)
      }

      const decryptionKey = Buffer.from(key, 'base64')
      const ivBuffer = Buffer.from(iv, 'base64')
      const encryptedBuffer = Buffer.from(encrypted, 'base64')

      let decrypted

      if (algorithm === 'aes-256-gcm' || algorithm === 'chacha20-poly1305') {
        // Authenticated decryption
        const decipher = crypto.createDecipher(algorithm, decryptionKey)
        decipher.setAAD(Buffer.from(dataClassification, 'utf8'))
        
        if (authTag) {
          decipher.setAuthTag(Buffer.from(authTag, 'base64'))
        }

        decrypted = Buffer.concat([
          decipher.update(encryptedBuffer),
          decipher.final()
        ])
      } else {
        // Standard decryption
        const decipher = crypto.createDecipher(algorithm, decryptionKey)
        decrypted = Buffer.concat([
          decipher.update(encryptedBuffer),
          decipher.final()
        ])
      }

      return decrypted.toString('utf8')
    } catch (error) {
      console.error('Error decrypting data:', error)
      throw error
    }
  }

  /**
   * Encrypt field-level data for database storage
   * @param {Object} data - Object with fields to encrypt
   * @param {Array} fieldsToEncrypt - Array of field names to encrypt
   * @param {string} dataClassification - Data classification
   * @returns {Promise<Object>} - Object with encrypted fields
   */
  async encryptFields(data, fieldsToEncrypt, dataClassification) {
    try {
      const encryptedData = { ...data }
      const encryptionKeys = {}

      for (const field of fieldsToEncrypt) {
        if (data[field] && typeof data[field] === 'string') {
          const result = await this.encryptData(data[field], dataClassification)
          encryptedData[field] = result.encryptedData
          encryptionKeys[field] = result.key
        }
      }

      return {
        encryptedData,
        encryptionKeys
      }
    } catch (error) {
      console.error('Error encrypting fields:', error)
      throw error
    }
  }

  /**
   * Decrypt field-level data from database
   * @param {Object} data - Object with encrypted fields
   * @param {Array} fieldsToDecrypt - Array of field names to decrypt
   * @param {Object} encryptionKeys - Object with decryption keys
   * @returns {Promise<Object>} - Object with decrypted fields
   */
  async decryptFields(data, fieldsToDecrypt, encryptionKeys) {
    try {
      const decryptedData = { ...data }

      for (const field of fieldsToDecrypt) {
        if (data[field] && encryptionKeys[field]) {
          try {
            decryptedData[field] = await this.decryptData(data[field], encryptionKeys[field])
          } catch (decryptError) {
            console.error(`Error decrypting field ${field}:`, decryptError)
            decryptedData[field] = '[DECRYPTION_ERROR]'
          }
        }
      }

      return decryptedData
    } catch (error) {
      console.error('Error decrypting fields:', error)
      throw error
    }
  }

  /**
   * Generate secure hash for data integrity
   * @param {string} data - Data to hash
   * @param {string} algorithm - Hash algorithm
   * @returns {string} - Hash value
   */
  generateHash(data, algorithm = 'sha256') {
    return crypto.createHash(algorithm).update(data).digest('hex')
  }

  /**
   * Verify data integrity using hash
   * @param {string} data - Original data
   * @param {string} hash - Expected hash
   * @param {string} algorithm - Hash algorithm
   * @returns {boolean} - Integrity check result
   */
  verifyIntegrity(data, hash, algorithm = 'sha256') {
    const computedHash = this.generateHash(data, algorithm)
    return computedHash === hash
  }

  /**
   * Rotate encryption keys for a data classification
   * @param {string} dataClassification - Data classification
   * @returns {Promise<Object>} - Key rotation result
   */
  async rotateKeys(dataClassification) {
    try {
      const algorithm = this.dataClassifications[dataClassification] || this.defaultAlgorithm
      const newKey = this.generateKey(algorithm)
      const newKeyVersion = await this.getNextKeyVersion()

      // Update encryption metadata
      await this.storeEncryptionMetadata(dataClassification, algorithm, newKeyVersion)

      // Schedule re-encryption of existing data (would be done in background)
      await this.scheduleDataReEncryption(dataClassification, newKeyVersion)

      return {
        success: true,
        newKeyVersion,
        algorithm,
        rotatedAt: new Date().toISOString()
      }
    } catch (error) {
      console.error('Error rotating keys:', error)
      throw error
    }
  }

  /**
   * Get current key version
   * @returns {Promise<string>} - Current key version
   */
  async getCurrentKeyVersion() {
    try {
      const { data, error } = await supabase
        .from('encryption_metadata')
        .select('key_version')
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      if (error && error.code !== 'PGRST116') { // Not found is OK
        throw error
      }

      return data?.key_version || 'v1.0'
    } catch (error) {
      return 'v1.0' // Default version
    }
  }

  /**
   * Get next key version
   * @returns {Promise<string>} - Next key version
   */
  async getNextKeyVersion() {
    const currentVersion = await this.getCurrentKeyVersion()
    const versionNumber = parseFloat(currentVersion.replace('v', '')) + 0.1
    return `v${versionNumber.toFixed(1)}`
  }

  /**
   * Store encryption metadata
   * @param {string} dataClassification - Data classification
   * @param {string} algorithm - Encryption algorithm
   * @param {string} keyVersion - Key version
   */
  async storeEncryptionMetadata(dataClassification, algorithm, keyVersion) {
    try {
      const nextRotationDue = new Date(Date.now() + this.keyRotationInterval)

      await supabase
        .from('encryption_metadata')
        .upsert({
          table_name: 'customer_data', // Generic for now
          field_name: dataClassification,
          encryption_algorithm: algorithm,
          key_version: keyVersion,
          next_rotation_due: nextRotationDue.toISOString(),
          data_classification: dataClassification,
          compliance_requirements: this.getComplianceRequirements(dataClassification)
        })
    } catch (error) {
      console.error('Error storing encryption metadata:', error)
    }
  }

  /**
   * Schedule data re-encryption with new key
   * @param {string} dataClassification - Data classification
   * @param {string} newKeyVersion - New key version
   */
  async scheduleDataReEncryption(dataClassification, newKeyVersion) {
    try {
      // In a production system, this would queue background jobs
      // to re-encrypt existing data with the new key
      console.log(`Scheduled re-encryption for ${dataClassification} with key version ${newKeyVersion}`)
      
      // TODO: Implement background job scheduling
    } catch (error) {
      console.error('Error scheduling re-encryption:', error)
    }
  }

  /**
   * Get compliance requirements for data classification
   * @param {string} dataClassification - Data classification
   * @returns {Array} - Compliance requirements
   */
  getComplianceRequirements(dataClassification) {
    const requirements = {
      'pii': ['GDPR', 'CCPA', 'PIPEDA'],
      'financial': ['PCI-DSS', 'GDPR', 'SOX'],
      'health': ['HIPAA', 'GDPR'],
      'biometric': ['GDPR', 'BIPA', 'CCPA'],
      'confidential': ['GDPR', 'ISO27001'],
      'internal': ['ISO27001']
    }

    return requirements[dataClassification] || ['GDPR']
  }

  /**
   * Encrypt customer PII data
   * @param {Object} customerData - Customer data object
   * @returns {Promise<Object>} - Encrypted customer data
   */
  async encryptCustomerPII(customerData) {
    const piiFields = ['name', 'email', 'phone', 'address', 'city', 'state', 'postal_code']
    return await this.encryptFields(customerData, piiFields, 'pii')
  }

  /**
   * Decrypt customer PII data
   * @param {Object} encryptedCustomerData - Encrypted customer data
   * @param {Object} encryptionKeys - Decryption keys
   * @returns {Promise<Object>} - Decrypted customer data
   */
  async decryptCustomerPII(encryptedCustomerData, encryptionKeys) {
    const piiFields = ['name', 'email', 'phone', 'address', 'city', 'state', 'postal_code']
    return await this.decryptFields(encryptedCustomerData, piiFields, encryptionKeys)
  }

  /**
   * Encrypt payment information
   * @param {Object} paymentData - Payment data object
   * @returns {Promise<Object>} - Encrypted payment data
   */
  async encryptPaymentData(paymentData) {
    const financialFields = ['card_number', 'cvv', 'bank_account']
    return await this.encryptFields(paymentData, financialFields, 'financial')
  }

  /**
   * Check if key rotation is due
   * @param {string} dataClassification - Data classification
   * @returns {Promise<boolean>} - Whether rotation is due
   */
  async isKeyRotationDue(dataClassification) {
    try {
      const { data, error } = await supabase
        .from('encryption_metadata')
        .select('next_rotation_due')
        .eq('data_classification', dataClassification)
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      if (error || !data) {
        return true // Rotate if no metadata exists
      }

      return new Date(data.next_rotation_due) <= new Date()
    } catch (error) {
      console.error('Error checking key rotation:', error)
      return false
    }
  }

  /**
   * Get encryption statistics
   * @returns {Promise<Object>} - Encryption statistics
   */
  async getEncryptionStats() {
    try {
      const { data: metadata, error } = await supabase
        .from('encryption_metadata')
        .select('*')

      if (error) {
        throw error
      }

      const stats = {
        totalEncryptedFields: metadata?.length || 0,
        algorithmDistribution: {},
        classificationDistribution: {},
        keysNeedingRotation: 0
      }

      metadata?.forEach(item => {
        // Algorithm distribution
        stats.algorithmDistribution[item.encryption_algorithm] = 
          (stats.algorithmDistribution[item.encryption_algorithm] || 0) + 1

        // Classification distribution
        stats.classificationDistribution[item.data_classification] = 
          (stats.classificationDistribution[item.data_classification] || 0) + 1

        // Keys needing rotation
        if (new Date(item.next_rotation_due) <= new Date()) {
          stats.keysNeedingRotation++
        }
      })

      return stats
    } catch (error) {
      console.error('Error getting encryption stats:', error)
      return {
        totalEncryptedFields: 0,
        algorithmDistribution: {},
        classificationDistribution: {},
        keysNeedingRotation: 0
      }
    }
  }
}

// Export singleton instance
export const dataEncryptionManager = new DataEncryptionManager()
export default dataEncryptionManager
