{"name": "Ocean Soul Sparkles", "short_name": "OSS", "description": "Melbourne's premier face painting, airbrush body art, and braiding service for events, festivals, and parties.", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#4A90E2", "orientation": "portrait-primary", "scope": "/", "lang": "en-AU", "dir": "ltr", "categories": ["entertainment", "lifestyle", "business"], "icons": [{"src": "/images/icons/icon-72x72.svg", "sizes": "72x72", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "/images/icons/icon-96x96.svg", "sizes": "96x96", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "/images/icons/icon-128x128.svg", "sizes": "128x128", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "/images/icons/icon-144x144.svg", "sizes": "144x144", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "/images/icons/icon-152x152.svg", "sizes": "152x152", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "/images/icons/icon-192x192.svg", "sizes": "192x192", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "/images/icons/icon-384x384.svg", "sizes": "384x384", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "/images/icons/icon-512x512.svg", "sizes": "512x512", "type": "image/svg+xml", "purpose": "maskable any"}], "shortcuts": [{"name": "Book Service", "short_name": "Book", "description": "Book a face painting or braiding service", "url": "/book-online", "icons": [{"src": "/images/icons/shortcut-book.svg", "sizes": "96x96"}]}, {"name": "Admin Dashboard", "short_name": "Admin", "description": "Access the admin dashboard", "url": "/admin/artist-braider-dashboard", "icons": [{"src": "/images/icons/shortcut-admin.svg", "sizes": "96x96"}]}, {"name": "POS System", "short_name": "POS", "description": "Point of Sale system for events", "url": "/admin/pos", "icons": [{"src": "/images/icons/shortcut-pos.svg", "sizes": "96x96"}]}], "screenshots": [{"src": "/images/screenshots/mobile-home.svg", "sizes": "540x720", "type": "image/svg+xml", "form_factor": "narrow", "label": "Home screen on mobile"}, {"src": "/images/screenshots/desktop-dashboard.svg", "sizes": "1280x720", "type": "image/svg+xml", "form_factor": "wide", "label": "Admin dashboard on desktop"}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}, "handle_links": "preferred", "capture_links": "existing-client-navigate"}