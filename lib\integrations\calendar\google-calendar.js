/**
 * Google Calendar Integration for Ocean Soul Sparkles
 * Provides Google Calendar API integration with two-way synchronization
 * 
 * Phase 7: Advanced Integrations & Ecosystem
 */

import { google } from 'googleapis'
import oauthManager from '../oauth-manager'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>tLogger } from '../security-utils'

/**
 * Google Calendar API Client
 */
export class GoogleCalendarClient {
  constructor(userId) {
    this.userId = userId
    this.calendar = null
    this.auth = null
  }

  /**
   * Initialize Google Calendar client with user credentials
   */
  async initialize() {
    try {
      // Get user's OAuth tokens
      const tokens = await oauthManager.getTokens(this.userId, 'google_calendar')
      
      if (!tokens) {
        throw new Error('No Google Calendar credentials found for user')
      }

      // Check if tokens need refresh
      if (oauthManager.needsRefresh(tokens.expires_at)) {
        const refreshedTokens = await oauthManager.refreshTokens(this.userId, 'google_calendar')
        tokens.access_token = refreshedTokens.access_token
      }

      // Initialize OAuth2 client
      this.auth = new google.auth.OAuth2(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET,
        `${process.env.NEXT_PUBLIC_SITE_URL}/api/integrations/oauth/callback/google_calendar`
      )

      this.auth.setCredentials(tokens)

      // Initialize Calendar API
      this.calendar = google.calendar({ version: 'v3', auth: this.auth })

      return true
    } catch (error) {
      console.error('Failed to initialize Google Calendar client:', error)
      
      await AuditLogger.logIntegrationActivity(
        this.userId,
        'google_calendar',
        'initialization_failed',
        'error',
        { error: error.message }
      )
      
      return false
    }
  }

  /**
   * Get user's calendar list
   */
  async getCalendars() {
    if (!this.calendar) {
      throw new Error('Google Calendar client not initialized')
    }

    return await RetryHandler.withRetry(async () => {
      const response = await this.calendar.calendarList.list({
        minAccessRole: 'writer' // Only calendars the user can write to
      })

      return response.data.items?.map(calendar => ({
        id: calendar.id,
        name: calendar.summary,
        description: calendar.description,
        primary: calendar.primary || false,
        accessRole: calendar.accessRole,
        backgroundColor: calendar.backgroundColor,
        foregroundColor: calendar.foregroundColor
      })) || []
    })
  }

  /**
   * Get events from a specific calendar
   */
  async getEvents(calendarId = 'primary', timeMin = null, timeMax = null, maxResults = 250) {
    if (!this.calendar) {
      throw new Error('Google Calendar client not initialized')
    }

    return await RetryHandler.withRetry(async () => {
      const params = {
        calendarId,
        maxResults,
        singleEvents: true,
        orderBy: 'startTime'
      }

      if (timeMin) {
        params.timeMin = timeMin instanceof Date ? timeMin.toISOString() : timeMin
      }

      if (timeMax) {
        params.timeMax = timeMax instanceof Date ? timeMax.toISOString() : timeMax
      }

      const response = await this.calendar.events.list(params)

      return response.data.items?.map(event => this.formatEvent(event)) || []
    })
  }

  /**
   * Create a new event in Google Calendar
   */
  async createEvent(calendarId = 'primary', eventData) {
    if (!this.calendar) {
      throw new Error('Google Calendar client not initialized')
    }

    return await RetryHandler.withRetry(async () => {
      const googleEvent = this.formatEventForGoogle(eventData)
      
      const response = await this.calendar.events.insert({
        calendarId,
        resource: googleEvent
      })

      await AuditLogger.logIntegrationActivity(
        this.userId,
        'google_calendar',
        'event_created',
        'success',
        { 
          eventId: response.data.id,
          calendarId,
          summary: eventData.summary
        }
      )

      return this.formatEvent(response.data)
    })
  }

  /**
   * Update an existing event in Google Calendar
   */
  async updateEvent(calendarId = 'primary', eventId, eventData) {
    if (!this.calendar) {
      throw new Error('Google Calendar client not initialized')
    }

    return await RetryHandler.withRetry(async () => {
      const googleEvent = this.formatEventForGoogle(eventData)
      
      const response = await this.calendar.events.update({
        calendarId,
        eventId,
        resource: googleEvent
      })

      await AuditLogger.logIntegrationActivity(
        this.userId,
        'google_calendar',
        'event_updated',
        'success',
        { 
          eventId,
          calendarId,
          summary: eventData.summary
        }
      )

      return this.formatEvent(response.data)
    })
  }

  /**
   * Delete an event from Google Calendar
   */
  async deleteEvent(calendarId = 'primary', eventId) {
    if (!this.calendar) {
      throw new Error('Google Calendar client not initialized')
    }

    return await RetryHandler.withRetry(async () => {
      await this.calendar.events.delete({
        calendarId,
        eventId
      })

      await AuditLogger.logIntegrationActivity(
        this.userId,
        'google_calendar',
        'event_deleted',
        'success',
        { eventId, calendarId }
      )

      return true
    })
  }

  /**
   * Check for conflicts with existing events
   */
  async checkConflicts(calendarId = 'primary', startTime, endTime, excludeEventId = null) {
    if (!this.calendar) {
      throw new Error('Google Calendar client not initialized')
    }

    const events = await this.getEvents(calendarId, startTime, endTime)
    
    return events.filter(event => {
      if (excludeEventId && event.id === excludeEventId) {
        return false
      }

      const eventStart = new Date(event.start)
      const eventEnd = new Date(event.end)
      const checkStart = new Date(startTime)
      const checkEnd = new Date(endTime)

      // Check for overlap
      return (checkStart < eventEnd && checkEnd > eventStart)
    })
  }

  /**
   * Get free/busy information
   */
  async getFreeBusy(calendarIds, timeMin, timeMax) {
    if (!this.calendar) {
      throw new Error('Google Calendar client not initialized')
    }

    return await RetryHandler.withRetry(async () => {
      const response = await this.calendar.freebusy.query({
        resource: {
          timeMin: timeMin instanceof Date ? timeMin.toISOString() : timeMin,
          timeMax: timeMax instanceof Date ? timeMax.toISOString() : timeMax,
          items: calendarIds.map(id => ({ id }))
        }
      })

      return response.data.calendars
    })
  }

  /**
   * Format Google Calendar event to our standard format
   */
  formatEvent(googleEvent) {
    return {
      id: googleEvent.id,
      summary: googleEvent.summary || 'Untitled Event',
      description: googleEvent.description || '',
      start: googleEvent.start?.dateTime || googleEvent.start?.date,
      end: googleEvent.end?.dateTime || googleEvent.end?.date,
      allDay: !googleEvent.start?.dateTime,
      location: googleEvent.location || '',
      attendees: googleEvent.attendees?.map(attendee => ({
        email: attendee.email,
        name: attendee.displayName,
        status: attendee.responseStatus
      })) || [],
      creator: {
        email: googleEvent.creator?.email,
        name: googleEvent.creator?.displayName
      },
      organizer: {
        email: googleEvent.organizer?.email,
        name: googleEvent.organizer?.displayName
      },
      status: googleEvent.status,
      visibility: googleEvent.visibility,
      created: googleEvent.created,
      updated: googleEvent.updated,
      htmlLink: googleEvent.htmlLink,
      source: 'google_calendar'
    }
  }

  /**
   * Format our event data for Google Calendar API
   */
  formatEventForGoogle(eventData) {
    const googleEvent = {
      summary: eventData.summary || eventData.title,
      description: eventData.description || '',
      location: eventData.location || ''
    }

    // Handle date/time formatting
    if (eventData.allDay) {
      googleEvent.start = { date: eventData.start.split('T')[0] }
      googleEvent.end = { date: eventData.end.split('T')[0] }
    } else {
      googleEvent.start = { 
        dateTime: eventData.start,
        timeZone: eventData.timeZone || 'Australia/Melbourne'
      }
      googleEvent.end = { 
        dateTime: eventData.end,
        timeZone: eventData.timeZone || 'Australia/Melbourne'
      }
    }

    // Add attendees if provided
    if (eventData.attendees && eventData.attendees.length > 0) {
      googleEvent.attendees = eventData.attendees.map(attendee => ({
        email: attendee.email,
        displayName: attendee.name
      }))
    }

    // Add reminders
    if (eventData.reminders) {
      googleEvent.reminders = {
        useDefault: false,
        overrides: eventData.reminders.map(reminder => ({
          method: reminder.method || 'email',
          minutes: reminder.minutes || 15
        }))
      }
    }

    return googleEvent
  }

  /**
   * Test connection to Google Calendar
   */
  async testConnection() {
    try {
      if (!await this.initialize()) {
        return { success: false, error: 'Failed to initialize Google Calendar client' }
      }

      // Try to get calendar list as a test
      const calendars = await this.getCalendars()
      
      await AuditLogger.logIntegrationActivity(
        this.userId,
        'google_calendar',
        'connection_test',
        'success',
        { calendarCount: calendars.length }
      )

      return { 
        success: true, 
        calendars: calendars.length,
        message: 'Google Calendar connection successful'
      }
    } catch (error) {
      await AuditLogger.logIntegrationActivity(
        this.userId,
        'google_calendar',
        'connection_test',
        'error',
        { error: error.message }
      )

      return { 
        success: false, 
        error: error.message 
      }
    }
  }
}

export default GoogleCalendarClient
