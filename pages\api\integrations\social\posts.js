/**
 * Social Media Posts API Endpoint for Ocean Soul Sparkles
 * Handles social media post operations and management
 * 
 * Phase 7.3: Social Media Integration Layer
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { integrationRateLimit } from '@/lib/integrations/rate-limiter'
import { RequestValidator, AuditLogger } from '@/lib/integrations/security-utils'
import SocialMediaManager from '@/lib/integrations/social/social-manager'

/**
 * Social Media Posts Handler
 * GET /api/integrations/social/posts - Get posts from connected social media accounts
 * POST /api/integrations/social/posts - Create new post on social media
 */
export default async function handler(req, res) {
  // Apply rate limiting
  await new Promise((resolve, reject) => {
    integrationRateLimit(req, res, (error) => {
      if (error) reject(error)
      else resolve()
    })
  })

  const startTime = Date.now()

  try {
    // Validate request method
    RequestValidator.validateEndpointAccess(req, ['GET', 'POST'])

    // Authenticate user
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    const { user } = authResult
    const userId = user.id

    // Initialize social media manager
    const socialManager = new SocialMediaManager(userId)

    if (req.method === 'GET') {
      // Get posts from social media accounts
      const { limit = 25 } = req.query

      const posts = await socialManager.getAllPosts(parseInt(limit))

      await AuditLogger.logApiAccess(
        userId,
        req.url,
        req.method,
        200,
        Date.now() - startTime,
        { 
          action: 'get_social_posts',
          postCount: posts.length,
          limit: parseInt(limit)
        }
      )

      return res.status(200).json({
        success: true,
        posts,
        count: posts.length
      })

    } else if (req.method === 'POST') {
      // Create new post on social media
      const { providers, postData } = req.body

      if (!providers || !Array.isArray(providers) || providers.length === 0) {
        return res.status(400).json({
          error: 'Missing providers',
          message: 'At least one provider must be specified'
        })
      }

      if (!postData || !postData.message) {
        return res.status(400).json({
          error: 'Missing post data',
          message: 'Post message is required'
        })
      }

      // Validate post data
      const validationResult = validatePostData(postData)
      if (!validationResult.valid) {
        return res.status(400).json({
          error: 'Invalid post data',
          message: validationResult.message
        })
      }

      // Create post on multiple providers
      const results = await socialManager.createMultiProviderPost(providers, postData)

      const successCount = results.filter(r => r.success).length
      const totalCount = results.length

      await AuditLogger.logIntegrationActivity(
        userId,
        'social_media',
        'multi_post_created',
        successCount === totalCount ? 'success' : 'warning',
        {
          providers,
          successCount,
          totalCount,
          results
        }
      )

      await AuditLogger.logApiAccess(
        userId,
        req.url,
        req.method,
        200,
        Date.now() - startTime,
        { 
          action: 'create_social_post',
          providers,
          successCount,
          totalCount
        }
      )

      return res.status(200).json({
        success: true,
        message: `Post created on ${successCount} of ${totalCount} platform(s)`,
        results
      })
    }

  } catch (error) {
    console.error('Social media posts error:', error)

    await AuditLogger.logSecurityEvent(
      req.user?.id || null,
      'social_posts_error',
      {
        error: error.message,
        stack: error.stack,
        endpoint: req.url,
        method: req.method,
        userAgent: req.headers['user-agent'],
        ipAddress: req.headers['x-forwarded-for'] || req.connection?.remoteAddress
      },
      'error'
    )

    await AuditLogger.logApiAccess(
      req.user?.id || null,
      req.url,
      req.method,
      500,
      Date.now() - startTime,
      { error: error.message }
    )

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to perform social media post operation'
    })
  }
}

/**
 * Validate post data
 */
function validatePostData(postData) {
  // Check message length
  if (!postData.message || postData.message.trim().length === 0) {
    return {
      valid: false,
      message: 'Post message is required'
    }
  }

  if (postData.message.length > 2200) {
    return {
      valid: false,
      message: 'Post message must be 2200 characters or less'
    }
  }

  // Validate image URL if provided
  if (postData.imageUrl) {
    try {
      new URL(postData.imageUrl)
    } catch {
      return {
        valid: false,
        message: 'Invalid image URL format'
      }
    }
  }

  // Validate scheduled time if provided
  if (postData.scheduledTime) {
    const scheduledDate = new Date(postData.scheduledTime)
    if (isNaN(scheduledDate.getTime())) {
      return {
        valid: false,
        message: 'Invalid scheduled time format'
      }
    }

    if (scheduledDate <= new Date()) {
      return {
        valid: false,
        message: 'Scheduled time must be in the future'
      }
    }
  }

  return { valid: true }
}

/**
 * API Route Configuration
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
