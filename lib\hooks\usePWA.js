/**
 * PWA Hook for Ocean Soul Sparkles
 * Manages Progressive Web App functionality including installation, offline status, and background sync
 */

import { useState, useEffect, useCallback } from 'react'
import { toast } from 'react-toastify'

export function usePWA() {
  const [isOnline, setIsOnline] = useState(true)
  const [isInstallable, setIsInstallable] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)
  const [deferredPrompt, setDeferredPrompt] = useState(null)
  const [serviceWorkerRegistration, setServiceWorkerRegistration] = useState(null)
  const [updateAvailable, setUpdateAvailable] = useState(false)

  // Initialize PWA functionality
  useEffect(() => {
    if (typeof window === 'undefined') return

    // Check initial online status
    setIsOnline(navigator.onLine)

    // Check if app is already installed
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches
    const isInWebAppiOS = window.navigator.standalone === true
    setIsInstalled(isStandalone || isInWebAppiOS)

    // Register service worker
    registerServiceWorker()

    // Set up event listeners
    const handleOnline = () => {
      setIsOnline(true)
      toast.success('Connection restored! 🌐')
    }

    const handleOffline = () => {
      setIsOnline(false)
      toast.warning('You are now offline. Some features may be limited.')
    }

    const handleBeforeInstallPrompt = (e) => {
      e.preventDefault()
      setDeferredPrompt(e)
      setIsInstallable(true)
    }

    const handleAppInstalled = () => {
      setIsInstalled(true)
      setIsInstallable(false)
      setDeferredPrompt(null)
      toast.success('Ocean Soul Sparkles installed successfully! 🎉')
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [])

  // Register service worker
  const registerServiceWorker = useCallback(async () => {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js', {
          scope: '/'
        })

        setServiceWorkerRegistration(registration)

        // Check for updates
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing
          
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              setUpdateAvailable(true)
              toast.info('App update available! Click to refresh.', {
                onClick: () => updateApp(),
                autoClose: false
              })
            }
          })
        })

        console.log('[PWA] Service Worker registered successfully')
      } catch (error) {
        console.error('[PWA] Service Worker registration failed:', error)
      }
    }
  }, [])

  // Install PWA
  const installApp = useCallback(async () => {
    if (!deferredPrompt) {
      toast.error('Installation not available')
      return false
    }

    try {
      deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice
      
      if (outcome === 'accepted') {
        setIsInstallable(false)
        setDeferredPrompt(null)
        return true
      } else {
        toast.info('Installation cancelled')
        return false
      }
    } catch (error) {
      console.error('[PWA] Installation failed:', error)
      toast.error('Installation failed')
      return false
    }
  }, [deferredPrompt])

  // Update app
  const updateApp = useCallback(() => {
    if (serviceWorkerRegistration?.waiting) {
      serviceWorkerRegistration.waiting.postMessage({ type: 'SKIP_WAITING' })
      window.location.reload()
    }
  }, [serviceWorkerRegistration])

  // Request background sync
  const requestBackgroundSync = useCallback(async (tag) => {
    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      try {
        const registration = await navigator.serviceWorker.ready
        await registration.sync.register(tag)
        console.log(`[PWA] Background sync registered: ${tag}`)
        return true
      } catch (error) {
        console.error(`[PWA] Background sync failed: ${tag}`, error)
        return false
      }
    }
    return false
  }, [])

  // Check if feature is supported
  const isFeatureSupported = useCallback((feature) => {
    switch (feature) {
      case 'serviceWorker':
        return 'serviceWorker' in navigator
      case 'backgroundSync':
        return 'serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype
      case 'pushNotifications':
        return 'serviceWorker' in navigator && 'PushManager' in window
      case 'camera':
        return 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices
      case 'geolocation':
        return 'geolocation' in navigator
      case 'vibration':
        return 'vibrate' in navigator
      case 'share':
        return 'share' in navigator
      case 'clipboard':
        return 'clipboard' in navigator
      default:
        return false
    }
  }, [])

  // Share content using Web Share API
  const shareContent = useCallback(async (shareData) => {
    if (isFeatureSupported('share')) {
      try {
        await navigator.share(shareData)
        return true
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('[PWA] Share failed:', error)
        }
        return false
      }
    }
    
    // Fallback to clipboard
    if (isFeatureSupported('clipboard') && shareData.url) {
      try {
        await navigator.clipboard.writeText(shareData.url)
        toast.success('Link copied to clipboard!')
        return true
      } catch (error) {
        console.error('[PWA] Clipboard write failed:', error)
        return false
      }
    }
    
    return false
  }, [isFeatureSupported])

  // Get device capabilities
  const getDeviceCapabilities = useCallback(() => {
    return {
      isOnline,
      isInstalled,
      isInstallable,
      updateAvailable,
      features: {
        serviceWorker: isFeatureSupported('serviceWorker'),
        backgroundSync: isFeatureSupported('backgroundSync'),
        pushNotifications: isFeatureSupported('pushNotifications'),
        camera: isFeatureSupported('camera'),
        geolocation: isFeatureSupported('geolocation'),
        vibration: isFeatureSupported('vibration'),
        share: isFeatureSupported('share'),
        clipboard: isFeatureSupported('clipboard')
      }
    }
  }, [isOnline, isInstalled, isInstallable, updateAvailable, isFeatureSupported])

  // Cache important data for offline use
  const cacheForOffline = useCallback(async (data, key) => {
    if ('caches' in window) {
      try {
        const cache = await caches.open('oss-offline-data')
        const response = new Response(JSON.stringify(data), {
          headers: { 'Content-Type': 'application/json' }
        })
        await cache.put(`/offline-data/${key}`, response)
        console.log(`[PWA] Cached data for offline: ${key}`)
        return true
      } catch (error) {
        console.error('[PWA] Failed to cache data:', error)
        return false
      }
    }
    return false
  }, [])

  // Retrieve cached data when offline
  const getCachedData = useCallback(async (key) => {
    if ('caches' in window) {
      try {
        const cache = await caches.open('oss-offline-data')
        const response = await cache.match(`/offline-data/${key}`)
        if (response) {
          const data = await response.json()
          return data
        }
      } catch (error) {
        console.error('[PWA] Failed to retrieve cached data:', error)
      }
    }
    return null
  }, [])

  return {
    // Status
    isOnline,
    isInstalled,
    isInstallable,
    updateAvailable,
    
    // Actions
    installApp,
    updateApp,
    requestBackgroundSync,
    shareContent,
    cacheForOffline,
    getCachedData,
    
    // Utilities
    isFeatureSupported,
    getDeviceCapabilities,
    
    // Service Worker
    serviceWorkerRegistration
  }
}
