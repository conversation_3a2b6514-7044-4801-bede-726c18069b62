import { supabase } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';
import { 
  DynamicPricingEngine, 
  MarketAnalysisEngine, 
  PriceElasticityCalculator 
} from '@/lib/analytics/pricing-optimization';

/**
 * Pricing Recommendations API Endpoint
 * Provides intelligent pricing optimization and market analysis
 */
export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });
  }

  try {
    // Authenticate request
    const authResult = await authenticateAdminRequest(req);
    if (!authResult.success) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const { 
      service = 'all', 
      strategy = 'balanced',
      timeframe = 'monthly'
    } = req.query;

    // Get services data
    const servicesData = await getServicesData(service);
    
    // Get market data for analysis
    const marketData = await getMarketData(timeframe);
    
    // Get historical pricing and demand data
    const historicalData = await getHistoricalPricingData(service, timeframe);

    // Initialize pricing engines
    const pricingEngine = new DynamicPricingEngine({
      basePriceMultiplier: getStrategyMultiplier(strategy)
    });
    const marketAnalyzer = new MarketAnalysisEngine();
    const elasticityCalculator = new PriceElasticityCalculator();

    // Generate pricing recommendations for each service
    const recommendations = [];
    for (const serviceData of servicesData) {
      const recommendation = await generateServicePricingRecommendation(
        serviceData,
        marketData,
        historicalData,
        pricingEngine,
        strategy
      );
      recommendations.push(recommendation);
    }

    // Perform market analysis
    const marketAnalysis = await performMarketAnalysis(servicesData, marketData, marketAnalyzer);

    // Calculate price elasticity
    const elasticityAnalysis = await calculatePriceElasticity(historicalData, elasticityCalculator);

    // Generate pricing insights
    const insights = generatePricingInsights(recommendations, marketAnalysis, elasticityAnalysis);

    // Generate price optimization data
    const priceOptimization = generatePriceOptimizationData(servicesData, historicalData);

    return res.status(200).json({
      success: true,
      data: {
        services: servicesData,
        recommendations,
        marketAnalysis,
        elasticityAnalysis,
        priceOptimization,
        insights,
        strategy,
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Pricing recommendations error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to generate pricing recommendations: ' + error.message
    });
  }
}

/**
 * Get services data from database
 */
async function getServicesData(serviceFilter) {
  try {
    let query = supabase
      .from('services')
      .select(`
        id,
        name,
        description,
        price,
        duration,
        is_active,
        created_at,
        updated_at
      `)
      .eq('is_active', true);

    if (serviceFilter !== 'all') {
      query = query.eq('id', serviceFilter);
    }

    const { data: services, error } = await query;
    if (error) throw error;

    return services || [];

  } catch (error) {
    console.error('Error fetching services data:', error);
    throw error;
  }
}

/**
 * Get market data for analysis
 */
async function getMarketData(timeframe) {
  try {
    const dateRange = calculateDateRange(timeframe);

    // Get booking data for market analysis
    const { data: bookings, error: bookingError } = await supabase
      .from('bookings')
      .select(`
        start_time,
        total_amount,
        status,
        service_id,
        services(name, price)
      `)
      .gte('start_time', dateRange.start)
      .lte('start_time', dateRange.end)
      .order('start_time', { ascending: true });

    if (bookingError) throw bookingError;

    // Get competitor data (simulated for demo - in real implementation, this would come from market research)
    const competitorData = generateCompetitorData();

    return {
      bookings: bookings || [],
      competitors: competitorData,
      dateRange
    };

  } catch (error) {
    console.error('Error fetching market data:', error);
    throw error;
  }
}

/**
 * Get historical pricing and demand data
 */
async function getHistoricalPricingData(serviceFilter, timeframe) {
  try {
    const dateRange = calculateDateRange(timeframe);

    let query = supabase
      .from('bookings')
      .select(`
        start_time,
        total_amount,
        status,
        service_id,
        services(name, price)
      `)
      .gte('start_time', dateRange.start)
      .lte('start_time', dateRange.end)
      .order('start_time', { ascending: true });

    if (serviceFilter !== 'all') {
      query = query.eq('service_id', serviceFilter);
    }

    const { data: bookings, error } = await query;
    if (error) throw error;

    // Process data into time series format
    const processedData = processHistoricalData(bookings || []);

    return processedData;

  } catch (error) {
    console.error('Error fetching historical pricing data:', error);
    throw error;
  }
}

/**
 * Generate pricing recommendation for a service
 */
async function generateServicePricingRecommendation(serviceData, marketData, historicalData, pricingEngine, strategy) {
  try {
    // Calculate demand level based on recent bookings
    const recentBookings = marketData.bookings.filter(b => 
      b.service_id === serviceData.id && 
      new Date(b.start_time) >= new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    );

    const demandLevel = Math.min(2.0, Math.max(0.5, recentBookings.length / 10)); // Normalize demand

    // Get competitor pricing for this service type
    const competitionPricing = marketData.competitors
      .filter(c => c.service.toLowerCase().includes(serviceData.name.toLowerCase().split(' ')[0]))
      .map(c => c.price);

    // Calculate pricing factors
    const factors = {
      demandLevel,
      competitionPricing,
      seasonalMultiplier: calculateSeasonalMultiplier(),
      artistSkillLevel: 3, // Default skill level (would come from artist data)
      timeToEvent: 14, // Average time to booking
      historicalConversionRate: calculateConversionRate(recentBookings),
      inventoryLevel: 0.5 // Default inventory level
    };

    // Generate optimal pricing
    const pricingResult = pricingEngine.calculateOptimalPrice(serviceData.price, factors);

    // Calculate expected impact
    const expectedImpact = calculateExpectedImpact(serviceData.price, pricingResult.optimizedPrice, demandLevel);

    return {
      serviceId: serviceData.id,
      service: serviceData.name,
      currentPrice: serviceData.price,
      recommendedPrice: pricingResult.optimizedPrice,
      priceChange: (pricingResult.optimizedPrice - serviceData.price) / serviceData.price,
      expectedImpact: expectedImpact.revenueChange,
      confidence: pricingResult.confidence,
      priority: determinePriority(Math.abs(pricingResult.optimizedPrice - serviceData.price) / serviceData.price),
      factors: pricingResult.adjustmentFactors,
      reasoning: generatePricingReasoning(pricingResult, factors, strategy)
    };

  } catch (error) {
    console.error('Error generating service pricing recommendation:', error);
    return {
      serviceId: serviceData.id,
      service: serviceData.name,
      currentPrice: serviceData.price,
      recommendedPrice: serviceData.price,
      priceChange: 0,
      expectedImpact: 0,
      confidence: 0.5,
      priority: 'low',
      error: error.message
    };
  }
}

/**
 * Perform comprehensive market analysis
 */
async function performMarketAnalysis(servicesData, marketData, marketAnalyzer) {
  try {
    const analysis = {
      competitorAnalysis: {
        competitors: marketData.competitors,
        averagePricing: {},
        marketPosition: {}
      },
      marketPosition: {
        priceCompetitiveness: 0,
        qualityRating: 0,
        marketShare: 0,
        customerSatisfaction: 0,
        brandRecognition: 0,
        serviceAvailability: 0
      },
      opportunities: [],
      threats: []
    };

    // Calculate average pricing by service category
    const serviceCategories = [...new Set(servicesData.map(s => s.name.split(' ')[0]))];
    
    serviceCategories.forEach(category => {
      const categoryServices = servicesData.filter(s => s.name.includes(category));
      const categoryCompetitors = marketData.competitors.filter(c => c.service.includes(category));
      
      const avgOurPrice = categoryServices.reduce((sum, s) => sum + s.price, 0) / categoryServices.length;
      const avgCompetitorPrice = categoryCompetitors.reduce((sum, c) => sum + c.price, 0) / categoryCompetitors.length;
      
      analysis.competitorAnalysis.averagePricing[category] = {
        our_average: avgOurPrice,
        competitor_average: avgCompetitorPrice,
        price_advantage: avgCompetitorPrice > 0 ? (avgCompetitorPrice - avgOurPrice) / avgCompetitorPrice : 0
      };
    });

    // Calculate market position metrics (simplified)
    analysis.marketPosition = {
      priceCompetitiveness: 75, // Simulated - would be calculated from actual market data
      qualityRating: 85,
      marketShare: 15,
      customerSatisfaction: 88,
      brandRecognition: 65,
      serviceAvailability: 90
    };

    // Identify opportunities and threats
    Object.entries(analysis.competitorAnalysis.averagePricing).forEach(([category, pricing]) => {
      if (pricing.price_advantage > 0.1) {
        analysis.opportunities.push({
          type: 'pricing',
          category,
          description: `${category} services are priced 10%+ below market average`,
          potential: 'Price increase opportunity'
        });
      } else if (pricing.price_advantage < -0.1) {
        analysis.threats.push({
          type: 'pricing',
          category,
          description: `${category} services are priced 10%+ above market average`,
          risk: 'Potential customer loss to competitors'
        });
      }
    });

    return analysis;

  } catch (error) {
    console.error('Error performing market analysis:', error);
    return {
      competitorAnalysis: { competitors: [], averagePricing: {}, marketPosition: {} },
      marketPosition: {},
      opportunities: [],
      threats: []
    };
  }
}

/**
 * Calculate price elasticity
 */
async function calculatePriceElasticity(historicalData, elasticityCalculator) {
  try {
    if (!historicalData.priceHistory || !historicalData.demandHistory || 
        historicalData.priceHistory.length < 3) {
      return {
        elasticity: -0.8, // Default elasticity
        interpretation: {
          type: 'inelastic',
          description: 'Insufficient data for accurate elasticity calculation',
          recommendation: 'Collect more pricing data for better analysis'
        },
        confidence: 0.3
      };
    }

    const elasticityResult = elasticityCalculator.calculateElasticity(
      historicalData.priceHistory,
      historicalData.demandHistory
    );

    return {
      ...elasticityResult,
      priceHistory: historicalData.priceHistory,
      demandHistory: historicalData.demandHistory
    };

  } catch (error) {
    console.error('Error calculating price elasticity:', error);
    return {
      elasticity: -0.8,
      interpretation: {
        type: 'unknown',
        description: 'Error calculating elasticity',
        recommendation: 'Review pricing data quality'
      },
      confidence: 0.1
    };
  }
}

/**
 * Helper functions
 */
function calculateDateRange(timeframe) {
  const now = new Date();
  let start;

  switch (timeframe) {
    case 'weekly':
      start = new Date(now.getTime() - 12 * 7 * 24 * 60 * 60 * 1000); // 12 weeks
      break;
    case 'monthly':
      start = new Date(now.getFullYear(), now.getMonth() - 6, 1); // 6 months
      break;
    case 'quarterly':
      start = new Date(now.getFullYear() - 1, now.getMonth(), 1); // 1 year
      break;
    default:
      start = new Date(now.getFullYear(), now.getMonth() - 3, 1); // 3 months
  }

  return {
    start: start.toISOString(),
    end: now.toISOString()
  };
}

function getStrategyMultiplier(strategy) {
  const multipliers = {
    'revenue_max': 1.2,
    'market_share': 0.9,
    'balanced': 1.0,
    'premium': 1.3
  };
  return multipliers[strategy] || 1.0;
}

function calculateSeasonalMultiplier() {
  const month = new Date().getMonth();
  // Simplified seasonal adjustment (would be more sophisticated in real implementation)
  const seasonalFactors = [0.9, 0.9, 1.0, 1.1, 1.2, 1.3, 1.3, 1.2, 1.1, 1.0, 0.9, 1.1];
  return seasonalFactors[month];
}

function calculateConversionRate(bookings) {
  // Simplified conversion rate calculation
  const completedBookings = bookings.filter(b => b.status === 'completed').length;
  return bookings.length > 0 ? completedBookings / bookings.length : 0.7;
}

function calculateExpectedImpact(currentPrice, newPrice, demandLevel) {
  const priceChange = (newPrice - currentPrice) / currentPrice;
  const elasticity = -0.8; // Simplified elasticity assumption
  const demandChange = elasticity * priceChange;
  const revenueChange = priceChange + demandChange + (priceChange * demandChange);
  
  return {
    priceChange,
    demandChange,
    revenueChange
  };
}

function determinePriority(priceChangePercent) {
  if (priceChangePercent > 0.15) return 'high';
  if (priceChangePercent > 0.05) return 'medium';
  return 'low';
}

function generatePricingReasoning(pricingResult, factors, strategy) {
  const reasons = [];
  
  if (factors.demandLevel > 1.2) {
    reasons.push('High demand detected');
  }
  if (factors.competitionPricing.length > 0) {
    reasons.push('Competitive pricing analysis applied');
  }
  if (Math.abs(pricingResult.totalAdjustment) > 0.1) {
    reasons.push(`${strategy} strategy optimization`);
  }
  
  return reasons.join(', ') || 'Standard pricing optimization';
}

function processHistoricalData(bookings) {
  // Process bookings into price and demand history
  const monthlyData = {};
  
  bookings.forEach(booking => {
    const month = booking.start_time.substring(0, 7); // YYYY-MM
    if (!monthlyData[month]) {
      monthlyData[month] = { bookings: 0, revenue: 0, prices: [] };
    }
    monthlyData[month].bookings++;
    monthlyData[month].revenue += booking.total_amount || 0;
    if (booking.services?.price) {
      monthlyData[month].prices.push(booking.services.price);
    }
  });

  const months = Object.keys(monthlyData).sort();
  const priceHistory = months.map(month => {
    const prices = monthlyData[month].prices;
    return prices.length > 0 ? prices.reduce((sum, p) => sum + p, 0) / prices.length : 0;
  });
  const demandHistory = months.map(month => monthlyData[month].bookings);

  return {
    priceHistory,
    demandHistory,
    months,
    monthlyData
  };
}

function generateCompetitorData() {
  // Simulated competitor data - in real implementation, this would come from market research APIs
  return [
    { name: 'Competitor A', service: 'Braiding', price: 120, rating: 4.2, bookings: 45 },
    { name: 'Competitor B', service: 'Braiding', price: 95, rating: 3.8, bookings: 32 },
    { name: 'Competitor C', service: 'Styling', price: 85, rating: 4.5, bookings: 28 },
    { name: 'Competitor D', service: 'Styling', price: 110, rating: 4.0, bookings: 38 },
    { name: 'Competitor E', service: 'Consultation', price: 50, rating: 4.3, bookings: 15 }
  ];
}

function generatePricingInsights(recommendations, marketAnalysis, elasticityAnalysis) {
  const insights = [];

  // High-impact recommendations
  const highPriorityRecs = recommendations.filter(r => r.priority === 'high');
  if (highPriorityRecs.length > 0) {
    insights.push({
      type: 'opportunity',
      title: 'High-Impact Pricing Opportunities',
      description: `${highPriorityRecs.length} services have significant pricing optimization potential.`,
      recommendation: 'Prioritize implementing these pricing changes for maximum revenue impact.',
      impact: 'Potential 15-25% revenue increase'
    });
  }

  // Market position insights
  if (marketAnalysis.marketPosition?.priceCompetitiveness < 60) {
    insights.push({
      type: 'warning',
      title: 'Price Competitiveness Below Market',
      description: 'Current pricing may be too high compared to market standards.',
      recommendation: 'Consider strategic price reductions to improve market position.',
      impact: 'Improved market share and customer acquisition'
    });
  }

  // Elasticity insights
  if (elasticityAnalysis.elasticity && Math.abs(elasticityAnalysis.elasticity) < 1) {
    insights.push({
      type: 'opportunity',
      title: 'Inelastic Demand Detected',
      description: 'Customers are not highly sensitive to price changes.',
      recommendation: 'Consider price increases to maximize revenue without significant demand loss.',
      impact: 'Revenue optimization opportunity'
    });
  }

  return insights;
}

function generatePriceOptimizationData(servicesData, historicalData) {
  // Generate price optimization visualization data
  const baseService = servicesData[0];
  if (!baseService) return {};

  const currentPrice = baseService.price;
  const pricePoints = [];
  const expectedRevenues = [];
  const expectedDemands = [];

  // Generate price points from 50% to 150% of current price
  for (let multiplier = 0.5; multiplier <= 1.5; multiplier += 0.1) {
    const price = currentPrice * multiplier;
    const priceChange = (price - currentPrice) / currentPrice;
    const elasticity = -0.8; // Simplified elasticity
    const demandChange = elasticity * priceChange;
    const newDemand = Math.max(0, 100 * (1 + demandChange)); // Base demand of 100
    const revenue = price * newDemand;

    pricePoints.push(Math.round(price));
    expectedRevenues.push(Math.round(revenue));
    expectedDemands.push(Math.round(newDemand));
  }

  return {
    pricePoints,
    expectedRevenues,
    expectedDemands,
    optimalPriceIndex: expectedRevenues.indexOf(Math.max(...expectedRevenues))
  };
}
