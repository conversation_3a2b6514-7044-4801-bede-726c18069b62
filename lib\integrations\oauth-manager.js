/**
 * OAuth 2.0 Manager for Ocean Soul Sparkles Integrations
 * Centralized OAuth management for third-party integrations
 * 
 * Phase 7: Advanced Integrations & Ecosystem
 */

import { createClient } from '@supabase/supabase-js'
import crypto from 'crypto'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * OAuth Provider Configurations
 */
const OAUTH_PROVIDERS = {
  google_calendar: {
    name: 'Google Calendar',
    authUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
    tokenUrl: 'https://oauth2.googleapis.com/token',
    scopes: [
      'https://www.googleapis.com/auth/calendar',
      'https://www.googleapis.com/auth/calendar.events'
    ],
    clientId: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET
  },
  instagram_business: {
    name: 'Instagram Business',
    authUrl: 'https://api.instagram.com/oauth/authorize',
    tokenUrl: 'https://api.instagram.com/oauth/access_token',
    scopes: [
      'instagram_basic',
      'instagram_content_publish',
      'pages_show_list',
      'pages_read_engagement'
    ],
    clientId: process.env.INSTAGRAM_CLIENT_ID,
    clientSecret: process.env.INSTAGRAM_CLIENT_SECRET
  },
  facebook_business: {
    name: 'Facebook Business',
    authUrl: 'https://www.facebook.com/v18.0/dialog/oauth',
    tokenUrl: 'https://graph.facebook.com/v18.0/oauth/access_token',
    scopes: [
      'pages_manage_posts',
      'pages_read_engagement',
      'business_management',
      'pages_show_list'
    ],
    clientId: process.env.FACEBOOK_CLIENT_ID,
    clientSecret: process.env.FACEBOOK_CLIENT_SECRET
  },
  quickbooks: {
    name: 'QuickBooks',
    authUrl: 'https://appcenter.intuit.com/connect/oauth2',
    tokenUrl: 'https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer',
    scopes: ['com.intuit.quickbooks.accounting'],
    clientId: process.env.QUICKBOOKS_CLIENT_ID,
    clientSecret: process.env.QUICKBOOKS_CLIENT_SECRET
  },
  mailchimp: {
    name: 'Mailchimp',
    authUrl: 'https://login.mailchimp.com/oauth2/authorize',
    tokenUrl: 'https://login.mailchimp.com/oauth2/token',
    scopes: [],
    clientId: process.env.MAILCHIMP_CLIENT_ID,
    clientSecret: process.env.MAILCHIMP_CLIENT_SECRET
  }
}

/**
 * OAuth Manager Class
 * Handles OAuth flows, token management, and security
 */
export class OAuthManager {
  constructor() {
    this.encryptionKey = process.env.INTEGRATION_ENCRYPTION_KEY || this.generateEncryptionKey()
  }

  /**
   * Generate encryption key for token storage
   */
  generateEncryptionKey() {
    return crypto.randomBytes(32).toString('hex')
  }

  /**
   * Encrypt sensitive data
   */
  encrypt(text) {
    const algorithm = 'aes-256-gcm'
    const key = Buffer.from(this.encryptionKey, 'hex')
    const iv = crypto.randomBytes(16)
    
    const cipher = crypto.createCipher(algorithm, key)
    cipher.setAAD(Buffer.from('ocean-soul-sparkles', 'utf8'))
    
    let encrypted = cipher.update(text, 'utf8', 'hex')
    encrypted += cipher.final('hex')
    
    const authTag = cipher.getAuthTag()
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex')
    }
  }

  /**
   * Decrypt sensitive data
   */
  decrypt(encryptedData) {
    const algorithm = 'aes-256-gcm'
    const key = Buffer.from(this.encryptionKey, 'hex')
    
    const decipher = crypto.createDecipher(algorithm, key)
    decipher.setAAD(Buffer.from('ocean-soul-sparkles', 'utf8'))
    decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'))
    
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8')
    decrypted += decipher.final('utf8')
    
    return decrypted
  }

  /**
   * Generate OAuth authorization URL
   */
  generateAuthUrl(provider, userId, redirectUri) {
    const config = OAUTH_PROVIDERS[provider]
    if (!config) {
      throw new Error(`Unsupported OAuth provider: ${provider}`)
    }

    const state = this.generateState(userId, provider)
    const params = new URLSearchParams({
      client_id: config.clientId,
      redirect_uri: redirectUri,
      scope: config.scopes.join(' '),
      response_type: 'code',
      state,
      access_type: 'offline', // For refresh tokens
      prompt: 'consent'
    })

    return `${config.authUrl}?${params.toString()}`
  }

  /**
   * Generate secure state parameter
   */
  generateState(userId, provider) {
    const timestamp = Date.now()
    const random = crypto.randomBytes(16).toString('hex')
    const data = JSON.stringify({ userId, provider, timestamp, random })
    
    return Buffer.from(data).toString('base64url')
  }

  /**
   * Validate state parameter
   */
  validateState(state, expectedUserId, expectedProvider) {
    try {
      const data = JSON.parse(Buffer.from(state, 'base64url').toString())
      const { userId, provider, timestamp } = data
      
      // Check if state is expired (1 hour)
      if (Date.now() - timestamp > 3600000) {
        return false
      }
      
      return userId === expectedUserId && provider === expectedProvider
    } catch (error) {
      return false
    }
  }

  /**
   * Exchange authorization code for tokens
   */
  async exchangeCodeForTokens(provider, code, redirectUri) {
    const config = OAUTH_PROVIDERS[provider]
    if (!config) {
      throw new Error(`Unsupported OAuth provider: ${provider}`)
    }

    const params = new URLSearchParams({
      client_id: config.clientId,
      client_secret: config.clientSecret,
      code,
      grant_type: 'authorization_code',
      redirect_uri: redirectUri
    })

    const response = await fetch(config.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      body: params.toString()
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Token exchange failed: ${error}`)
    }

    return await response.json()
  }

  /**
   * Store encrypted tokens in database
   */
  async storeTokens(userId, provider, tokens) {
    const encryptedTokens = this.encrypt(JSON.stringify(tokens))
    
    const { data, error } = await supabase
      .from('integration_credentials')
      .upsert({
        user_id: userId,
        provider,
        encrypted_tokens: JSON.stringify(encryptedTokens),
        expires_at: tokens.expires_in ? 
          new Date(Date.now() + tokens.expires_in * 1000).toISOString() : null,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id,provider'
      })

    if (error) {
      throw new Error(`Failed to store tokens: ${error.message}`)
    }

    // Log the integration activity
    await this.logActivity(userId, provider, 'tokens_stored', 'success', {
      hasRefreshToken: !!tokens.refresh_token,
      expiresIn: tokens.expires_in
    })

    return data
  }

  /**
   * Retrieve and decrypt tokens from database
   */
  async getTokens(userId, provider) {
    const { data, error } = await supabase
      .from('integration_credentials')
      .select('encrypted_tokens, expires_at')
      .eq('user_id', userId)
      .eq('provider', provider)
      .single()

    if (error || !data) {
      return null
    }

    try {
      const encryptedData = JSON.parse(data.encrypted_tokens)
      const decryptedTokens = this.decrypt(encryptedData)
      return JSON.parse(decryptedTokens)
    } catch (error) {
      console.error('Failed to decrypt tokens:', error)
      return null
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshTokens(userId, provider) {
    const tokens = await this.getTokens(userId, provider)
    if (!tokens || !tokens.refresh_token) {
      throw new Error('No refresh token available')
    }

    const config = OAUTH_PROVIDERS[provider]
    const params = new URLSearchParams({
      client_id: config.clientId,
      client_secret: config.clientSecret,
      refresh_token: tokens.refresh_token,
      grant_type: 'refresh_token'
    })

    const response = await fetch(config.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      body: params.toString()
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Token refresh failed: ${error}`)
    }

    const newTokens = await response.json()
    
    // Preserve refresh token if not provided in response
    if (!newTokens.refresh_token && tokens.refresh_token) {
      newTokens.refresh_token = tokens.refresh_token
    }

    await this.storeTokens(userId, provider, newTokens)
    
    await this.logActivity(userId, provider, 'tokens_refreshed', 'success', {
      expiresIn: newTokens.expires_in
    })

    return newTokens
  }

  /**
   * Remove integration credentials
   */
  async removeIntegration(userId, provider) {
    const { error } = await supabase
      .from('integration_credentials')
      .delete()
      .eq('user_id', userId)
      .eq('provider', provider)

    if (error) {
      throw new Error(`Failed to remove integration: ${error.message}`)
    }

    await this.logActivity(userId, provider, 'integration_removed', 'success')
  }

  /**
   * Log integration activity
   */
  async logActivity(userId, provider, action, status, details = {}) {
    const { error } = await supabase
      .from('integration_logs')
      .insert({
        user_id: userId,
        provider,
        action,
        status,
        details,
        created_at: new Date().toISOString()
      })

    if (error) {
      console.error('Failed to log integration activity:', error)
    }
  }

  /**
   * Get integration status for user
   */
  async getIntegrationStatus(userId, provider = null) {
    let query = supabase
      .from('integration_credentials')
      .select('provider, expires_at, updated_at')
      .eq('user_id', userId)

    if (provider) {
      query = query.eq('provider', provider)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to get integration status: ${error.message}`)
    }

    return data?.map(integration => ({
      provider: integration.provider,
      connected: true,
      expiresAt: integration.expires_at,
      lastUpdated: integration.updated_at,
      needsRefresh: integration.expires_at ? 
        new Date(integration.expires_at) < new Date(Date.now() + 300000) : false // 5 min buffer
    })) || []
  }

  /**
   * Get available OAuth providers
   */
  getAvailableProviders() {
    return Object.keys(OAUTH_PROVIDERS).map(key => ({
      id: key,
      name: OAUTH_PROVIDERS[key].name,
      scopes: OAUTH_PROVIDERS[key].scopes
    }))
  }
}

// Export singleton instance
export default new OAuthManager()
