import { useState, useEffect } from 'react'
import styles from '@/styles/admin/POS.module.css'

// Cash Payment Form Component
function CashPaymentForm({ totalAmount, onPaymentComplete }) {
  const [cashReceived, setCashReceived] = useState('')
  const [error, setError] = useState('')

  const cashReceivedAmount = parseFloat(cashReceived) || 0
  const changeAmount = cashReceivedAmount - totalAmount
  const isValidAmount = cashReceivedAmount >= totalAmount

  // Cash denomination values
  const denominations = [5, 10, 20, 50, 100]

  const handleCashReceivedChange = (e) => {
    const value = e.target.value
    setCashReceived(value)
    setError('')
  }

  const handleDenominationClick = (amount) => {
    const currentAmount = parseFloat(cashReceived) || 0
    const newAmount = currentAmount + amount
    setCashReceived(newAmount.toFixed(2))
    setError('')
  }

  const handleClearAmount = () => {
    setCashReceived('')
    setError('')
  }

  const handleSubmit = (e) => {
    e.preventDefault()

    if (!cashReceived || cashReceivedAmount <= 0) {
      setError('Please enter the cash received amount')
      return
    }

    if (cashReceivedAmount < totalAmount) {
      setError(`Insufficient cash. Need at least $${totalAmount.toFixed(2)}`)
      return
    }

    onPaymentComplete({
      cashReceived: cashReceivedAmount,
      changeAmount: Math.max(0, changeAmount)
    })
  }

  return (
    <div className={styles.cashPaymentForm}>
      <div className={styles.instructionHeader}>
        <span className={styles.instructionIcon}>💵</span>
        <h4>Cash Payment</h4>
      </div>

      <form onSubmit={handleSubmit} className={styles.cashForm}>
        <div className={styles.amountSummary}>
          <div className={styles.totalDue}>
            Total Due: <span className={styles.amount}>${totalAmount.toFixed(2)}</span>
          </div>
        </div>

        <div className={styles.cashInputGroup}>
          <label htmlFor="cashReceived" className={styles.cashLabel}>
            Cash Received:
          </label>
          <div className={styles.cashInputWrapper}>
            <span className={styles.currencySymbol}>$</span>
            <input
              type="number"
              id="cashReceived"
              value={cashReceived}
              onChange={handleCashReceivedChange}
              placeholder="0.00"
              step="0.01"
              min="0"
              className={`${styles.cashInput} ${error ? styles.error : ''}`}
              autoFocus
            />
          </div>

          {/* Quick Cash Denomination Buttons */}
          <div className={styles.denominationButtons}>
            <div className={styles.denominationLabel}>
              Quick Add:
            </div>
            <div className={styles.denominationGrid}>
              {denominations.map((amount) => (
                <button
                  key={amount}
                  type="button"
                  className={styles.denominationButton}
                  onClick={() => handleDenominationClick(amount)}
                >
                  +${amount}
                </button>
              ))}
              <button
                type="button"
                className={styles.clearButton}
                onClick={handleClearAmount}
                title="Clear amount"
              >
                Clear
              </button>
            </div>
          </div>
        </div>

        {error && (
          <div className={styles.errorMessage}>
            {error}
          </div>
        )}

        {cashReceived && isValidAmount && (
          <div className={styles.changeDisplay}>
            <div className={styles.changeAmount}>
              Change: <span className={styles.changeValue}>${changeAmount.toFixed(2)}</span>
            </div>
          </div>
        )}

        <button
          type="submit"
          className={`${styles.processCashButton} ${!isValidAmount ? styles.disabled : ''}`}
          disabled={!isValidAmount}
        >
          Process Cash Payment
        </button>
      </form>
    </div>
  )
}

/**
 * PaymentMethodSelector component for choosing between cash, card, and hardware payments
 *
 * @param {Object} props - Component props
 * @param {Function} props.onPaymentMethodSelect - Callback when payment method is selected
 * @param {number} props.amount - Total amount to be paid
 * @param {boolean} props.isLoading - Loading state
 * @returns {JSX.Element}
 */
export default function PaymentMethodSelector({ onPaymentMethodSelect, amount, isLoading = false }) {
  const [selectedMethod, setSelectedMethod] = useState(null)
  const [terminalDevices, setTerminalDevices] = useState([])
  const [readerDevices, setReaderDevices] = useState([])
  const [loadingDevices, setLoadingDevices] = useState(false)

  // Calculate processing fee (2% for Square payments)
  const SQUARE_PROCESSING_FEE_RATE = 0.02
  const originalAmount = parseFloat(amount || 0)
  const processingFee = originalAmount * SQUARE_PROCESSING_FEE_RATE
  const totalWithFee = originalAmount + processingFee

  // Load available Terminal and Reader devices on component mount
  useEffect(() => {
    loadAllDevices()
  }, [])

  const loadAllDevices = async () => {
    setLoadingDevices(true)
    try {
      // Load Terminal devices
      const terminalResponse = await fetch('/api/admin/pos/terminal-devices', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      })

      if (terminalResponse.ok) {
        const terminalData = await terminalResponse.json()
        setTerminalDevices(terminalData.devices || [])
      }

      // Load Reader devices
      const readerResponse = await fetch('/api/admin/pos/reader-devices', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      })

      if (readerResponse.ok) {
        const readerData = await readerResponse.json()
        setReaderDevices(readerData.devices || [])
      }
    } catch (error) {
      console.error('Error loading devices:', error)
    } finally {
      setLoadingDevices(false)
    }
  }

  const paymentMethods = [
    {
      id: 'square_terminal',
      name: 'Square Terminal',
      description: 'Hardware card reader with contactless payments',
      icon: '📱',
      color: '#006AFF',
      available: false, // Temporarily hidden as requested
      requiresDevice: true,
      deviceType: 'terminal',
      features: [
        'Contactless & chip payments',
        'Built-in receipt printer',
        'PCI compliant hardware',
        'Real-time processing'
      ],
      hidden: true // Mark as hidden for UI purposes
    },
    {
      id: 'square_reader',
      name: 'Square Reader',
      description: 'Mobile card reader with Square POS app integration',
      icon: '📲',
      color: '#FF6B35',
      available: readerDevices.length > 0,
      requiresDevice: true,
      deviceType: 'reader',
      processingFee: processingFee,
      totalAmount: totalWithFee,
      features: [
        'App-to-app integration',
        'Contactless & chip payments',
        'Mobile device compatibility',
        'Tap to Pay support',
        `Processing fee: $${processingFee.toFixed(2)} (2%)`
      ]
    },
    {
      id: 'card',
      name: 'Manual Card Entry',
      description: 'Manual card entry through Square Web SDK',
      icon: '💳',
      color: '#4ECDC4',
      available: true,
      processingFee: processingFee,
      totalAmount: totalWithFee,
      features: [
        'Secure card processing',
        'Automatic receipt',
        'Real-time verification',
        'No hardware required',
        `Processing fee: $${processingFee.toFixed(2)} (2%)`
      ]
    },
    {
      id: 'cash',
      name: 'Cash Payment',
      description: 'Customer pays with cash - record transaction manually',
      icon: '💵',
      color: '#28a745',
      available: true,
      totalAmount: originalAmount, // No fee for cash
      features: [
        'Immediate transaction',
        'No processing fees',
        'Manual receipt generation',
        'No internet required'
      ]
    }
  ]

  const getDeviceStatusIcon = (device) => {
    switch (device.status) {
      case 'PAIRED': return '🟢'
      case 'UNPAIRED': return '🔴'
      case 'UNKNOWN': return '🟡'
      case 'Available': return '🟢'
      case 'Connected': return '🟢'
      default: return '⚪'
    }
  }

  const getDeviceList = (method) => {
    if (method.deviceType === 'terminal') {
      return terminalDevices
    } else if (method.deviceType === 'reader') {
      return readerDevices
    }
    return []
  }

  const getDeviceDisplayName = (device, deviceType) => {
    if (deviceType === 'terminal') {
      return `Terminal ${device.id.slice(-4)}`
    } else if (deviceType === 'reader') {
      return device.type === 'tap_to_pay' ? 'Tap to Pay' : `Reader ${device.id.slice(-4)}`
    }
    return 'Device'
  }

  const handleMethodSelect = (method) => {
    setSelectedMethod(method.id)

    // For non-cash payments, immediately proceed to payment processing
    if (method.id !== 'cash') {
      onPaymentMethodSelect(method.id, {
        ...method,
        originalAmount,
        processingFee: method.processingFee || 0,
        totalAmount: method.totalAmount || originalAmount
      })
    }
    // For cash payments, just set the selection - the form will appear below
    // The actual payment processing will happen when the cash form is submitted
  }

  return (
    <div className={styles.paymentMethodSelector}>
      <div className={styles.paymentHeader}>
        <h3>Select Payment Method</h3>
        <div className={styles.totalAmount}>
          <div className={styles.amountBreakdown}>
            <div className={styles.originalAmount}>
              Subtotal: <span className={styles.amount}>${originalAmount.toFixed(2)}</span>
            </div>
            {selectedMethod && (selectedMethod === 'square_reader' || selectedMethod === 'card') && (
              <div className={styles.processingFee}>
                Processing Fee (2%): <span className={styles.feeAmount}>+${processingFee.toFixed(2)}</span>
              </div>
            )}
            <div className={styles.finalTotal}>
              Total: <span className={styles.amount}>
                ${selectedMethod && (selectedMethod === 'square_reader' || selectedMethod === 'card')
                  ? totalWithFee.toFixed(2)
                  : originalAmount.toFixed(2)}
              </span>
            </div>
          </div>
        </div>
        {loadingDevices && (
          <div className={styles.loadingIndicator}>
            <span>Loading devices...</span>
          </div>
        )}
      </div>

      <div className={styles.paymentMethods}>
        {paymentMethods.filter(method => !method.hidden).map((method) => (
          <div
            key={method.id}
            className={`${styles.paymentMethod} ${selectedMethod === method.id ? styles.selected : ''} ${!method.available ? styles.disabled : ''}`}
            onClick={() => method.available && handleMethodSelect(method)}
            style={{ '--method-color': method.color }}
          >
            <div className={styles.methodIcon}>
              {method.icon}
            </div>
            
            <div className={styles.methodContent}>
              <h4 className={styles.methodName}>
                {method.name}
              </h4>
              
              <p className={styles.methodDescription}>
                {method.description}
              </p>
              
              <ul className={styles.methodFeatures}>
                {method.features.map((feature, index) => (
                  <li key={index} className={styles.feature}>
                    ✓ {feature}
                  </li>
                ))}
              </ul>

              {method.requiresDevice && (
                <div className={styles.deviceInfo}>
                  {getDeviceList(method).length === 0 ? (
                    <div className={styles.noDevices}>
                      <span className={styles.deviceIcon}>⚠️</span>
                      <span>No devices available</span>
                    </div>
                  ) : (
                    <div className={styles.deviceList}>
                      <span className={styles.deviceLabel}>Available devices:</span>
                      {getDeviceList(method).slice(0, 2).map((device) => (
                        <div key={device.id} className={styles.deviceItem}>
                          <span className={styles.deviceStatus}>
                            {getDeviceStatusIcon(device)}
                          </span>
                          <span className={styles.deviceName}>
                            {device.name || getDeviceDisplayName(device, method.deviceType)}
                          </span>
                        </div>
                      ))}
                      {getDeviceList(method).length > 2 && (
                        <span className={styles.moreDevices}>
                          +{getDeviceList(method).length - 2} more
                        </span>
                      )}
                    </div>
                  )}
                </div>
              )}

              {!method.available && (
                <div className={styles.unavailableReason}>
                  {method.requiresDevice && terminalDevices.length === 0
                    ? 'No hardware devices available'
                    : 'Coming Soon'
                  }
                </div>
              )}
            </div>

            <div className={styles.methodSelector}>
              <div className={`${styles.radioButton} ${selectedMethod === method.id ? styles.selected : ''}`}>
                {selectedMethod === method.id && <div className={styles.radioInner}></div>}
              </div>
            </div>
          </div>
        ))}
      </div>

      {selectedMethod === 'square_terminal' && (
        <div className={styles.terminalInstructions}>
          <div className={styles.instructionHeader}>
            <span className={styles.instructionIcon}>📱</span>
            <h4>Square Terminal Payment Instructions</h4>
          </div>
          <ul className={styles.instructionList}>
            <li>Ensure Square Terminal is powered on and connected</li>
            <li>Customer will be prompted on the Terminal device</li>
            <li>Support for chip, contactless, and mobile payments</li>
            <li>Receipt will print automatically from Terminal</li>
          </ul>
          {terminalDevices.length > 0 && (
            <div className={styles.terminalDeviceStatus}>
              <h5>Connected Devices:</h5>
              {terminalDevices.map((device) => (
                <div key={device.id} className={styles.deviceStatusItem}>
                  {getDeviceStatusIcon(device)} {device.name || `Terminal ${device.id.slice(-4)}`}
                  <span className={styles.deviceStatusText}>({device.status})</span>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {selectedMethod === 'square_reader' && (
        <div className={styles.readerInstructions}>
          <div className={styles.instructionHeader}>
            <span className={styles.instructionIcon}>📲</span>
            <h4>Square Reader Payment Instructions</h4>
          </div>
          <ul className={styles.instructionList}>
            <li>Square POS app will launch automatically for payment processing</li>
            <li>Customer will complete payment using connected card reader</li>
            <li>Support for contactless, chip, and mobile payments</li>
            <li>You'll be returned to this page after payment completion</li>
          </ul>
          {readerDevices.length > 0 && (
            <div className={styles.readerDeviceStatus}>
              <h5>Available Readers:</h5>
              {readerDevices.map((device) => (
                <div key={device.id} className={styles.deviceStatusItem}>
                  {getDeviceStatusIcon(device)} {device.name}
                  <span className={styles.deviceStatusText}>({device.status})</span>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {selectedMethod === 'cash' && (
        <CashPaymentForm
          totalAmount={originalAmount}
          onPaymentComplete={(cashData) => {
            // Pass cash payment data to parent for processing
            const cashMethod = paymentMethods.find(m => m.id === 'cash')
            onPaymentMethodSelect('cash', {
              ...cashMethod,
              originalAmount,
              processingFee: 0,
              totalAmount: originalAmount,
              cashReceived: cashData.cashReceived,
              changeAmount: cashData.changeAmount
            })
          }}
        />
      )}

      {selectedMethod && selectedMethod !== 'cash' && (
        <div className={styles.proceedToPayment}>
          <div className={styles.feeBreakdown}>
            <div className={styles.breakdownHeader}>
              <h4>Payment Summary</h4>
            </div>
            <div className={styles.breakdownDetails}>
              <div className={styles.breakdownLine}>
                <span>Subtotal:</span>
                <span>${originalAmount.toFixed(2)}</span>
              </div>
              {(selectedMethod === 'square_reader' || selectedMethod === 'card') && (
                <div className={styles.breakdownLine}>
                  <span>Processing Fee (2%):</span>
                  <span className={styles.feeAmount}>+${processingFee.toFixed(2)}</span>
                </div>
              )}
              <div className={`${styles.breakdownLine} ${styles.totalLine}`}>
                <span><strong>Total:</strong></span>
                <span className={styles.totalAmount}>
                  <strong>
                    ${(selectedMethod === 'square_reader' || selectedMethod === 'card')
                      ? totalWithFee.toFixed(2)
                      : originalAmount.toFixed(2)}
                  </strong>
                </span>
              </div>
            </div>
          </div>

          <button
            className={styles.proceedButton}
            onClick={() => {
              const selectedMethodData = paymentMethods.find(m => m.id === selectedMethod)
              onPaymentMethodSelect(selectedMethod, {
                ...selectedMethodData,
                originalAmount,
                processingFee: (selectedMethod === 'square_reader' || selectedMethod === 'card') ? processingFee : 0,
                totalAmount: (selectedMethod === 'square_reader' || selectedMethod === 'card') ? totalWithFee : originalAmount
              })
            }}
            disabled={isLoading}
          >
            {isLoading ? 'Processing...' : 'Proceed to Payment'}
          </button>
        </div>
      )}

      {selectedMethod === 'card' && (
        <div className={styles.cardInstructions}>
          <div className={styles.instructionHeader}>
            <span className={styles.instructionIcon}>💳</span>
            <h4>Manual Card Entry Instructions</h4>
          </div>
          <ul className={styles.instructionList}>
            <li>Customer card details will be entered manually</li>
            <li>Secure processing through Square Web SDK</li>
            <li>Payment will be processed in real-time</li>
            <li>Digital receipt will be sent automatically</li>
          </ul>
        </div>
      )}

      <div className={styles.securityNote}>
        <div className={styles.securityIcon}>🔒</div>
        <div className={styles.securityText}>
          <strong>Secure Processing:</strong> All transactions are encrypted and comply with 
          PCI DSS standards. Customer payment information is never stored locally.
        </div>
      </div>
    </div>
  )
}
