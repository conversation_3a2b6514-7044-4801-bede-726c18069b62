import { useState } from 'react'
import { safeRender } from '@/lib/safe-render-utils'
import styles from '@/styles/admin/POS.module.css'

/**
 * ServiceTileGrid component for displaying services as clickable tiles
 * Now includes consistent visibility filtering to match QuickEventServiceSelector
 *
 * @param {Object} props - Component props
 * @param {Array} props.services - Array of services with pricing tiers
 * @param {Function} props.onServiceSelect - Callback when service is selected
 * @returns {JSX.Element}
 */
export default function ServiceTileGrid({ services, onServiceSelect }) {
  const [selectedServiceId, setSelectedServiceId] = useState(null)
  const [serviceAvailability, setServiceAvailability] = useState({})
  const [selectedCategory, setSelectedCategory] = useState(null)
  const [selectedService, setSelectedService] = useState(null)
  const [navigationLevel, setNavigationLevel] = useState('categories') // 'categories', 'services', 'tiers'

  // Filter services to match QuickEventServiceSelector logic for consistency
  const getFilteredServices = () => {
    console.log('🔍 ServiceTileGrid - Total services received:', services.length)
    console.log('🔍 ServiceTileGrid - Sample service:', services[0])

    const filteredServices = services.filter(service => {
      // Include services that are visible on events or POS
      // Handle both boolean and string representations
      const visibleOnEvents = service.visible_on_events === true || service.visible_on_events === 'true'
      const visibleOnPos = service.visible_on_pos === true || service.visible_on_pos === 'true'

      // Fallback: if visibility flags are undefined/null, include the service
      // This ensures backward compatibility and prevents empty service lists
      const hasVisibilityFlags = service.visible_on_events !== undefined || service.visible_on_pos !== undefined
      const isVisible = hasVisibilityFlags ? (visibleOnEvents || visibleOnPos) : true

      console.log(`🔍 Service "${service.name}": visible_on_events=${service.visible_on_events}, visible_on_pos=${service.visible_on_pos}, hasFlags=${hasVisibilityFlags}, included=${isVisible}`)
      return isVisible
    })

    console.log('🔍 ServiceTileGrid - Filtered services count:', filteredServices.length)
    console.log('🔍 ServiceTileGrid - Filtered services:', filteredServices.map(s => s.name))
    return filteredServices
  }

  // Group services by category for organized display
  const getServiceCategories = () => {
    const filteredServices = getFilteredServices()
    const categoryMap = new Map()

    filteredServices.forEach(service => {
      // Handle empty/null categories properly
      const rawCategory = service.category || ''
      const category = rawCategory.trim() || 'General'
      const categoryKey = category.toLowerCase().trim() || 'general'

      if (!categoryMap.has(categoryKey)) {
        categoryMap.set(categoryKey, {
          name: category,
          key: categoryKey,
          services: [],
          icon: getCategoryIcon(categoryKey)
        })
      }

      categoryMap.get(categoryKey).services.push(service)
    })

    // Sort categories by service count (most popular first)
    const categories = Array.from(categoryMap.values()).sort((a, b) => b.services.length - a.services.length)
    return categories
  }

  // Get category icon based on service category - enhanced for Full Booking Mode
  const getCategoryIcon = (categoryKey) => {
    const iconMap = {
      'face painting': '🎨',
      'painting': '🎨',
      'airbrush': '🎨',
      'body painting': '🎨',
      'makeup': '💄',
      'hair': '💇‍♀️',
      'hair & braiding': '💇',
      'nails': '💅',
      'massage': '💆‍♀️',
      'skincare': '✨',
      'glitter': '✨',
      'glitter & gems': '✨',
      'photography': '📸',
      'entertainment': '🎭',
      'special': '⭐',
      'general': '⭐',
      'body': '🎨',
      'uv': '🌟',
      'temporary': '🎨'
    }
    return iconMap[categoryKey?.toLowerCase()] || iconMap.general
  }

  // Calculate price range for a service based on its pricing tiers
  const getPriceRange = (pricingTiers, fallbackPrice) => {
    if (!pricingTiers || pricingTiers.length === 0) {
      return `$${parseFloat(fallbackPrice || 0).toFixed(2)}`
    }

    const prices = pricingTiers.map(tier => parseFloat(tier.price || 0))
    const minPrice = Math.min(...prices)
    const maxPrice = Math.max(...prices)

    if (minPrice === maxPrice) {
      return `$${minPrice.toFixed(2)}`
    }

    return `$${minPrice.toFixed(2)} - $${maxPrice.toFixed(2)}`
  }

  const handleServiceClick = (service) => {
    // Check if service has multiple pricing tiers
    const hasPricingTiers = service.pricing_tiers && service.pricing_tiers.length > 1

    if (hasPricingTiers) {
      // Navigate to pricing tier selection
      setSelectedService(service)
      setSelectedServiceId(service.id)
      setNavigationLevel('tiers')
    } else {
      // Direct selection for services with single pricing tier
      setSelectedServiceId(service.id)
      setTimeout(() => {
        onServiceSelect(service)
      }, 150)
    }
  }

  const handlePricingTierSelect = (service, pricingTier) => {
    // Create service object with selected pricing tier
    const serviceWithTier = {
      ...service,
      selectedPricingTier: pricingTier,
      price: pricingTier.price,
      duration: pricingTier.duration
    }

    setSelectedServiceId(service.id)
    setTimeout(() => {
      onServiceSelect(serviceWithTier)
    }, 150)
  }

  const handleCategorySelect = (category) => {
    setSelectedCategory(category)
    setSelectedService(null)
    setSelectedServiceId(null)
    setNavigationLevel('services')
  }

  const handleBackToCategories = () => {
    setSelectedCategory(null)
    setSelectedService(null)
    setSelectedServiceId(null)
    setNavigationLevel('categories')
  }

  const handleBackToServices = () => {
    setSelectedService(null)
    setSelectedServiceId(null)
    setNavigationLevel('services')
  }

  // Get availability status for a service based on available artists
  const getAvailabilityStatus = (service) => {
    const serviceId = service.id

    // Check if we already calculated availability for this service
    const cachedAvailability = serviceAvailability[serviceId]
    if (cachedAvailability !== undefined) return cachedAvailability

    // Determine availability based on artist count and availability
    let status = 'unavailable'

    if (service.availableArtistCount > 0) {
      // Check if any artists are available today
      const availableToday = service.availableArtists?.filter(artist => artist.isAvailableToday) || []

      if (availableToday.length >= 2) {
        status = 'available' // Multiple artists available
      } else if (availableToday.length === 1) {
        status = 'busy' // Only one artist available
      } else {
        status = 'unavailable' // No artists available today
      }
    }

    // Cache the result
    setServiceAvailability(prev => ({ ...prev, [serviceId]: status }))
    return status
  }

  // Get service categories for organized display
  const serviceCategories = getServiceCategories()

  if (serviceCategories.length === 0) {
    return (
      <div className={styles.serviceGrid}>
        <div className={styles.noServices}>
          <h3>No Services Available</h3>
          <p>Please add services with proper visibility settings in the inventory section to use the POS terminal.</p>
          <p className={styles.debugInfo}>
            Total services received: {services?.length || 0} |
            Categories found: {serviceCategories?.length || 0}
          </p>
        </div>
      </div>
    )
  }

  // Render pricing tier selection (third tier)
  if (navigationLevel === 'tiers' && selectedService) {
    return (
      <div className={styles.serviceGrid}>
        <div className={styles.fullBookingHeader}>
          <button
            className={styles.backToCategoriesButton}
            onClick={handleBackToServices}
          >
            ← Back to Services
          </button>
          <h2 className={styles.fullBookingTitle}>{selectedService.name}</h2>
          <p className={styles.fullBookingSubtitle}>Choose duration and pricing option</p>
        </div>

        <div className={styles.pricingTierGrid}>
          {selectedService.pricing_tiers.map((tier, index) => (
            <button
              key={tier.id || index}
              className={`${styles.pricingTierButton} ${tier.is_default === 'true' ? styles.defaultTier : ''}`}
              onClick={() => handlePricingTierSelect(selectedService, tier)}
            >
              <div className={styles.tierName}>{tier.name}</div>
              <div className={styles.tierPrice}>${parseFloat(tier.price || 0).toFixed(2)}</div>
              <div className={styles.tierDuration}>{tier.duration} minutes</div>
              {tier.description && (
                <div className={styles.tierDescription}>{tier.description}</div>
              )}
              {tier.is_default === 'true' && (
                <div className={styles.defaultBadge}>Recommended</div>
              )}
            </button>
          ))}
        </div>
      </div>
    )
  }

  // Render category selection (first tier) - similar to Quick Event Mode
  if (navigationLevel === 'categories' || !selectedCategory) {
    return (
      <div className={styles.serviceGrid}>
        <div className={styles.fullBookingHeader}>
          <h2 className={styles.fullBookingTitle}>Select Service Category</h2>
          <p className={styles.fullBookingSubtitle}>Choose a category to see available services for booking</p>
        </div>

        <div className={styles.categoryGrid}>
          {serviceCategories.map((category) => (
            <button
              key={category.key}
              className={styles.categoryButton}
              onClick={() => handleCategorySelect(category)}
            >
              <div className={styles.categoryIcon}>{category.icon}</div>
              <div className={styles.categoryName}>{category.name}</div>
              <div className={styles.categoryCount}>
                {category.services.length} service{category.services.length !== 1 ? 's' : ''}
              </div>
            </button>
          ))}
        </div>
      </div>
    )
  }

  // Render services within selected category (second tier)
  return (
    <div className={styles.serviceGrid}>
      <div className={styles.fullBookingHeader}>
        <button
          className={styles.backToCategoriesButton}
          onClick={handleBackToCategories}
        >
          ← Back to Categories
        </button>
        <h2 className={styles.fullBookingTitle}>{selectedCategory.name}</h2>
        <p className={styles.fullBookingSubtitle}>Select a service to proceed with booking</p>
      </div>

      <div className={styles.serviceGridContainer}>
        {selectedCategory.services.map((service) => {
          try {
          const isSelected = selectedServiceId === service.id
          const icon = getCategoryIcon(service.category)
          const priceRange = getPriceRange(service.pricing_tiers, service.price)
          const availabilityStatus = getAvailabilityStatus(service)

          // Get artist availability info for display
          const availableToday = service.availableArtists?.filter(artist => artist.isAvailableToday) || []
          const artistCountText = availableToday.length > 0
            ? `${availableToday.length} artist${availableToday.length > 1 ? 's' : ''} available`
            : 'No artists available'

          return (
            <div
              key={service.id}
              className={`${styles.serviceTile} ${isSelected ? styles.selected : ''}`}
              onClick={() => handleServiceClick(service)}
            >
              {/* Availability Indicator */}
              <div className={`${styles.serviceAvailability} ${styles[availabilityStatus]}`}
                   title={`${artistCountText} - ${availabilityStatus}`}
              />

              <span className={styles.serviceIcon}>
                {icon}
              </span>

              <h3 className={styles.serviceName}>
                {safeRender(service.name, 'Unnamed Service')}
              </h3>

              <p className={styles.serviceDescription}>
                {safeRender(service.description, 'No description available')}
              </p>

              <div className={styles.servicePriceRange}>
                {priceRange}
              </div>

              {/* Artist availability info */}
              <div className={styles.artistAvailability}>
                {artistCountText}
              </div>

              {service.pricing_tiers && service.pricing_tiers.length > 1 && (
                <div className={styles.tierCount}>
                  {service.pricing_tiers.length} pricing options →
                </div>
              )}

              {(!service.pricing_tiers || service.pricing_tiers.length <= 1) && (
                <div className={styles.directBooking}>
                  Tap to book
                </div>
              )}
            </div>
          )
        } catch (error) {
          console.error('Error rendering service tile:', error, 'Service:', service)
            return (
              <div key={service.id || Math.random()} className={styles.serviceTile}>
                <div className={styles.serviceError}>
                  <span className={styles.serviceIcon}>⚠️</span>
                  <h3 className={styles.serviceName}>Error Loading Service</h3>
                  <p className={styles.serviceDescription}>
                    Unable to display this service. Please refresh the page.
                  </p>
                </div>
              </div>
            )
          }
        })}
      </div>
    </div>
  )
}
