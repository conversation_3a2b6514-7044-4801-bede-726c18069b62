/**
 * OAuth Callback Endpoint for Ocean Soul Sparkles Integrations
 * Handles OAuth authorization callback and token exchange
 * 
 * Phase 7: Advanced Integrations & Ecosystem
 */

import oauthManager from '@/lib/integrations/oauth-manager'
import { oauthRateLimit } from '@/lib/integrations/rate-limiter'
import { RequestValidator, AuditLogger, SecurityError } from '@/lib/integrations/security-utils'

/**
 * OAuth Callback Handler
 * GET /api/integrations/oauth/callback/[provider] - Handle OAuth callback
 */
export default async function handler(req, res) {
  // Apply rate limiting
  await new Promise((resolve, reject) => {
    oauthRateLimit(req, res, (error) => {
      if (error) reject(error)
      else resolve()
    })
  })

  const { provider } = req.query
  const startTime = Date.now()

  try {
    // Validate request method
    RequestValidator.validateEndpointAccess(req, ['GET'])

    // Validate OAuth callback parameters
    const { code, state } = RequestValidator.validateOAuthCallback(req)

    // Validate provider
    const availableProviders = oauthManager.getAvailableProviders()
    const providerConfig = availableProviders.find(p => p.id === provider)
    
    if (!providerConfig) {
      await AuditLogger.logSecurityEvent(
        null,
        'oauth_callback_invalid_provider',
        { provider, code: code?.substring(0, 10) + '...' },
        'warning'
      )

      return res.redirect(`/admin/settings/integrations?error=invalid_provider&provider=${provider}`)
    }

    // Decode and validate state parameter
    let stateData
    try {
      stateData = JSON.parse(Buffer.from(state, 'base64url').toString())
    } catch (error) {
      await AuditLogger.logSecurityEvent(
        null,
        'oauth_callback_invalid_state',
        { provider, state: state?.substring(0, 20) + '...' },
        'warning'
      )

      return res.redirect(`/admin/settings/integrations?error=invalid_state&provider=${provider}`)
    }

    const { userId, provider: stateProvider, timestamp } = stateData

    // Validate state
    if (!oauthManager.validateState(state, userId, provider)) {
      await AuditLogger.logSecurityEvent(
        userId,
        'oauth_callback_state_validation_failed',
        { provider, userId, stateProvider },
        'warning'
      )

      return res.redirect(`/admin/settings/integrations?error=state_validation_failed&provider=${provider}`)
    }

    // Generate redirect URI (must match the one used in authorization)
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
    const redirectUri = `${baseUrl}/api/integrations/oauth/callback/${provider}`

    // Exchange authorization code for tokens
    const tokens = await oauthManager.exchangeCodeForTokens(provider, code, redirectUri)

    // Store encrypted tokens
    await oauthManager.storeTokens(userId, provider, tokens)

    // Log successful OAuth completion
    await AuditLogger.logIntegrationActivity(
      userId,
      provider,
      'oauth_completed',
      'success',
      {
        hasRefreshToken: !!tokens.refresh_token,
        expiresIn: tokens.expires_in,
        tokenType: tokens.token_type,
        scope: tokens.scope
      }
    )

    // Log API access
    await AuditLogger.logApiAccess(
      userId,
      req.url,
      req.method,
      200,
      Date.now() - startTime,
      { provider, action: 'oauth_callback_success' }
    )

    // Redirect to integration settings with success message
    const successUrl = `/admin/settings/integrations?success=true&provider=${provider}&action=connected`
    return res.redirect(successUrl)

  } catch (error) {
    console.error('OAuth callback error:', error)

    const userId = req.query.state ? 
      (() => {
        try {
          const stateData = JSON.parse(Buffer.from(req.query.state, 'base64url').toString())
          return stateData.userId
        } catch {
          return null
        }
      })() : null

    // Log error
    await AuditLogger.logSecurityEvent(
      userId,
      'oauth_callback_error',
      {
        provider,
        error: error.message,
        code: req.query.code?.substring(0, 10) + '...',
        state: req.query.state?.substring(0, 20) + '...',
        userAgent: req.headers['user-agent'],
        ipAddress: req.headers['x-forwarded-for'] || req.connection?.remoteAddress
      },
      'error'
    )

    // Log API access
    await AuditLogger.logApiAccess(
      userId,
      req.url,
      req.method,
      500,
      Date.now() - startTime,
      { provider, error: error.message }
    )

    // Determine error type and redirect accordingly
    let errorCode = 'unknown_error'
    let errorMessage = 'An unknown error occurred'

    if (error instanceof SecurityError) {
      errorCode = error.code
      errorMessage = error.message
    } else if (error.message.includes('Token exchange failed')) {
      errorCode = 'token_exchange_failed'
      errorMessage = 'Failed to exchange authorization code for tokens'
    } else if (error.message.includes('Failed to store tokens')) {
      errorCode = 'token_storage_failed'
      errorMessage = 'Failed to store integration tokens'
    }

    // Redirect to integration settings with error
    const errorUrl = `/admin/settings/integrations?error=${errorCode}&provider=${provider}&message=${encodeURIComponent(errorMessage)}`
    return res.redirect(errorUrl)
  }
}

/**
 * API Route Configuration
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
