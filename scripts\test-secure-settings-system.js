#!/usr/bin/env node

/**
 * Secure Settings System Test Script
 * Tests the complete secure settings management system
 */

import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      persistSession: false,
      autoRefreshToken: false
    }
  }
)

async function testSecureSettingsSystem() {
  console.log('🔐 Testing Secure Settings Management System\n')

  let allPassed = true
  const results = []

  // Test 1: Database Schema
  console.log('1. Testing admin_settings table...')
  try {
    const { data: settings, error } = await supabase
      .from('admin_settings')
      .select('setting_key, setting_value')
      .limit(5)

    if (error) {
      throw new Error(`admin_settings table error: ${error.message}`)
    }

    console.log(`✅ admin_settings table accessible (${settings?.length || 0} settings found)`)
    results.push({ test: 'admin_settings_table', passed: true, count: settings?.length || 0 })
  } catch (error) {
    console.error('❌ admin_settings table test failed:', error.message)
    allPassed = false
    results.push({ test: 'admin_settings_table', passed: false, error: error.message })
  }

  // Test 2: Encryption Functions
  console.log('\n2. Testing encryption functions...')
  try {
    const { encrypt, decrypt, maskValue, isEncrypted } = await import('../lib/encryption.js')
    
    const testValue = 'test-secret-password-123'
    const encrypted = encrypt(testValue)
    const decrypted = decrypt(encrypted)
    const masked = maskValue(testValue)
    const encryptedCheck = isEncrypted(encrypted)

    if (decrypted !== testValue) {
      throw new Error('Encryption/decryption failed - values do not match')
    }

    if (!encryptedCheck) {
      throw new Error('Encryption detection failed')
    }

    if (!masked.includes('*')) {
      throw new Error('Masking failed - no asterisks found')
    }

    console.log('✅ Encryption functions working correctly')
    console.log(`   Original: ${testValue}`)
    console.log(`   Encrypted: ${encrypted.substring(0, 20)}...`)
    console.log(`   Decrypted: ${decrypted}`)
    console.log(`   Masked: ${masked}`)
    
    results.push({ test: 'encryption_functions', passed: true })
  } catch (error) {
    console.error('❌ Encryption functions test failed:', error.message)
    allPassed = false
    results.push({ test: 'encryption_functions', passed: false, error: error.message })
  }

  // Test 3: Secure Settings Manager
  console.log('\n3. Testing secure settings manager...')
  try {
    const { getAllSettings, SETTING_CATEGORIES } = await import('../lib/secure-settings-manager.js')
    
    const settings = await getAllSettings()
    const categoryCount = Object.keys(SETTING_CATEGORIES).length
    const settingCount = Object.values(SETTING_CATEGORIES).reduce((total, cat) => 
      total + Object.keys(cat.settings).length, 0
    )

    console.log(`✅ Secure settings manager working`)
    console.log(`   Categories: ${categoryCount}`)
    console.log(`   Total settings: ${settingCount}`)
    console.log(`   Current settings: ${Object.keys(settings).length}`)
    
    results.push({ 
      test: 'secure_settings_manager', 
      passed: true, 
      categories: categoryCount,
      totalSettings: settingCount,
      currentSettings: Object.keys(settings).length
    })
  } catch (error) {
    console.error('❌ Secure settings manager test failed:', error.message)
    allPassed = false
    results.push({ test: 'secure_settings_manager', passed: false, error: error.message })
  }

  // Test 4: API Endpoints
  console.log('\n4. Testing API endpoints...')
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3001'
    
    // Test settings API (should require auth)
    const settingsResponse = await fetch(`${baseUrl}/api/admin/secure-settings`)
    
    if (settingsResponse.status !== 401) {
      console.warn('⚠️  Settings API should require authentication')
    } else {
      console.log('✅ Settings API properly requires authentication')
    }

    // Test settings test API (should require auth)
    const testResponse = await fetch(`${baseUrl}/api/admin/secure-settings/test`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ serviceType: 'gmail', credentials: {} })
    })

    if (testResponse.status !== 401) {
      console.warn('⚠️  Test API should require authentication')
    } else {
      console.log('✅ Test API properly requires authentication')
    }

    results.push({ test: 'api_endpoints', passed: true })
  } catch (error) {
    console.error('❌ API endpoints test failed:', error.message)
    allPassed = false
    results.push({ test: 'api_endpoints', passed: false, error: error.message })
  }

  // Test 5: Email Service Manager Integration
  console.log('\n5. Testing email service manager integration...')
  try {
    const { getEmailConfig } = await import('../lib/email-service-manager.js')
    
    const emailConfig = await getEmailConfig()
    
    const hasGmailConfig = !!(emailConfig.gmail.user || emailConfig.gmail.password)
    const hasWorkspaceConfig = !!(emailConfig.workspace.user || emailConfig.workspace.password)
    const hasOneSignalConfig = !!(emailConfig.onesignal.appId || emailConfig.onesignal.restApiKey)
    
    console.log('✅ Email service manager integration working')
    console.log(`   Gmail configured: ${hasGmailConfig ? 'Yes' : 'No'}`)
    console.log(`   Workspace configured: ${hasWorkspaceConfig ? 'Yes' : 'No'}`)
    console.log(`   OneSignal configured: ${hasOneSignalConfig ? 'Yes' : 'No'}`)
    console.log(`   Service priority: ${emailConfig.priority}`)
    
    results.push({ 
      test: 'email_service_integration', 
      passed: true,
      services: {
        gmail: hasGmailConfig,
        workspace: hasWorkspaceConfig,
        onesignal: hasOneSignalConfig
      }
    })
  } catch (error) {
    console.error('❌ Email service manager integration test failed:', error.message)
    allPassed = false
    results.push({ test: 'email_service_integration', passed: false, error: error.message })
  }

  // Test 6: Setting Categories Configuration
  console.log('\n6. Testing setting categories configuration...')
  try {
    const { SETTING_CATEGORIES } = await import('../lib/secure-settings-manager.js')
    
    const requiredCategories = ['gmail', 'workspace', 'onesignal', 'google_oauth', 'email_config']
    const missingCategories = requiredCategories.filter(cat => !SETTING_CATEGORIES[cat])
    
    if (missingCategories.length > 0) {
      throw new Error(`Missing categories: ${missingCategories.join(', ')}`)
    }

    // Check for required sensitive settings
    const sensitiveSettings = []
    Object.entries(SETTING_CATEGORIES).forEach(([catKey, category]) => {
      Object.entries(category.settings).forEach(([settingKey, config]) => {
        if (config.sensitive) {
          sensitiveSettings.push(settingKey)
        }
      })
    })

    console.log('✅ Setting categories configuration valid')
    console.log(`   Categories: ${Object.keys(SETTING_CATEGORIES).join(', ')}`)
    console.log(`   Sensitive settings: ${sensitiveSettings.length}`)
    
    results.push({ 
      test: 'setting_categories', 
      passed: true,
      categories: Object.keys(SETTING_CATEGORIES),
      sensitiveSettings: sensitiveSettings.length
    })
  } catch (error) {
    console.error('❌ Setting categories test failed:', error.message)
    allPassed = false
    results.push({ test: 'setting_categories', passed: false, error: error.message })
  }

  // Summary
  console.log('\n📊 Test Summary:')
  const passedCount = results.filter(r => r.passed).length
  const totalCount = results.length
  
  console.log(`   Passed tests: ${passedCount}/${totalCount}`)
  
  results.forEach(result => {
    const status = result.passed ? '✅' : '❌'
    const error = result.error ? ` (${result.error})` : ''
    console.log(`   ${status} ${result.test}${error}`)
  })

  console.log(`\n   Overall status: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`)

  if (allPassed) {
    console.log('\n🎉 Secure Settings System is ready!')
    console.log('   ✅ Database schema configured')
    console.log('   ✅ Encryption working correctly')
    console.log('   ✅ API endpoints secured')
    console.log('   ✅ Email integration functional')
    console.log('\n📋 Next Steps:')
    console.log('   1. Access /admin/secure-settings in your browser')
    console.log('   2. Configure your email service credentials')
    console.log('   3. Test each service before saving')
    console.log('   4. Verify Artist/Braider onboarding emails work')
  } else {
    console.log('\n🔧 Issues Found:')
    console.log('   1. Run CRITICAL_PRODUCTION_FIX.sql in Supabase if database tests failed')
    console.log('   2. Check environment variables and dependencies')
    console.log('   3. Verify admin authentication is working')
    console.log('   4. Test individual components manually')
  }

  return allPassed
}

// Run the test
testSecureSettingsSystem()
  .then(success => {
    if (success) {
      console.log('\n✅ Secure settings system tests completed successfully!')
      process.exit(0)
    } else {
      console.log('\n❌ Secure settings system tests completed with failures!')
      process.exit(1)
    }
  })
  .catch(error => {
    console.error('\n💥 Test script error:', error)
    process.exit(1)
  })
