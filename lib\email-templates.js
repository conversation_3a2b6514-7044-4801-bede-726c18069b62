/**
 * Email Templates for User Management System
 * Provides templates for welcome emails, onboarding, approval notifications, and other communications
 */

/**
 * Generate welcome email for new users
 * @param {Object} user - User object
 * @param {string} user.name - User's name
 * @param {string} user.email - User's email
 * @param {string} user.role - User's role
 * @param {string} user.applicationToken - Optional secure token for application access
 * @returns {Object} Email template with subject and HTML body
 */
export function generateWelcomeEmail(user) {
  const { name, email, role, applicationToken } = user

  const roleSpecificContent = getRoleSpecificContent(role)

  const subject = `Welcome to Ocean Soul Sparkles Team, ${name}!`
  
  const htmlBody = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Welcome to Ocean Soul Sparkles</title>
      <style>
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
          background-color: #f8f9fa;
        }
        .container {
          background: white;
          border-radius: 12px;
          padding: 30px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .header {
          text-align: center;
          margin-bottom: 30px;
          padding-bottom: 20px;
          border-bottom: 2px solid #f0f0f0;
        }
        .logo {
          font-size: 2rem;
          font-weight: bold;
          color: #6e8efb;
          margin-bottom: 10px;
        }
        .welcome-message {
          font-size: 1.2rem;
          color: #333;
          margin-bottom: 20px;
        }
        .role-badge {
          display: inline-block;
          background: linear-gradient(135deg, #6e8efb, #a777e3);
          color: white;
          padding: 8px 16px;
          border-radius: 20px;
          font-weight: 600;
          font-size: 0.9rem;
          margin: 10px 0;
        }
        .content-section {
          margin: 25px 0;
          padding: 20px;
          background: #f8f9ff;
          border-radius: 8px;
          border-left: 4px solid #6e8efb;
        }
        .content-section h3 {
          color: #333;
          margin: 0 0 15px 0;
          font-size: 1.1rem;
        }
        .next-steps {
          background: #e8f5e8;
          border-left-color: #28a745;
        }
        .contact-info {
          background: #fff3cd;
          border-left-color: #ffc107;
        }
        .button {
          display: inline-block;
          background: linear-gradient(135deg, #6e8efb, #a777e3);
          color: white;
          text-decoration: none;
          padding: 12px 24px;
          border-radius: 6px;
          font-weight: 600;
          margin: 15px 0;
        }
        .footer {
          text-align: center;
          margin-top: 30px;
          padding-top: 20px;
          border-top: 2px solid #f0f0f0;
          color: #666;
          font-size: 0.9rem;
        }
        ul {
          padding-left: 20px;
        }
        li {
          margin: 8px 0;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">🌊 Ocean Soul Sparkles</div>
          <div class="welcome-message">Welcome to the team...</div>
          <div class="role-badge">${roleSpecificContent.roleTitle}</div>
        </div>

        <p>Dear ${name},</p>
        
        <p>We're absolutely thrilled to welcome you to the Ocean Soul Sparkles family! Your journey with us begins now, and we're excited to have someone with your talents and passion join our team.</p>

        ${roleSpecificContent.welcomeContent}

        <div class="content-section next-steps">
          <h3>🚀 Next Steps</h3>
          ${roleSpecificContent.nextSteps}
        </div>

        ${roleSpecificContent.applicationForm ? `
        <div class="content-section">
          <h3>📝 Complete Your Application</h3>
          <p>To finalize your onboarding, please complete our detailed application form:</p>
          <a href="https://www.oceansoulsparkles.com.au/apply/${role}${applicationToken ? `?token=${applicationToken}` : ''}" class="button">
            Complete Application Form
          </a>
          <p><small>This secure link will take you directly to your application form. ${applicationToken ? 'This link is unique to you and will expire after use for security.' : 'Please log in with your provided credentials to access the form.'}</small></p>
        </div>
        ` : ''}

        <div class="content-section contact-info">
          <h3>📞 Contact Information</h3>
          <p>If you have any questions or need assistance, don't hesitate to reach out:</p>
          <ul>
            <li><strong>Email:</strong> <EMAIL></li>
            <li><strong>Phone:</strong> Available in your admin portal</li>
            <li><strong>Staff Portal:</strong> <a href="https://www.oceansoulsparkles.com.au/staff-login">Access Here</a></li>
          </ul>
        </div>

        <p>We're committed to supporting your success and growth within our team. Welcome aboard!</p>

        <p>Warm regards,<br>
        <strong>The Ocean Soul Sparkles Team</strong></p>

        <div class="footer">
          <p>Ocean Soul Sparkles - Beauty & Wellness Services</p>
          <p>This email was sent to ${email}</p>
        </div>
      </div>
    </body>
    </html>
  `

  return {
    subject,
    htmlBody,
    textBody: generateTextVersion(user, roleSpecificContent)
  }
}

/**
 * Get role-specific content for welcome emails
 * @param {string} role - User role
 * @returns {Object} Role-specific content
 */
function getRoleSpecificContent(role) {
  const content = {
    dev: {
      roleTitle: 'Developer',
      welcomeContent: `
        <p>As a Developer on our team, you'll have full access to our systems and will play a crucial role in maintaining and enhancing our digital infrastructure. Your technical expertise will help us deliver exceptional experiences to our clients.</p>
      `,
      nextSteps: `
        <ul>
          <li>Access your developer admin panel with full system privileges</li>
          <li>Review our technical documentation and codebase</li>
          <li>Set up your development environment</li>
          <li>Schedule a technical onboarding session with the team</li>
        </ul>
      `,
      applicationForm: false
    },
    admin: {
      roleTitle: 'Administrator',
      welcomeContent: `
        <p>As an Administrator, you'll have comprehensive access to manage our operations, oversee team members, and ensure smooth business operations. Your leadership will be instrumental in our continued success.</p>
      `,
      nextSteps: `
        <ul>
          <li>Access your admin dashboard with full management privileges</li>
          <li>Review current team structure and operations</li>
          <li>Familiarize yourself with our booking and customer management systems</li>
          <li>Schedule a management briefing with the leadership team</li>
        </ul>
      `,
      applicationForm: false
    },
    artist: {
      roleTitle: 'Beauty Artist',
      welcomeContent: `
        <p>Welcome to our talented team of beauty artists! Your creativity and skills will help our clients look and feel their absolute best. We're excited to see the artistry you'll bring to Ocean Soul Sparkles.</p>
      `,
      nextSteps: `
        <ul>
          <li>Complete your detailed artist application form</li>
          <li>Upload your portfolio and certifications</li>
          <li>Schedule your skills assessment and interview</li>
          <li>Set your availability preferences</li>
          <li>Learn about our service offerings and pricing</li>
        </ul>
      `,
      applicationForm: true
    },
    braider: {
      roleTitle: 'Hair Braiding Specialist',
      welcomeContent: `
        <p>Welcome to our skilled team of hair braiding specialists! Your expertise in protective styling and hair artistry will help our clients achieve beautiful, healthy hairstyles. We're thrilled to have you join our braiding family.</p>
      `,
      nextSteps: `
        <ul>
          <li>Complete your detailed braider application form</li>
          <li>Showcase your braiding portfolio and techniques</li>
          <li>Schedule your practical skills demonstration</li>
          <li>Set your availability and service preferences</li>
          <li>Learn about our braiding service menu and techniques</li>
        </ul>
      `,
      applicationForm: true
    },
    user: {
      roleTitle: 'Team Member',
      welcomeContent: `
        <p>Welcome to the Ocean Soul Sparkles community! As a valued team member, you'll have access to our platform and can participate in our growing network of beauty and wellness professionals.</p>
      `,
      nextSteps: `
        <ul>
          <li>Explore your user dashboard</li>
          <li>Update your profile information</li>
          <li>Browse our services and team</li>
          <li>Connect with other team members</li>
        </ul>
      `,
      applicationForm: false
    }
  }

  return content[role] || content.user
}

/**
 * Generate plain text version of welcome email
 * @param {Object} user - User object
 * @param {Object} roleContent - Role-specific content
 * @returns {string} Plain text email body
 */
function generateTextVersion(user, roleContent) {
  return `
Welcome to Ocean Soul Sparkles Team, ${user.name}!

Dear ${user.name},

We're absolutely thrilled to welcome you to the Ocean Soul Sparkles family! Your journey with us begins now, and we're excited to have someone with your talents and passion join our team.

Role: ${roleContent.roleTitle}

${roleContent.welcomeContent.replace(/<[^>]*>/g, '').trim()}

Next Steps:
${roleContent.nextSteps.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').trim()}

${roleContent.applicationForm ? `
Complete Your Application:
To finalize your onboarding, please complete our detailed application form at:
https://www.oceansoulsparkles.com.au/apply/${user.role}${user.applicationToken ? `?token=${user.applicationToken}` : ''}
${user.applicationToken ? 'This secure link is unique to you and will expire after use.' : 'Please log in with your provided credentials to access the form.'}
` : ''}

Contact Information:
If you have any questions or need assistance, don't hesitate to reach out:
- Email: <EMAIL>
- Staff Portal: https://www.oceansoulsparkles.com.au/staff-login

We're committed to supporting your success and growth within our team. Welcome aboard!

Warm regards,
The Ocean Soul Sparkles Team

---
Ocean Soul Sparkles - Beauty & Wellness Services
This email was sent to ${user.email}
  `.trim()
}

/**
 * Generate approval email for approved Artist/Braider applications
 * @param {Object} user - User object
 * @param {string} user.name - User's name
 * @param {string} user.email - User's email
 * @param {string} user.role - User's role (artist/braider)
 * @param {string} user.activationToken - Secure token for account activation
 * @returns {Object} Email template with subject and HTML body
 */
export function generateApprovalEmail(user) {
  const { name, email, role, activationToken } = user

  const roleTitle = role === 'artist' ? 'Beauty Artist' : 'Hair Braiding Specialist'
  const subject = `🎉 Congratulations! Your ${roleTitle} Application Has Been Approved`

  const activationLink = `https://www.oceansoulsparkles.com.au/activate-account?token=${activationToken}`

  const htmlBody = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${subject}</title>
      <style>
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .email-container {
          background: white;
          border-radius: 15px;
          overflow: hidden;
          box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .header {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 30px;
          text-align: center;
        }
        .header h1 {
          margin: 0;
          font-size: 28px;
          font-weight: 600;
        }
        .celebration-icon {
          font-size: 48px;
          margin-bottom: 15px;
          display: block;
        }
        .content {
          padding: 40px 30px;
        }
        .content-section {
          margin-bottom: 30px;
        }
        .content-section h3 {
          color: #667eea;
          margin-bottom: 15px;
          font-size: 20px;
        }
        .button {
          display: inline-block;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 15px 30px;
          text-decoration: none;
          border-radius: 8px;
          font-weight: 600;
          margin: 15px 0;
          text-align: center;
          transition: transform 0.2s;
        }
        .button:hover {
          transform: translateY(-2px);
        }
        .highlight-box {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          color: white;
          padding: 20px;
          border-radius: 10px;
          margin: 20px 0;
          text-align: center;
        }
        .next-steps {
          background: #f8f9ff;
          padding: 25px;
          border-radius: 10px;
          border-left: 4px solid #667eea;
        }
        .next-steps ol {
          margin: 0;
          padding-left: 20px;
        }
        .next-steps li {
          margin-bottom: 10px;
          font-weight: 500;
        }
        .footer {
          background: #f8f9ff;
          padding: 25px 30px;
          text-align: center;
          border-top: 1px solid #e0e6ff;
        }
        .footer p {
          margin: 5px 0;
          color: #666;
        }
        .security-note {
          background: #fff3cd;
          border: 1px solid #ffeaa7;
          padding: 15px;
          border-radius: 8px;
          margin: 20px 0;
          font-size: 14px;
        }
        .contact-info {
          background: #e8f4fd;
          padding: 20px;
          border-radius: 10px;
          margin: 20px 0;
        }
        @media (max-width: 600px) {
          body { padding: 10px; }
          .content { padding: 20px; }
          .header { padding: 20px; }
          .header h1 { font-size: 24px; }
        }
      </style>
    </head>
    <body>
      <div class="email-container">
        <div class="header">
          <span class="celebration-icon">🎉</span>
          <h1>Congratulations, ${name}!</h1>
          <p>Your ${roleTitle} application has been approved!</p>
        </div>

        <div class="content">
          <div class="content-section">
            <p>Dear ${name},</p>
            <p>We're absolutely <strong>thrilled</strong> to inform you that your application to join Ocean Soul Sparkles as a <strong>${roleTitle}</strong> has been <strong>approved</strong>! 🌟</p>
            <p>After careful review of your application, portfolio, and experience, our team is excited to welcome you to the Ocean Soul Sparkles family. Your skills and passion align perfectly with our vision of creating magical beauty experiences.</p>
          </div>

          <div class="highlight-box">
            <h3 style="margin: 0; color: white;">🔐 Activate Your Account Now</h3>
            <p style="margin: 10px 0; color: white;">Click the button below to create your secure password and activate your Ocean Soul Sparkles team account:</p>
            <a href="${activationLink}" class="button" style="background: white; color: #667eea; margin: 10px 0;">
              Activate My Account
            </a>
          </div>

          <div class="security-note">
            <strong>🔒 Security Notice:</strong> This activation link is unique to you and will expire in 7 days for security purposes. Please activate your account as soon as possible.
          </div>

          <div class="content-section">
            <h3>🌟 What's Next?</h3>
            <div class="next-steps">
              <ol>
                <li><strong>Activate your account</strong> using the secure link above</li>
                <li><strong>Complete your profile</strong> with additional details and preferences</li>
                <li><strong>Access your staff portal</strong> to view schedules and bookings</li>
                <li><strong>Join our team orientation</strong> (details will be provided after activation)</li>
                <li><strong>Start creating magic</strong> with Ocean Soul Sparkles! ✨</li>
              </ol>
            </div>
          </div>

          <div class="contact-info">
            <h3 style="margin-top: 0; color: #667eea;">📞 Need Help?</h3>
            <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p><strong>Staff Portal:</strong> <a href="https://www.oceansoulsparkles.com.au/staff-login">oceansoulsparkles.com.au/staff-login</a></p>
            <p><strong>Main Website:</strong> <a href="https://www.oceansoulsparkles.com.au">oceansoulsparkles.com.au</a></p>
          </div>
        </div>

        <div class="footer">
          <p><strong>Welcome to the Ocean Soul Sparkles Family! 🌊✨</strong></p>
          <p>We're excited to see the amazing work you'll create with us.</p>
          <p style="font-size: 12px; color: #999; margin-top: 20px;">
            This email was sent to ${email}. If you have any questions, please contact our admin team.
          </p>
        </div>
      </div>
    </body>
    </html>
  `

  const textVersion = `
🎉 Congratulations! Your ${roleTitle} Application Has Been Approved

Dear ${name},

We're absolutely thrilled to inform you that your application to join Ocean Soul Sparkles as a ${roleTitle} has been APPROVED! 🌟

After careful review of your application, portfolio, and experience, our team is excited to welcome you to the Ocean Soul Sparkles family.

🔐 ACTIVATE YOUR ACCOUNT NOW:
Please activate your account and create your secure password by visiting:
${activationLink}

🔒 SECURITY NOTICE: This activation link is unique to you and will expire in 7 days for security purposes.

🌟 WHAT'S NEXT:
1. Activate your account using the secure link above
2. Complete your profile with additional details and preferences
3. Access your staff portal to view schedules and bookings
4. Join our team orientation (details provided after activation)
5. Start creating magic with Ocean Soul Sparkles! ✨

📞 NEED HELP?
Email: <EMAIL>
Staff Portal: https://www.oceansoulsparkles.com.au/staff-login
Main Website: https://www.oceansoulsparkles.com.au

Welcome to the Ocean Soul Sparkles Family! 🌊✨
We're excited to see the amazing work you'll create with us.

This email was sent to ${email}. If you have any questions, please contact our admin team.
  `

  return {
    subject,
    htmlBody,
    text: textVersion
  }
}

export default {
  generateWelcomeEmail,
  generateApprovalEmail
}
