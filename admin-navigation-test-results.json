{"navigationFixes": [{"file": "components/admin/ProtectedRoute.js", "line": 73, "fix": "Using router.replace for auth redirects", "code": "router.replace('/admin/login')"}, {"file": "components/admin/ProtectedRoute.js", "line": 78, "fix": "Using router.replace for auth redirects", "code": "router.replace('/admin/login')"}, {"file": "components/admin/ProtectedRoute.js", "line": 159, "fix": "Using router.replace for auth redirects", "code": "router.replace('/admin/login')"}, {"file": "components/admin/ProtectedRoute.js", "line": 188, "fix": "Using router.replace for auth redirects", "code": "router.replace('/admin/login').catch(error => {"}, {"file": "components/admin/ProtectedRoute.js", "line": 294, "fix": "Using router.replace for auth redirects", "code": "router.replace('/admin/artist-braider-dashboard');"}, {"file": "components/admin/ProtectedRoute.js", "line": 324, "fix": "Using router.replace for auth redirects", "code": "router.replace('/admin/login')"}, {"file": "components/admin/ProtectedRoute.js", "line": 351, "fix": "Using router.replace for auth redirects", "code": "router.replace('/admin/login')"}, {"file": "components/admin/ProtectedRoute.js", "line": 401, "fix": "Using router.replace for auth redirects", "code": "onClick={() => router.replace('/admin/login')}"}, {"file": "components/admin/AdminLayout.js", "line": 38, "fix": "Using router.replace for auth redirects", "code": "router.replace('/admin/login')"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 47, "fix": "Using router.replace for auth redirects", "code": "if (!authLoading) router.replace('/staff-login?redirect=/admin/artist-braider-dashboard');"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 65, "fix": "Using router.replace for auth redirects", "code": "router.replace('/staff-login?redirect=/admin/artist-braider-dashboard');"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 88, "fix": "Using router.replace for auth redirects", "code": "router.replace('/admin/complete-profile');"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 119, "fix": "Using router.replace for auth redirects", "code": "router.replace('/admin/complete-profile');"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 167, "fix": "Using router.replace for auth redirects", "code": "router.replace('/staff-login?redirect=/admin/artist-braider-dashboard');"}], "authenticationFixes": [{"file": "components/admin/ProtectedRoute.js", "line": 73, "fix": "Improved login redirect handling", "code": "router.replace('/admin/login')"}, {"file": "components/admin/ProtectedRoute.js", "line": 78, "fix": "Improved login redirect handling", "code": "router.replace('/admin/login')"}, {"file": "components/admin/ProtectedRoute.js", "line": 159, "fix": "Improved login redirect handling", "code": "router.replace('/admin/login')"}, {"file": "components/admin/ProtectedRoute.js", "line": 188, "fix": "Improved login redirect handling", "code": "router.replace('/admin/login').catch(error => {"}, {"file": "components/admin/ProtectedRoute.js", "line": 324, "fix": "Improved login redirect handling", "code": "router.replace('/admin/login')"}, {"file": "components/admin/ProtectedRoute.js", "line": 351, "fix": "Improved login redirect handling", "code": "router.replace('/admin/login')"}, {"file": "components/admin/ProtectedRoute.js", "line": 401, "fix": "Improved login redirect handling", "code": "onClick={() => router.replace('/admin/login')}"}, {"file": "components/admin/AdminLayout.js", "line": 38, "fix": "Improved login redirect handling", "code": "router.replace('/admin/login')"}], "componentIntegration": [{"file": "components/admin/ProtectedRoute.js", "line": 8, "improvement": "Proper error handling component", "code": "export default function ProtectedRoute({"}, {"file": "components/admin/ProtectedRoute.js", "line": 16, "improvement": "Proper error handling component", "code": "console.log(`[ProtectedRoute] Rendering for path: ${router.pathname}. Props: adminOnly=${adminOnly}, devOnly=${devOnly}, staffOnly=${staffOnly}, requiredRole=${requiredRole}`);"}, {"file": "components/admin/ProtectedRoute.js", "line": 53, "improvement": "Proper error handling component", "code": "console.log('ProtectedRoute: Development mode or auth bypass - skipping timeout mechanisms')"}, {"file": "components/admin/ProtectedRoute.js", "line": 61, "improvement": "Proper error handling component", "code": "console.log('ProtectedRoute: Authentication timeout reached')"}, {"file": "components/admin/ProtectedRoute.js", "line": 66, "improvement": "Proper error handling component", "code": "console.log(`ProtectedRoute: Attempting recovery (attempt ${retryCount + 1}/2)`)"}, {"file": "components/admin/ProtectedRoute.js", "line": 71, "improvement": "Proper error handling component", "code": "console.log('ProtectedRoute: Attempting auth refresh')"}, {"file": "components/admin/ProtectedRoute.js", "line": 76, "improvement": "Proper error handling component", "code": "console.log('ProtectedRoute: Max retry attempts reached, redirecting to login')"}, {"file": "components/admin/ProtectedRoute.js", "line": 101, "improvement": "Proper error handling component", "code": "console.log('ProtectedRoute: Auth token stored for API access')"}, {"file": "components/admin/ProtectedRoute.js", "line": 103, "improvement": "Proper error handling component", "code": "console.warn('ProtectedRoute: Failed to get session for token storage:', error)"}, {"file": "components/admin/ProtectedRoute.js", "line": 106, "improvement": "Proper error handling component", "code": "console.warn('ProtectedRoute: Error storing session token:', error)"}, {"file": "components/admin/ProtectedRoute.js", "line": 117, "improvement": "Proper error handling component", "code": "console.log(`[ProtectedRoute useEffect] Running. AuthLoading: ${authLoading}, User: ${user?.email}, Role: ${role}`);"}, {"file": "components/admin/ProtectedRoute.js", "line": 119, "improvement": "Proper error handling component", "code": "console.log('[ProtectedRoute useEffect] Development mode or auth bypass enabled - bypassing ALL auth checks and timeouts')"}, {"file": "components/admin/ProtectedRoute.js", "line": 140, "improvement": "Proper error handling component", "code": "console.log('ProtectedRoute: Running access check', { authLoading, user: !!user, role })"}, {"file": "components/admin/ProtectedRoute.js", "line": 145, "improvement": "Proper error handling component", "code": "console.log(`[ProtectedRoute checkAccess] START. User: ${user?.email}, Role: ${role}, AuthLoading: ${authLoading}, Path: ${router.pathname}`);"}, {"file": "components/admin/ProtectedRoute.js", "line": 147, "improvement": "Proper error handling component", "code": "// console.log('ProtectedRoute: Checking access for user:', user?.email, 'role:', role, 'authLoading:', authLoading) // Original log"}, {"file": "components/admin/ProtectedRoute.js", "line": 151, "improvement": "Proper error handling component", "code": "console.log('[ProtectedRoute checkAccess] Auth still loading and no user data, waiting...');"}, {"file": "components/admin/ProtectedRoute.js", "line": 157, "improvement": "Proper error handling component", "code": "console.log('ProtectedRoute: Fallback timeout - forcing redirect to login')"}, {"file": "components/admin/ProtectedRoute.js", "line": 173, "improvement": "Proper error handling component", "code": "console.log(`[ProtectedRoute checkAccess] No user, not on login page. Redirecting to /admin/login. Storing redirect_after_login: ${router.asPath}`);"}, {"file": "components/admin/ProtectedRoute.js", "line": 189, "improvement": "Proper error handling component", "code": "console.error('ProtectedRoute: Failed to redirect to login:', error)"}, {"file": "components/admin/ProtectedRoute.js", "line": 206, "improvement": "Proper error handling component", "code": "console.log(`[ProtectedRoute checkAccess] User authenticated on login page. Redirecting to: ${redirectPath}`);"}, {"file": "components/admin/ProtectedRoute.js", "line": 221, "improvement": "Proper error handling component", "code": "console.log('[ProtectedRoute checkAccess] DEV user or known admin - granting unrestricted access.');"}, {"file": "components/admin/ProtectedRoute.js", "line": 226, "improvement": "Proper error handling component", "code": "console.log(`[ProtectedRoute checkAccess] END (DEV/KnownAdmin). isAuthorized: true, Loading: false, Error: null`);"}, {"file": "components/admin/ProtectedRoute.js", "line": 229, "improvement": "Proper error handling component", "code": "console.log('[ProtectedRoute checkAccess] Proceeding to role-based authorization checks.');"}, {"file": "components/admin/ProtectedRoute.js", "line": 237, "improvement": "Proper error handling component", "code": "if (!isDev) console.log(`[ProtectedRoute checkAccess] Authorization FAILED: ${errorMessage}. User role: ${role}`);"}, {"file": "components/admin/ProtectedRoute.js", "line": 241, "improvement": "Proper error handling component", "code": "if (!hasAdminAccess) console.log(`[ProtectedRoute checkAccess] Authorization FAILED: ${errorMessage}. User role: ${role}`);"}, {"file": "components/admin/ProtectedRoute.js", "line": 245, "improvement": "Proper error handling component", "code": "if (!hasStaffAccess) console.log(`[ProtectedRoute checkAccess] Authorization FAILED: ${errorMessage}. User role: ${role}`);"}, {"file": "components/admin/ProtectedRoute.js", "line": 249, "improvement": "Proper error handling component", "code": "if (role !== requiredRole) console.log(`[ProtectedRoute checkAccess] Authorization FAILED: ${errorMessage}. User role: ${role}`);"}, {"file": "components/admin/ProtectedRoute.js", "line": 254, "improvement": "Proper error handling component", "code": "if (!hasStaffAccess) console.log(`[ProtectedRoute checkAccess] Authorization FAILED: ${errorMessage}. User role: ${role}`);"}, {"file": "components/admin/ProtectedRoute.js", "line": 273, "improvement": "Enhanced artist/braider path access", "code": "currentPath === '/admin/complete-profile' ||"}, {"file": "components/admin/ProtectedRoute.js", "line": 274, "improvement": "Enhanced artist/braider path access", "code": "currentPath === '/admin/my-profile' ||"}, {"file": "components/admin/ProtectedRoute.js", "line": 292, "improvement": "Proper error handling component", "code": "// console.warn(`ProtectedRoute: Artist/Braider on non-allowed path '${currentPath}'. Redirecting to their dashboard '/admin/artist-braider-dashboard'.`); // Original log"}, {"file": "components/admin/ProtectedRoute.js", "line": 293, "improvement": "Proper error handling component", "code": "console.warn(`[ProtectedRoute checkAccess] Artist/Braider on non-allowed path '${currentPath}'. Redirecting to their dashboard '/admin/artist-braider-dashboard'.`);"}, {"file": "components/admin/ProtectedRoute.js", "line": 296, "improvement": "Proper error handling component", "code": "console.log(`[ProtectedRoute checkAccess] END (Artist/Braider Redirect). isAuthorized: false (implicitly, due to redirect), Loading: ${loading} (state), Error: ${error} (state)`);"}, {"file": "components/admin/ProtectedRoute.js", "line": 302, "improvement": "Proper error handling component", "code": "// console.log('ProtectedRoute: User lacks required privileges', { // Original log"}, {"file": "components/admin/ProtectedRoute.js", "line": 320, "improvement": "Proper error handling component", "code": "console.log(`[ProtectedRoute checkAccess] Not authorized. Error: ${errorMessage}. Redirecting to /admin/login (or other for prod).`);"}, {"file": "components/admin/ProtectedRoute.js", "line": 329, "improvement": "Proper error handling component", "code": "console.log(`[ProtectedRoute checkAccess] User IS AUTHORIZED. Role: ${role}. Path: ${router.pathname}`);"}, {"file": "components/admin/ProtectedRoute.js", "line": 337, "improvement": "Proper error handling component", "code": "console.log(`[ProtectedRoute checkAccess] END. Decision: isAuthorized=${authorized} (variable). Current component states - Loading: ${loading}, Error: ${error}`);"}, {"file": "components/admin/ProtectedRoute.js", "line": 340, "improvement": "Proper error handling component", "code": "console.error('[ProtectedRoute checkAccess] Access verification CATCH block error:', err);"}, {"file": "components/admin/ProtectedRoute.js", "line": 345, "improvement": "Proper error handling component", "code": "console.log(`[ProtectedRoute checkAccess] END (Catch Block). isAuthorized: false, Loading: false, Error: ${errorMsg}`);"}, {"file": "components/admin/ProtectedRoute.js", "line": 362, "improvement": "Proper error handling component", "code": "console.log('ProtectedRoute: Development mode or auth bypass enabled - rendering children directly without auth checks')"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 7, "improvement": "Proper error handling component", "code": "import ProtectedRoute from '@/components/admin/ProtectedRoute';"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 9, "improvement": "Proper error handling component", "code": "import DashboardErrorBoundary from '@/components/admin/DashboardErrorBoundary';"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 88, "improvement": "Enhanced artist/braider path access", "code": "router.replace('/admin/complete-profile');"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 108, "improvement": "Enhanced artist/braider path access", "code": "console.log('[ArtistDashboard] Profile incomplete, redirecting to /admin/complete-profile');"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 119, "improvement": "Enhanced artist/braider path access", "code": "router.replace('/admin/complete-profile');"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 189, "improvement": "Proper error handling component", "code": "<ProtectedRoute>"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 202, "improvement": "Proper error handling component", "code": "</ProtectedRoute>"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 208, "improvement": "Proper error handling component", "code": "<ProtectedRoute>"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 227, "improvement": "Proper error handling component", "code": "</ProtectedRoute>"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 235, "improvement": "Proper error handling component", "code": "<ProtectedRoute>"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 244, "improvement": "Proper error handling component", "code": "</ProtectedRoute>"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 249, "improvement": "Proper error handling component", "code": "<ProtectedRoute>"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 252, "improvement": "Proper error handling component", "code": "<DashboardErrorBoundary>"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 385, "improvement": "Enhanced artist/braider path access", "code": "<Link href=\"/admin/complete-profile\" legacyBehavior><a className={styles.completeProfileLink}>No, Complete Now</a></Link>"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 407, "improvement": "Enhanced artist/braider path access", "code": "<Link href=\"/admin/my-profile\" legacyBehavior>"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 414, "improvement": "Proper error handling component", "code": "</DashboardErrorBoundary>"}, {"file": "pages/admin/artist-braider-dashboard.js", "line": 417, "improvement": "Proper error handling component", "code": "</ProtectedRoute>"}, {"file": "components/admin/ProtectedRoute.js", "improvement": "Enhanced artist/braider allowed paths", "details": "Added profile completion and management paths"}], "issues": []}