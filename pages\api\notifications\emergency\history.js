import { supabase } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * Emergency Notification History API Endpoint
 * Retrieves history of emergency notifications for admin review
 */
export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });
  }

  try {
    // Authenticate admin request
    const authResult = await authenticateAdminRequest(req);
    if (!authResult.success) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Only allow admin and dev roles to view emergency history
    if (!['admin', 'dev'].includes(authResult.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions to view emergency notification history'
      });
    }

    const { 
      limit = 20, 
      offset = 0, 
      priority,
      target_audience,
      date_from,
      date_to 
    } = req.query;

    // Build query for emergency notifications
    let query = supabase
      .from('emergency_notifications')
      .select(`
        id,
        title,
        message,
        priority,
        target_audience,
        channels,
        expires_at,
        requires_acknowledgment,
        sent_by,
        sent_at,
        recipients_count,
        successful_deliveries,
        status,
        completed_at,
        user_profiles!emergency_notifications_sent_by_fkey(
          id,
          name,
          email,
          role
        )
      `)
      .order('sent_at', { ascending: false })
      .range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1);

    // Apply filters if provided
    if (priority) {
      query = query.eq('priority', priority);
    }

    if (target_audience) {
      query = query.eq('target_audience', target_audience);
    }

    if (date_from) {
      query = query.gte('sent_at', date_from);
    }

    if (date_to) {
      query = query.lte('sent_at', date_to);
    }

    const { data: emergencyNotifications, error: queryError } = await query;

    if (queryError) {
      throw queryError;
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('emergency_notifications')
      .select('id', { count: 'exact', head: true });

    // Apply same filters to count query
    if (priority) {
      countQuery = countQuery.eq('priority', priority);
    }

    if (target_audience) {
      countQuery = countQuery.eq('target_audience', target_audience);
    }

    if (date_from) {
      countQuery = countQuery.gte('sent_at', date_from);
    }

    if (date_to) {
      countQuery = countQuery.lte('sent_at', date_to);
    }

    const { count: totalCount, error: countError } = await countQuery;

    if (countError) {
      throw countError;
    }

    // Format the response data
    const formattedNotifications = emergencyNotifications.map(notification => ({
      id: notification.id,
      title: notification.title,
      message: notification.message,
      priority: notification.priority,
      target_audience: notification.target_audience,
      channels: notification.channels,
      expires_at: notification.expires_at,
      requires_acknowledgment: notification.requires_acknowledgment,
      sent_at: notification.sent_at,
      completed_at: notification.completed_at,
      recipients_count: notification.recipients_count,
      successful_deliveries: notification.successful_deliveries,
      status: notification.status,
      sent_by: {
        id: notification.user_profiles?.id,
        name: notification.user_profiles?.name,
        email: notification.user_profiles?.email,
        role: notification.user_profiles?.role
      },
      delivery_rate: notification.recipients_count > 0 
        ? Math.round((notification.successful_deliveries / notification.recipients_count) * 100)
        : 0
    }));

    return res.status(200).json({
      success: true,
      data: formattedNotifications,
      pagination: {
        total: totalCount,
        limit: parseInt(limit),
        offset: parseInt(offset),
        has_more: parseInt(offset) + parseInt(limit) < totalCount
      }
    });

  } catch (error) {
    console.error('Emergency notification history error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to retrieve emergency notification history: ' + error.message
    });
  }
}
