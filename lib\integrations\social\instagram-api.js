/**
 * Instagram Business API Integration for Ocean Soul Sparkles
 * Provides Instagram Business API integration with portfolio sync and automated posting
 * 
 * Phase 7.3: Social Media Integration Layer
 */

import oauthManager from '../oauth-manager'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, AuditLogger } from '../security-utils'

/**
 * Instagram Business API Client
 */
export class InstagramBusinessClient {
  constructor(userId) {
    this.userId = userId
    this.baseUrl = 'https://graph.facebook.com/v18.0'
    this.accessToken = null
    this.businessAccountId = null
  }

  /**
   * Initialize Instagram Business client with user credentials
   */
  async initialize() {
    try {
      // Get user's OAuth tokens
      const tokens = await oauthManager.getTokens(this.userId, 'instagram_business')
      
      if (!tokens) {
        throw new Error('No Instagram Business credentials found for user')
      }

      // Check if tokens need refresh
      if (oauthManager.needsRefresh(tokens.expires_at)) {
        const refreshedTokens = await oauthManager.refreshTokens(this.userId, 'instagram_business')
        tokens.access_token = refreshedTokens.access_token
      }

      this.accessToken = tokens.access_token

      // Get business account information
      await this.getBusinessAccountInfo()

      return true
    } catch (error) {
      console.error('Failed to initialize Instagram Business client:', error)
      
      await AuditLogger.logIntegrationActivity(
        this.userId,
        'instagram_business',
        'initialization_failed',
        'error',
        { error: error.message }
      )
      
      return false
    }
  }

  /**
   * Get business account information
   */
  async getBusinessAccountInfo() {
    return await RetryHandler.withRetry(async () => {
      const response = await fetch(`${this.baseUrl}/me/accounts?access_token=${this.accessToken}`)
      
      if (!response.ok) {
        throw new Error(`Failed to get business accounts: ${response.statusText}`)
      }

      const data = await response.json()
      
      // Find Instagram Business account
      for (const account of data.data || []) {
        const igResponse = await fetch(
          `${this.baseUrl}/${account.id}?fields=instagram_business_account&access_token=${this.accessToken}`
        )
        
        if (igResponse.ok) {
          const igData = await igResponse.json()
          if (igData.instagram_business_account) {
            this.businessAccountId = igData.instagram_business_account.id
            break
          }
        }
      }

      if (!this.businessAccountId) {
        throw new Error('No Instagram Business account found')
      }

      return this.businessAccountId
    })
  }

  /**
   * Get Instagram Business account profile information
   */
  async getProfile() {
    if (!this.businessAccountId) {
      throw new Error('Instagram Business client not initialized')
    }

    return await RetryHandler.withRetry(async () => {
      const response = await fetch(
        `${this.baseUrl}/${this.businessAccountId}?fields=id,username,name,biography,followers_count,follows_count,media_count,profile_picture_url,website&access_token=${this.accessToken}`
      )

      if (!response.ok) {
        throw new Error(`Failed to get profile: ${response.statusText}`)
      }

      const data = await response.json()
      return {
        id: data.id,
        username: data.username,
        name: data.name,
        biography: data.biography,
        followersCount: data.followers_count,
        followsCount: data.follows_count,
        mediaCount: data.media_count,
        profilePictureUrl: data.profile_picture_url,
        website: data.website
      }
    })
  }

  /**
   * Get Instagram media (posts)
   */
  async getMedia(limit = 25, after = null) {
    if (!this.businessAccountId) {
      throw new Error('Instagram Business client not initialized')
    }

    return await RetryHandler.withRetry(async () => {
      let url = `${this.baseUrl}/${this.businessAccountId}/media?fields=id,media_type,media_url,thumbnail_url,permalink,caption,timestamp,like_count,comments_count&limit=${limit}&access_token=${this.accessToken}`
      
      if (after) {
        url += `&after=${after}`
      }

      const response = await fetch(url)

      if (!response.ok) {
        throw new Error(`Failed to get media: ${response.statusText}`)
      }

      const data = await response.json()
      
      return {
        media: data.data?.map(item => ({
          id: item.id,
          type: item.media_type,
          mediaUrl: item.media_url,
          thumbnailUrl: item.thumbnail_url,
          permalink: item.permalink,
          caption: item.caption,
          timestamp: item.timestamp,
          likeCount: item.like_count,
          commentsCount: item.comments_count
        })) || [],
        paging: data.paging
      }
    })
  }

  /**
   * Create Instagram media (post)
   */
  async createMedia(mediaData) {
    if (!this.businessAccountId) {
      throw new Error('Instagram Business client not initialized')
    }

    return await RetryHandler.withRetry(async () => {
      // Step 1: Create media container
      const containerData = {
        image_url: mediaData.imageUrl,
        caption: mediaData.caption,
        access_token: this.accessToken
      }

      // Add location if provided
      if (mediaData.locationId) {
        containerData.location_id = mediaData.locationId
      }

      // Add user tags if provided
      if (mediaData.userTags && mediaData.userTags.length > 0) {
        containerData.user_tags = JSON.stringify(mediaData.userTags)
      }

      const containerResponse = await fetch(
        `${this.baseUrl}/${this.businessAccountId}/media`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams(containerData)
        }
      )

      if (!containerResponse.ok) {
        const error = await containerResponse.text()
        throw new Error(`Failed to create media container: ${error}`)
      }

      const containerResult = await containerResponse.json()
      const creationId = containerResult.id

      // Step 2: Publish the media
      const publishResponse = await fetch(
        `${this.baseUrl}/${this.businessAccountId}/media_publish`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            creation_id: creationId,
            access_token: this.accessToken
          })
        }
      )

      if (!publishResponse.ok) {
        const error = await publishResponse.text()
        throw new Error(`Failed to publish media: ${error}`)
      }

      const publishResult = await publishResponse.json()

      await AuditLogger.logIntegrationActivity(
        this.userId,
        'instagram_business',
        'media_created',
        'success',
        {
          mediaId: publishResult.id,
          caption: mediaData.caption?.substring(0, 100) + '...',
          hasLocation: !!mediaData.locationId,
          userTagsCount: mediaData.userTags?.length || 0
        }
      )

      return {
        id: publishResult.id,
        creationId
      }
    })
  }

  /**
   * Get Instagram insights (analytics)
   */
  async getInsights(mediaId = null, period = 'day', metrics = null) {
    if (!this.businessAccountId) {
      throw new Error('Instagram Business client not initialized')
    }

    return await RetryHandler.withRetry(async () => {
      let url
      let defaultMetrics

      if (mediaId) {
        // Media-specific insights
        defaultMetrics = ['engagement', 'impressions', 'reach', 'saved']
        url = `${this.baseUrl}/${mediaId}/insights`
      } else {
        // Account insights
        defaultMetrics = ['impressions', 'reach', 'profile_views', 'website_clicks']
        url = `${this.baseUrl}/${this.businessAccountId}/insights`
      }

      const metricsToUse = metrics || defaultMetrics
      const params = new URLSearchParams({
        metric: metricsToUse.join(','),
        period,
        access_token: this.accessToken
      })

      const response = await fetch(`${url}?${params}`)

      if (!response.ok) {
        throw new Error(`Failed to get insights: ${response.statusText}`)
      }

      const data = await response.json()
      
      return data.data?.reduce((acc, insight) => {
        acc[insight.name] = {
          value: insight.values?.[0]?.value || 0,
          period: insight.period,
          title: insight.title,
          description: insight.description
        }
        return acc
      }, {}) || {}
    })
  }

  /**
   * Search for hashtags
   */
  async searchHashtags(query, limit = 25) {
    return await RetryHandler.withRetry(async () => {
      const response = await fetch(
        `${this.baseUrl}/ig_hashtag_search?user_id=${this.businessAccountId}&q=${encodeURIComponent(query)}&limit=${limit}&access_token=${this.accessToken}`
      )

      if (!response.ok) {
        throw new Error(`Failed to search hashtags: ${response.statusText}`)
      }

      const data = await response.json()
      
      return data.data?.map(hashtag => ({
        id: hashtag.id,
        name: hashtag.name
      })) || []
    })
  }

  /**
   * Get hashtag information
   */
  async getHashtagInfo(hashtagId) {
    return await RetryHandler.withRetry(async () => {
      const response = await fetch(
        `${this.baseUrl}/${hashtagId}?fields=id,name&access_token=${this.accessToken}`
      )

      if (!response.ok) {
        throw new Error(`Failed to get hashtag info: ${response.statusText}`)
      }

      return await response.json()
    })
  }

  /**
   * Test connection to Instagram Business API
   */
  async testConnection() {
    try {
      if (!await this.initialize()) {
        return { success: false, error: 'Failed to initialize Instagram Business client' }
      }

      // Try to get profile as a test
      const profile = await this.getProfile()
      
      await AuditLogger.logIntegrationActivity(
        this.userId,
        'instagram_business',
        'connection_test',
        'success',
        { username: profile.username, mediaCount: profile.mediaCount }
      )

      return { 
        success: true, 
        profile,
        message: 'Instagram Business connection successful'
      }
    } catch (error) {
      await AuditLogger.logIntegrationActivity(
        this.userId,
        'instagram_business',
        'connection_test',
        'error',
        { error: error.message }
      )

      return { 
        success: false, 
        error: error.message 
      }
    }
  }
}

export default InstagramBusinessClient
