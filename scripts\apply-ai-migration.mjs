/**
 * Apply Phase 6 AI Features Migration
 * Ocean Soul Sparkles - AI Database Schema Setup
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });
dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Error: Supabase URL or service role key not found in environment variables.');
  console.error('Make sure you have a .env.local file with NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🤖 Starting Phase 6: AI Features Database Migration...');

// Individual SQL statements for AI features
const aiMigrationStatements = [
  // Add AI columns to artist_profiles
  `ALTER TABLE artist_profiles ADD COLUMN IF NOT EXISTS ai_optimization_enabled BOOLEAN DEFAULT true;`,
  `ALTER TABLE artist_profiles ADD COLUMN IF NOT EXISTS last_ai_analysis TIMESTAMP;`,
  `ALTER TABLE artist_profiles ADD COLUMN IF NOT EXISTS ai_performance_score DECIMAL(3,2);`,
  
  // Add AI columns to bookings
  `ALTER TABLE bookings ADD COLUMN IF NOT EXISTS ai_optimized BOOLEAN DEFAULT false;`,
  `ALTER TABLE bookings ADD COLUMN IF NOT EXISTS ai_confidence_score DECIMAL(3,2);`,
  `ALTER TABLE bookings ADD COLUMN IF NOT EXISTS travel_time_minutes INTEGER;`,
  `ALTER TABLE bookings ADD COLUMN IF NOT EXISTS ai_optimization_applied_at TIMESTAMP;`,
  `ALTER TABLE bookings ADD COLUMN IF NOT EXISTS original_start_time TIMESTAMP;`,
  
  // Create AI insights cache table
  `CREATE TABLE IF NOT EXISTS ai_insights_cache (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    cache_key VARCHAR(255) UNIQUE NOT NULL,
    insights_data JSONB NOT NULL,
    insight_type VARCHAR(50) DEFAULT 'daily',
    target_date DATE NOT NULL,
    confidence_level DECIMAL(3,2) DEFAULT 0.5,
    created_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP NOT NULL,
    CONSTRAINT valid_confidence_level CHECK (confidence_level >= 0 AND confidence_level <= 1),
    CONSTRAINT valid_insight_type CHECK (insight_type IN ('daily', 'weekly', 'monthly'))
  );`,
  
  // Create AI recommendations table
  `CREATE TABLE IF NOT EXISTS ai_recommendations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type VARCHAR(50) NOT NULL,
    category VARCHAR(50) NOT NULL,
    priority VARCHAR(20) DEFAULT 'medium',
    target_id UUID,
    target_type VARCHAR(50),
    recommendation_data JSONB NOT NULL,
    confidence_score DECIMAL(3,2) DEFAULT 0.5,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT NOW(),
    applied_at TIMESTAMP,
    dismissed_at TIMESTAMP,
    created_by UUID,
    CONSTRAINT valid_priority CHECK (priority IN ('low', 'medium', 'high')),
    CONSTRAINT valid_status CHECK (status IN ('pending', 'applied', 'dismissed', 'expired')),
    CONSTRAINT valid_confidence CHECK (confidence_score >= 0 AND confidence_score <= 1)
  );`,
  
  // Create AI compatibility scores table
  `CREATE TABLE IF NOT EXISTS ai_compatibility_scores (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL,
    artist_id UUID NOT NULL,
    compatibility_score DECIMAL(4,3) NOT NULL,
    confidence_level DECIMAL(3,2) NOT NULL,
    factors JSONB,
    last_calculated TIMESTAMP DEFAULT NOW(),
    calculation_version VARCHAR(10) DEFAULT '1.0',
    UNIQUE(customer_id, artist_id),
    CONSTRAINT valid_compatibility_score CHECK (compatibility_score >= 0 AND compatibility_score <= 1),
    CONSTRAINT valid_confidence_level CHECK (confidence_level >= 0 AND confidence_level <= 1)
  );`,
  
  // Create AI travel time cache table
  `CREATE TABLE IF NOT EXISTS ai_travel_time_cache (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    origin_location TEXT NOT NULL,
    destination_location TEXT NOT NULL,
    travel_time_minutes INTEGER NOT NULL,
    distance_meters INTEGER,
    traffic_factor DECIMAL(3,2) DEFAULT 1.0,
    departure_time_slot VARCHAR(10),
    day_of_week INTEGER,
    cached_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP NOT NULL,
    source VARCHAR(20) DEFAULT 'google_maps',
    UNIQUE(origin_location, destination_location, departure_time_slot, day_of_week),
    CONSTRAINT valid_day_of_week CHECK (day_of_week >= 0 AND day_of_week <= 6),
    CONSTRAINT valid_travel_time CHECK (travel_time_minutes >= 0),
    CONSTRAINT valid_distance CHECK (distance_meters >= 0)
  );`,
  
  // Create indexes
  `CREATE INDEX IF NOT EXISTS idx_ai_insights_cache_key ON ai_insights_cache(cache_key);`,
  `CREATE INDEX IF NOT EXISTS idx_ai_insights_cache_date ON ai_insights_cache(target_date);`,
  `CREATE INDEX IF NOT EXISTS idx_ai_insights_cache_expires ON ai_insights_cache(expires_at);`,
  `CREATE INDEX IF NOT EXISTS idx_ai_recommendations_type ON ai_recommendations(type);`,
  `CREATE INDEX IF NOT EXISTS idx_ai_recommendations_status ON ai_recommendations(status);`,
  `CREATE INDEX IF NOT EXISTS idx_ai_compatibility_customer ON ai_compatibility_scores(customer_id);`,
  `CREATE INDEX IF NOT EXISTS idx_ai_compatibility_artist ON ai_compatibility_scores(artist_id);`,
  `CREATE INDEX IF NOT EXISTS idx_ai_travel_cache_locations ON ai_travel_time_cache(origin_location, destination_location);`,
  `CREATE INDEX IF NOT EXISTS idx_ai_travel_cache_expires ON ai_travel_time_cache(expires_at);`
];

// AI settings to add
const aiSettings = [
  {
    setting_key: 'ai_features_enabled',
    setting_value: 'true',
    setting_type: 'boolean',
    description: 'Enable AI-powered features and automation'
  },
  {
    setting_key: 'ai_cache_duration',
    setting_value: '1800',
    setting_type: 'number',
    description: 'AI cache duration in seconds (30 minutes)'
  },
  {
    setting_key: 'ai_max_recommendations',
    setting_value: '10',
    setting_type: 'number',
    description: 'Maximum number of AI recommendations to generate'
  },
  {
    setting_key: 'ai_confidence_threshold',
    setting_value: '0.7',
    setting_type: 'number',
    description: 'Minimum confidence threshold for AI recommendations'
  }
];

async function applyMigration() {
  console.log('📊 Applying AI database schema changes...');
  
  let successCount = 0;
  let errorCount = 0;
  
  // Execute each SQL statement
  for (let i = 0; i < aiMigrationStatements.length; i++) {
    const statement = aiMigrationStatements[i];
    console.log(`⚡ Executing statement ${i + 1}/${aiMigrationStatements.length}...`);
    
    try {
      const { error } = await supabase.rpc('execute_sql', { sql: statement });
      
      if (error) {
        console.error(`❌ Error in statement ${i + 1}:`, error.message);
        errorCount++;
      } else {
        console.log(`✅ Statement ${i + 1} executed successfully`);
        successCount++;
      }
    } catch (error) {
      console.error(`❌ Exception in statement ${i + 1}:`, error.message);
      errorCount++;
    }
  }
  
  // Add AI settings
  console.log('⚙️ Adding AI configuration settings...');
  
  for (const setting of aiSettings) {
    try {
      const { error } = await supabase
        .from('admin_settings')
        .upsert([{
          ...setting,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }], {
          onConflict: 'setting_key'
        });
      
      if (error) {
        console.error(`❌ Error adding setting ${setting.setting_key}:`, error.message);
        errorCount++;
      } else {
        console.log(`✅ Setting ${setting.setting_key} added successfully`);
        successCount++;
      }
    } catch (error) {
      console.error(`❌ Exception adding setting ${setting.setting_key}:`, error.message);
      errorCount++;
    }
  }
  
  console.log('\n🎯 Migration Summary:');
  console.log(`✅ Successful operations: ${successCount}`);
  console.log(`❌ Failed operations: ${errorCount}`);
  
  if (errorCount === 0) {
    console.log('\n🚀 Phase 6: AI Features migration completed successfully!');
    console.log('🤖 AI-powered features are now ready for use.');
  } else {
    console.log('\n⚠️ Migration completed with some errors. Please review the output above.');
  }
}

// Test database connection first
async function testConnection() {
  try {
    const { data, error } = await supabase
      .from('admin_settings')
      .select('setting_key')
      .limit(1);
    
    if (error) {
      console.error('❌ Database connection test failed:', error.message);
      return false;
    }
    
    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection test failed:', error.message);
    return false;
  }
}

// Main execution
async function main() {
  try {
    console.log('🔗 Testing database connection...');
    const connected = await testConnection();
    
    if (!connected) {
      console.error('❌ Cannot proceed without database connection');
      process.exit(1);
    }
    
    await applyMigration();
    
  } catch (error) {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
  }
}

main();
