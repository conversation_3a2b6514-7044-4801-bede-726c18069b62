import { createClient } from '@supabase/supabase-js'

/**
 * API endpoint for validating activation tokens
 * POST /api/auth/validate-activation-token - Validate activation token
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7)
  
  console.log(`[${requestId}] Activation token validation API called`)

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { token } = req.body

    if (!token) {
      return res.status(400).json({ 
        valid: false,
        error: 'Activation token is required' 
      })
    }

    console.log(`[${requestId}] Validating token: ${token.substring(0, 8)}...`)

    // Initialize Supabase admin client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          persistSession: false,
          autoRefreshToken: false
        }
      }
    )

    // Validate the activation token
    const { data: tokenData, error: tokenError } = await supabase
      .from('application_tokens')
      .select(`
        *,
        artist_braider_applications (
          id,
          user_id,
          application_type,
          status
        )
      `)
      .eq('token', token)
      .eq('token_type', 'account_activation')
      .single()

    if (tokenError || !tokenData) {
      console.log(`[${requestId}] Token not found or invalid`)
      return res.status(400).json({
        valid: false,
        error: 'Invalid activation token'
      })
    }

    // Check if token has expired
    const now = new Date()
    const expiresAt = new Date(tokenData.expires_at)
    
    if (now > expiresAt) {
      console.log(`[${requestId}] Token has expired`)
      return res.status(400).json({
        valid: false,
        error: 'Activation token has expired'
      })
    }

    // Check if token has already been used
    if (tokenData.used_at) {
      console.log(`[${requestId}] Token has already been used`)
      return res.status(400).json({
        valid: false,
        error: 'Activation token has already been used'
      })
    }

    // Check if application is approved
    const application = tokenData.artist_braider_applications
    if (!application || application.status !== 'approved') {
      console.log(`[${requestId}] Application not approved`)
      return res.status(400).json({
        valid: false,
        error: 'Application is not approved'
      })
    }

    // Get user information
    const { data: userData, error: userError } = await supabase.auth.admin.getUserById(application.user_id)
    
    if (userError || !userData.user) {
      console.error(`[${requestId}] Error fetching user data:`, userError)
      return res.status(500).json({
        valid: false,
        error: 'Failed to fetch user information'
      })
    }

    // Get user profile for name
    const { data: userProfile, error: profileError } = await supabase
      .from('user_profiles')
      .select('name')
      .eq('id', application.user_id)
      .single()

    const userName = userProfile?.name || userData.user.email

    console.log(`[${requestId}] ✅ Token validated successfully for user: ${userName}`)

    return res.status(200).json({
      valid: true,
      user: {
        id: application.user_id,
        email: userData.user.email,
        name: userName,
        role: application.application_type
      },
      application: {
        id: application.id,
        type: application.application_type,
        status: application.status
      }
    })

  } catch (error) {
    console.error(`[${requestId}] Error validating activation token:`, error)
    return res.status(500).json({
      valid: false,
      error: 'Internal server error'
    })
  }
}
