/**
 * Mobile Experience Test Page
 * Ocean Soul Sparkles - Test mobile optimizations
 */

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization'
import MobileDashboardNavigation, { 
  MobileDashboardSection, 
  MobileQuickActionsFAB, 
  MobileTouchCard 
} from '@/components/admin/MobileDashboardNavigation'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import AdminLayout from '@/components/admin/AdminLayout'
import { toast } from 'react-toastify'
import styles from '@/styles/admin/TestMobile.module.css'

export default function TestMobile() {
  const { user } = useAuth()
  const [activeSection, setActiveSection] = useState('overview')
  const [testResults, setTestResults] = useState([])

  const {
    isMobile,
    isTablet,
    isTouchDevice,
    orientation,
    viewport,
    performance,
    hapticFeedback,
    vibrate,
    getOptimalAnimationSettings,
    getOptimalImageQuality,
    shouldReduceAnimations,
    isSmallScreen,
    isLandscape,
    hasHighDPI
  } = useMobileOptimization()

  // Test sections for mobile navigation
  const testSections = [
    {
      id: 'overview',
      label: 'Overview',
      icon: '📱',
      badge: null
    },
    {
      id: 'touch',
      label: 'Touch',
      icon: '👆',
      badge: null
    },
    {
      id: 'gestures',
      label: 'Gestures',
      icon: '👋',
      badge: null
    },
    {
      id: 'performance',
      label: 'Performance',
      icon: '⚡',
      badge: performance.isLowEndDevice ? '!' : null
    }
  ]

  // Test quick actions
  const testQuickActions = [
    {
      id: 'haptic-light',
      label: 'Light Haptic',
      icon: '💫',
      type: 'primary',
      action: () => {
        hapticFeedback('light')
        addTestResult('Haptic Feedback', 'Light haptic triggered')
      }
    },
    {
      id: 'haptic-medium',
      label: 'Medium Haptic',
      icon: '⚡',
      type: 'secondary',
      action: () => {
        hapticFeedback('medium')
        addTestResult('Haptic Feedback', 'Medium haptic triggered')
      }
    },
    {
      id: 'haptic-success',
      label: 'Success Haptic',
      icon: '✅',
      type: 'success',
      action: () => {
        hapticFeedback('success')
        addTestResult('Haptic Feedback', 'Success haptic triggered')
      }
    },
    {
      id: 'vibrate-pattern',
      label: 'Vibrate Pattern',
      icon: '📳',
      type: 'warning',
      action: () => {
        vibrate([200, 100, 200])
        addTestResult('Vibration', 'Pattern vibration triggered')
      }
    }
  ]

  const addTestResult = (category, message) => {
    const timestamp = new Date().toLocaleTimeString()
    setTestResults(prev => [...prev.slice(-9), { // Keep last 10 results
      timestamp,
      category,
      message
    }])
  }

  const runTouchTest = () => {
    addTestResult('Touch Test', 'Touch test initiated')
    hapticFeedback('light')
    toast.success('Touch test completed!')
  }

  const runGestureTest = () => {
    addTestResult('Gesture Test', 'Gesture test initiated')
    hapticFeedback('medium')
    toast.info('Try swiping between sections!')
  }

  const runPerformanceTest = () => {
    const start = performance.now ? performance.now() : Date.now()
    
    // Simulate some work
    setTimeout(() => {
      const end = performance.now ? performance.now() : Date.now()
      const duration = Math.round(end - start)
      addTestResult('Performance Test', `Test completed in ${duration}ms`)
      hapticFeedback('success')
      toast.success(`Performance test: ${duration}ms`)
    }, 100)
  }

  return (
    <ProtectedRoute>
      <AdminLayout>
        <div className={styles.container}>
          <div className={styles.header}>
            <h1>Mobile Experience Test</h1>
            <p>Test mobile optimizations and touch interactions</p>
          </div>

          {/* Device Information */}
          <div className={styles.deviceInfo}>
            <h2>Device Information</h2>
            <div className={styles.infoGrid}>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Device Type:</span>
                <span className={styles.infoValue}>
                  {isMobile ? 'Mobile' : isTablet ? 'Tablet' : 'Desktop'}
                </span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Touch Device:</span>
                <span className={styles.infoValue}>{isTouchDevice ? 'Yes' : 'No'}</span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Orientation:</span>
                <span className={styles.infoValue}>{orientation}</span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Viewport:</span>
                <span className={styles.infoValue}>{viewport.width}×{viewport.height}</span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>High DPI:</span>
                <span className={styles.infoValue}>{hasHighDPI ? 'Yes' : 'No'}</span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Low-end Device:</span>
                <span className={styles.infoValue}>{performance.isLowEndDevice ? 'Yes' : 'No'}</span>
              </div>
            </div>
          </div>

          {/* Mobile Navigation */}
          <MobileDashboardNavigation
            sections={testSections}
            activeSection={activeSection}
            onSectionChange={setActiveSection}
          />

          {/* Mobile Quick Actions FAB */}
          <MobileQuickActionsFAB
            actions={testQuickActions}
            onActionClick={(action) => action.action()}
          />

          {/* Test Sections */}
          <div className={styles.sections}>
            <MobileDashboardSection id="overview" isActive={activeSection === 'overview'}>
              <MobileTouchCard title="Overview">
                <p>This page tests mobile optimizations including:</p>
                <ul>
                  <li>Touch-friendly interface elements</li>
                  <li>Haptic feedback and vibration</li>
                  <li>Swipe navigation between sections</li>
                  <li>Performance optimizations</li>
                  <li>Responsive design</li>
                </ul>
                <div className={styles.optimalSettings}>
                  <h4>Optimal Settings for Your Device:</h4>
                  <p><strong>Animation Duration:</strong> {getOptimalAnimationSettings().animationDuration}ms</p>
                  <p><strong>Image Quality:</strong> {getOptimalImageQuality()}</p>
                  <p><strong>Reduce Animations:</strong> {shouldReduceAnimations ? 'Yes' : 'No'}</p>
                </div>
              </MobileTouchCard>
            </MobileDashboardSection>

            <MobileDashboardSection id="touch" isActive={activeSection === 'touch'}>
              <MobileTouchCard 
                title="Touch Test"
                onTap={runTouchTest}
                onLongPress={() => {
                  hapticFeedback('heavy')
                  addTestResult('Touch Test', 'Long press detected')
                  toast.info('Long press detected!')
                }}
              >
                <p>Tap this card for a touch test, or long press for long press test.</p>
                <button 
                  className={styles.testButton}
                  onClick={runTouchTest}
                >
                  Run Touch Test
                </button>
              </MobileTouchCard>
            </MobileDashboardSection>

            <MobileDashboardSection id="gestures" isActive={activeSection === 'gestures'}>
              <MobileTouchCard title="Gesture Test">
                <p>Test swipe gestures by swiping left/right on the navigation or this card.</p>
                <button 
                  className={styles.testButton}
                  onClick={runGestureTest}
                >
                  Run Gesture Test
                </button>
              </MobileTouchCard>
            </MobileDashboardSection>

            <MobileDashboardSection id="performance" isActive={activeSection === 'performance'}>
              <MobileTouchCard title="Performance Test">
                <p>Test performance optimizations and timing.</p>
                <button 
                  className={styles.testButton}
                  onClick={runPerformanceTest}
                >
                  Run Performance Test
                </button>
                
                <div className={styles.performanceInfo}>
                  <h4>Performance Information:</h4>
                  <p><strong>Connection Speed:</strong> {performance.connectionSpeed}</p>
                  <p><strong>Reduced Motion:</strong> {performance.reducedMotion ? 'Yes' : 'No'}</p>
                  {performance.memoryInfo && (
                    <p><strong>Memory Usage:</strong> {Math.round(performance.memoryInfo.usedJSHeapSize / 1024 / 1024)}MB</p>
                  )}
                </div>
              </MobileTouchCard>
            </MobileDashboardSection>
          </div>

          {/* Test Results */}
          <div className={styles.testResults}>
            <h3>Test Results</h3>
            {testResults.length > 0 ? (
              <div className={styles.resultsList}>
                {testResults.map((result, index) => (
                  <div key={index} className={styles.resultItem}>
                    <span className={styles.resultTime}>{result.timestamp}</span>
                    <span className={styles.resultCategory}>{result.category}</span>
                    <span className={styles.resultMessage}>{result.message}</span>
                  </div>
                ))}
              </div>
            ) : (
              <p>No test results yet. Try the tests above!</p>
            )}
          </div>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}
