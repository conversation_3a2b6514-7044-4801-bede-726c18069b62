-- Ocean Soul Sparkles Service Consolidation Migration
-- This script consolidates individual services into parent services with pricing tiers
-- Run this script in a transaction to ensure data integrity

BEGIN;

-- Create a backup table for the original services
CREATE TABLE services_backup AS SELECT * FROM services;

-- Create mapping table to track the consolidation
CREATE TABLE service_consolidation_mapping (
  old_service_id UUID,
  new_parent_service_id UUID,
  tier_name TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 1. BODY PAINTING CONSOLIDATION
-- Create parent service for Body Painting
INSERT INTO services (
  id, name, description, duration, price, color, category, status, 
  visible_on_public, visible_on_pos, visible_on_events, featured, created_at, updated_at
) VALUES (
  gen_random_uuid(),
  'Body Painting',
  'Professional body painting services with various coverage options from small details to full body art',
  15, -- Default duration (medium tier)
  65.00, -- Default price (medium tier)
  '#FF6B6B',
  'Body Painting',
  'active',
  false,
  true,
  true,
  false,
  NOW(),
  NOW()
) RETURNING id AS body_painting_parent_id;

-- Store the parent ID for reference
DO $$
DECLARE
  body_painting_parent_id UUID;
BEGIN
  SELECT id INTO body_painting_parent_id FROM services WHERE name = 'Body Painting' AND category = 'Body Painting';
  
  -- Create pricing tiers for Body Painting
  INSERT INTO service_pricing_tiers (service_id, name, description, duration, price, is_default, sort_order) VALUES
  (body_painting_parent_id, 'Small', 'Small area body painting', 5, 40.00, false, 1),
  (body_painting_parent_id, 'Medium', 'Medium area body painting', 10, 65.00, true, 2),
  (body_painting_parent_id, 'Large', 'Large area body painting', 15, 90.00, false, 3),
  (body_painting_parent_id, 'XLarge', 'Extra large area body painting', 20, 150.00, false, 4),
  (body_painting_parent_id, 'Variable', 'Custom pricing for special requests', 30, 0.00, false, 5);
  
  -- Create mapping records
  INSERT INTO service_consolidation_mapping (old_service_id, new_parent_service_id, tier_name) VALUES
  ('450c8225-534c-4e6a-8e2c-9af751a25d74', body_painting_parent_id, 'Small'),
  ('33c521a6-783d-4062-ae16-8aa187074819', body_painting_parent_id, 'Medium'),
  ('208a4478-6ca6-4a16-9ab1-68599dbb768f', body_painting_parent_id, 'Large'),
  ('f47cb28e-1732-4be3-9a00-9ed4c7563b98', body_painting_parent_id, 'XLarge'),
  ('6caa9188-61ef-4a2f-a1c7-b4ba86b4aa7e', body_painting_parent_id, 'Variable');
END $$;

-- 2. FACE PAINTING CONSOLIDATION
INSERT INTO services (
  id, name, description, duration, price, color, category, status, 
  visible_on_public, visible_on_pos, visible_on_events, featured, created_at, updated_at
) VALUES (
  gen_random_uuid(),
  'Face Painting',
  'Creative face painting for all ages and occasions, from simple designs to elaborate artwork',
  10, -- Default duration
  40.00, -- Default price
  '#4ECDC4',
  'Face Painting',
  'active',
  false,
  true,
  true,
  false,
  NOW(),
  NOW()
);

DO $$
DECLARE
  face_painting_parent_id UUID;
BEGIN
  SELECT id INTO face_painting_parent_id FROM services WHERE name = 'Face Painting' AND category = 'Face Painting';
  
  -- Create pricing tiers for Face Painting
  INSERT INTO service_pricing_tiers (service_id, name, description, duration, price, is_default, sort_order) VALUES
  (face_painting_parent_id, 'Children', 'Face painting for children under 12', 5, 15.00, false, 1),
  (face_painting_parent_id, 'Kids', 'Special face painting for kids', 5, 20.00, false, 2),
  (face_painting_parent_id, 'Standard', 'Standard face painting service', 10, 40.00, true, 3),
  (face_painting_parent_id, 'Face + Small', 'Face painting with small additional area', 5, 55.00, false, 4),
  (face_painting_parent_id, 'Face + Medium', 'Face painting with medium additional area', 10, 80.00, false, 5),
  (face_painting_parent_id, 'Face + Large', 'Face painting with large additional area', 15, 110.00, false, 6),
  (face_painting_parent_id, 'Professional', 'Professional makeup and face painting appointment', 60, 60.00, false, 7);
  
  -- Create mapping records
  INSERT INTO service_consolidation_mapping (old_service_id, new_parent_service_id, tier_name) VALUES
  ('5dc6fabd-dc46-44d0-b077-99b4f3fb8cdf', face_painting_parent_id, 'Children'),
  ('71febb5e-e333-415b-bd71-1e2760c6075b', face_painting_parent_id, 'Kids'),
  ('df6f5613-6df0-4e5e-ada6-af6091cfd54b', face_painting_parent_id, 'Face + Small'),
  ('28b12de4-657e-493c-a40b-40dfbeb58db2', face_painting_parent_id, 'Face + Medium'),
  ('467de4b6-4d55-4753-8cb9-e5cf66966852', face_painting_parent_id, 'Face + Large'),
  ('b7bffa48-a096-4405-ac8d-29695ed9ea62', face_painting_parent_id, 'Professional');
END $$;

-- 3. AIRBRUSH CONSOLIDATION
INSERT INTO services (
  id, name, description, duration, price, color, category, status, 
  visible_on_public, visible_on_pos, visible_on_events, featured, created_at, updated_at
) VALUES (
  gen_random_uuid(),
  'Airbrush Face & Body Painting',
  'Professional airbrush services for face and body art with vibrant, long-lasting results',
  45, -- Default duration
  150.00, -- Default price
  '#45B7D1',
  'Airbrush',
  'active',
  true,
  false,
  false,
  false,
  NOW(),
  NOW()
);

DO $$
DECLARE
  airbrush_parent_id UUID;
BEGIN
  SELECT id INTO airbrush_parent_id FROM services WHERE name = 'Airbrush Face & Body Painting' AND category = 'Airbrush';
  
  -- Create pricing tiers for Airbrush
  INSERT INTO service_pricing_tiers (service_id, name, description, duration, price, is_default, sort_order) VALUES
  (airbrush_parent_id, 'Face Paint', 'Airbrush face painting', 5, 30.00, false, 1),
  (airbrush_parent_id, 'Neck Tattoo', 'Airbrush neck tattoo design', 5, 40.00, false, 2),
  (airbrush_parent_id, 'Face + Neck', 'Popular combination of face and neck airbrush', 5, 45.00, false, 3),
  (airbrush_parent_id, 'Standard Session', 'Standard airbrush session', 45, 150.00, true, 4),
  (airbrush_parent_id, 'Full Torso', 'Complete airbrush coverage for torso and extremities', 45, 275.00, false, 5),
  (airbrush_parent_id, 'Event Package', 'Full event airbrush package', 120, 350.00, false, 6),
  (airbrush_parent_id, 'Temporary Tattoos', 'Professional airbrush temporary tattoo services', 120, 350.00, false, 7);
  
  -- Create mapping records
  INSERT INTO service_consolidation_mapping (old_service_id, new_parent_service_id, tier_name) VALUES
  ('ad4fd3c5-31d5-4076-8262-1d59c2131ad3', airbrush_parent_id, 'Face Paint'),
  ('9b2a33d4-41d4-4f6a-aa87-8ef66140bdf7', airbrush_parent_id, 'Neck Tattoo'),
  ('23fcb7e2-6141-465f-bb73-dd146a6bc4f4', airbrush_parent_id, 'Face + Neck'),
  ('c4509415-64a5-41ed-9066-c6cf2c1c610f', airbrush_parent_id, 'Full Torso'),
  ('c35117a3-a451-4884-8a29-c930372d8463', airbrush_parent_id, 'Event Package'),
  ('903ccf92-fcee-48e5-b121-e38bfe9baacc', airbrush_parent_id, 'Event Package'),
  ('*************-4cdc-baa3-75428af990f1', airbrush_parent_id, 'Temporary Tattoos');
END $$;

-- 4. GLITTER & GEMS CONSOLIDATION
INSERT INTO services (
  id, name, description, duration, price, color, category, status, 
  visible_on_public, visible_on_pos, visible_on_events, featured, created_at, updated_at
) VALUES (
  gen_random_uuid(),
  'Glitter & Gem Application',
  'Sparkling glitter and gem application services for face and body with premium materials',
  5, -- Default duration
  35.00, -- Default price
  '#F7DC6F',
  'Glitter & Gems',
  'active',
  false,
  true,
  true,
  false,
  NOW(),
  NOW()
);

DO $$
DECLARE
  glitter_parent_id UUID;
BEGIN
  SELECT id INTO glitter_parent_id FROM services WHERE name = 'Glitter & Gem Application' AND category = 'Glitter & Gems';

  -- Create pricing tiers for Glitter & Gems
  INSERT INTO service_pricing_tiers (service_id, name, description, duration, price, is_default, sort_order) VALUES
  (glitter_parent_id, 'One Side', 'Glitter application on one side', 5, 25.00, false, 1),
  (glitter_parent_id, 'Both Sides', 'Glitter application on both sides', 5, 30.00, true, 2),
  (glitter_parent_id, 'Short Beard', 'Glitter application for short beard', 5, 30.00, false, 3),
  (glitter_parent_id, 'Shoulder & Eye', 'Glitter on shoulder and one eye', 5, 40.00, false, 4),
  (glitter_parent_id, 'Long Beard', 'Glitter application for long beard', 5, 40.00, false, 5),
  (glitter_parent_id, 'Standard', 'Standard glitter and gem application', 30, 45.00, false, 6),
  (glitter_parent_id, 'Chest', 'Glitter application on chest area', 10, 50.00, false, 7),
  (glitter_parent_id, 'Butt', 'Glitter application on buttocks area', 10, 55.00, false, 8),
  (glitter_parent_id, 'Boobs', 'Glitter application on breast area', 10, 60.00, false, 9);

  -- Create mapping records
  INSERT INTO service_consolidation_mapping (old_service_id, new_parent_service_id, tier_name) VALUES
  ('92e64aea-271c-45bf-987a-d72869d54109', glitter_parent_id, 'One Side'),
  ('8be49c44-b7c0-426a-84d6-32dbd7ae6bbf', glitter_parent_id, 'Both Sides'),
  ('138de135-3a16-4327-8ecf-66f9a472cdc4', glitter_parent_id, 'Short Beard'),
  ('59d06c5e-e6da-42c6-900d-c65d2746419a', glitter_parent_id, 'Shoulder & Eye'),
  ('fc4ddb47-f30d-485e-ab1f-b33ae019ff76', glitter_parent_id, 'Long Beard'),
  ('496406b3-9ad0-4032-a6ed-516a5bdb7ed6', glitter_parent_id, 'Standard'),
  ('d412c98c-be14-4b7e-939f-2cf6c719dde6', glitter_parent_id, 'Chest'),
  ('72ea5340-fed6-4385-9d1e-c5deb7d1d23a', glitter_parent_id, 'Butt'),
  ('5b5d3c42-9415-4c47-a733-dfbdaad30e42', glitter_parent_id, 'Boobs');
END $$;

-- 5. HAIR & BRAIDING CONSOLIDATION
INSERT INTO services (
  id, name, description, duration, price, color, category, status,
  visible_on_public, visible_on_pos, visible_on_events, featured, created_at, updated_at
) VALUES (
  gen_random_uuid(),
  'Hair Braiding & Styling',
  'Professional braiding and hair styling services including festival braids, extensions, and custom designs',
  40, -- Default duration
  80.00, -- Default price
  '#BB8FCE',
  'Hair & Braiding',
  'active',
  false,
  true,
  true,
  false,
  NOW(),
  NOW()
);

DO $$
DECLARE
  hair_parent_id UUID;
BEGIN
  SELECT id INTO hair_parent_id FROM services WHERE name = 'Hair Braiding & Styling' AND category = 'Hair & Braiding';

  -- Create pricing tiers for Hair & Braiding (first batch)
  INSERT INTO service_pricing_tiers (service_id, name, description, duration, price, is_default, sort_order) VALUES
  (hair_parent_id, '1 Braid', 'Single braid styling', 20, 40.00, false, 1),
  (hair_parent_id, '2 Braids', 'Two braid styling', 30, 55.00, false, 2),
  (hair_parent_id, '3 Side Braids', 'Three side braids styling', 30, 55.00, false, 3),
  (hair_parent_id, 'Standard Package', 'Standard braiding package', 40, 80.00, true, 4),
  (hair_parent_id, '2 Boxer + Extensions', 'Two boxer braids with extensions', 40, 100.00, false, 5),
  (hair_parent_id, '4 Boxer No EXT', 'Four boxer braids without extensions', 60, 100.00, false, 6),
  (hair_parent_id, '2 Small Front/Back', 'Two small braids at front or back', 40, 55.00, false, 7),
  (hair_parent_id, '2 Small + Space Buns', 'Two small front braids with space buns', 40, 95.00, false, 8),
  (hair_parent_id, '6 Boxer No EXT', 'Six boxer braids without extensions', 80, 140.00, false, 9),
  (hair_parent_id, 'Viking Style', 'Viking style with mohawk and 4-6 side braids', 45, 140.00, false, 10);

  -- Create mapping records for Hair & Braiding (first batch)
  INSERT INTO service_consolidation_mapping (old_service_id, new_parent_service_id, tier_name) VALUES
  ('17d871f5-688f-4c8b-bc46-7202575199da', hair_parent_id, '1 Braid'),
  ('5d3cdd58-e6d5-4093-9374-b4aece73b195', hair_parent_id, '2 Braids'),
  ('57fcfcd8-b1f7-40bb-b3b4-e096f7a656c8', hair_parent_id, '3 Side Braids'),
  ('aa79ddc2-6a18-444e-8350-da044ac5bf6e', hair_parent_id, '2 Boxer + Extensions'),
  ('3f00aed1-5d17-4b51-a547-f014530fcee6', hair_parent_id, '4 Boxer No EXT'),
  ('62b81164-a869-4aad-b5ee-a00dd7d95ac5', hair_parent_id, '2 Small Front/Back'),
  ('9c8ec938-a9a9-4258-8b50-73a1b1d42bca', hair_parent_id, '2 Small + Space Buns'),
  ('1f62c69a-78f7-4f7b-8abd-54f9c0c27b74', hair_parent_id, '6 Boxer No EXT'),
  ('861dbf73-af84-4350-a8e4-255d94230abd', hair_parent_id, 'Viking Style');
END $$;

-- Continue with remaining Hair & Braiding tiers and other services...

COMMIT;
