/**
 * Consent Update API Endpoint for Ocean Soul Sparkles
 * Updates user consent preferences
 * 
 * Phase 9.2: Data Protection & Privacy Compliance
 */

import { withAdminAuth } from '@/lib/admin-auth'
import { privacyManager } from '@/lib/compliance/privacy-manager'

async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { 
      userId, 
      customerId, 
      consentType, 
      consentGiven, 
      consentMethod = 'explicit',
      consentSource = 'website'
    } = req.body

    if (!userId && !customerId) {
      return res.status(400).json({ 
        error: 'Missing required fields',
        message: 'Either userId or customerId is required'
      })
    }

    if (!consentType || typeof consentGiven !== 'boolean') {
      return res.status(400).json({ 
        error: 'Missing required fields',
        message: 'consentType and consentGiven are required'
      })
    }

    // Get client IP and user agent for audit trail
    const ipAddress = req.headers['x-forwarded-for']?.split(',')[0] || 
                     req.headers['x-real-ip'] || 
                     req.connection?.remoteAddress || 
                     'unknown'
    const userAgent = req.headers['user-agent'] || 'unknown'

    // Record consent
    const consent = await privacyManager.recordConsent({
      userId,
      customerId,
      consentType,
      consentGiven,
      consentMethod,
      consentSource,
      ipAddress,
      userAgent
    })

    res.status(200).json({
      success: true,
      consent,
      message: `Consent ${consentGiven ? 'granted' : 'withdrawn'} successfully`
    })

  } catch (error) {
    console.error('Consent update error:', error)
    res.status(500).json({
      error: 'Failed to update consent',
      message: error.message
    })
  }
}

export default withAdminAuth(handler)
