/* Quick Actions Styles - Phase 8: Advanced Customer Experience */

.quickActions {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
  max-width: 400px;
}

/* Actions Grid */
.actionsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.actionButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 1.5rem 1rem;
  background: white;
  border: 2px solid transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: #333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.actionButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border-color: var(--action-color, #4ECDC4);
}

.actionButton:active {
  transform: translateY(0);
}

.actionButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.actionButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--action-color, #4ECDC4);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.actionButton:hover::before {
  transform: scaleX(1);
}

.actionIcon {
  font-size: 2rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--action-color, #4ECDC4);
  color: white;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.actionButton:hover .actionIcon {
  transform: scale(1.1);
}

.spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.actionContent {
  text-align: center;
  flex: 1;
}

.actionLabel {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.actionDescription {
  font-size: 0.8rem;
  color: #666;
  line-height: 1.3;
}

/* Emergency Actions */
.emergencyActions {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

.emergencyButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.emergencyButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(255, 107, 107, 0.4);
}

.emergencyButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.emergencyIcon {
  font-size: 1.2rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.emergencyLabel {
  font-size: 0.9rem;
}

/* Quick Links */
.quickLinks {
  display: flex;
  justify-content: space-around;
  gap: 0.5rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

.quickLink {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  text-decoration: none;
  color: #666;
  border-radius: 8px;
  transition: all 0.3s ease;
  flex: 1;
  text-align: center;
}

.quickLink:hover {
  background: #f8f9fa;
  color: #4ECDC4;
  transform: translateY(-1px);
}

.quickLinkIcon {
  font-size: 1.2rem;
}

.quickLinkLabel {
  font-size: 0.8rem;
  font-weight: 500;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .quickActions {
    max-width: 100%;
  }

  .actionsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .actionButton {
    padding: 1rem 0.75rem;
  }

  .actionIcon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
  }

  .actionLabel {
    font-size: 0.9rem;
  }

  .actionDescription {
    font-size: 0.7rem;
  }

  .quickLinks {
    flex-direction: column;
    gap: 0.25rem;
  }

  .quickLink {
    flex-direction: row;
    justify-content: flex-start;
    text-align: left;
  }
}

@media (max-width: 480px) {
  .actionsGrid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .actionButton {
    flex-direction: row;
    text-align: left;
    padding: 1rem;
  }

  .actionIcon {
    flex-shrink: 0;
  }

  .actionContent {
    text-align: left;
    flex: 1;
  }

  .emergencyButton {
    padding: 0.75rem;
  }

  .emergencyIcon {
    font-size: 1rem;
  }

  .emergencyLabel {
    font-size: 0.8rem;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .actionButton {
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }

  .actionButton:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .actionButton {
    background: #2d2d2d;
    color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .actionLabel {
    color: #fff;
  }

  .actionDescription {
    color: #ccc;
  }

  .quickLink {
    color: #ccc;
  }

  .quickLink:hover {
    background: #3d3d3d;
    color: #4ECDC4;
  }

  .emergencyActions,
  .quickLinks {
    border-top-color: #444;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .actionButton,
  .actionIcon,
  .emergencyButton,
  .quickLink {
    transition: none;
  }

  .actionButton:hover .actionIcon {
    transform: none;
  }

  .emergencyIcon {
    animation: none;
  }

  .spinner {
    animation: none;
  }
}

/* Print Styles */
@media print {
  .quickActions {
    display: none;
  }
}
