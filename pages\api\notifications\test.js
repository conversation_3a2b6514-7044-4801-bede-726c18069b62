import { supabase } from '@/lib/supabase';
import { sendOneSignalPush, sendOneSignalEmail } from '@/lib/notifications-server';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * Test Notification API Endpoint
 * Sends test notifications to verify user settings and delivery
 */
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });
  }

  try {
    // Authenticate request
    const authResult = await authenticateAdminRequest(req);
    if (!authResult.success) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const {
      user_id,
      title = 'Test Notification',
      message = 'This is a test notification to verify your settings are working correctly.',
      channels = ['push']
    } = req.body;

    // Validate required fields
    if (!user_id) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }

    // Get user information and preferences
    const { data: user, error: userError } = await supabase
      .from('user_profiles')
      .select(`
        id,
        name,
        email,
        role,
        notification_preferences(
          email_notifications,
          push_notifications,
          sms_notifications,
          phone_number
        )
      `)
      .eq('id', user_id)
      .single();

    if (userError) {
      throw userError;
    }

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    const preferences = user.notification_preferences || {};
    const testResults = [];

    // Test notification data
    const notificationData = {
      type: 'test',
      test_id: `test_${Date.now()}`,
      sent_at: new Date().toISOString()
    };

    // Send push notification test
    if (channels.includes('push')) {
      try {
        if (preferences.push_notifications === false) {
          testResults.push({
            type: 'push',
            success: false,
            error: 'Push notifications are disabled in user preferences'
          });
        } else {
          const pushResult = await sendOneSignalPush({
            userIds: [user_id],
            title: `🧪 ${title}`,
            message: `${message}\n\nTest sent at: ${new Date().toLocaleTimeString()}`,
            data: {
              ...notificationData,
              user_id
            }
          });

          testResults.push({
            type: 'push',
            success: true,
            result: pushResult,
            message: 'Push notification sent successfully'
          });
        }
      } catch (error) {
        console.error('Push notification test failed:', error);
        testResults.push({
          type: 'push',
          success: false,
          error: error.message
        });
      }
    }

    // Send email notification test
    if (channels.includes('email')) {
      try {
        if (preferences.email_notifications === false) {
          testResults.push({
            type: 'email',
            success: false,
            error: 'Email notifications are disabled in user preferences'
          });
        } else if (!user.email) {
          testResults.push({
            type: 'email',
            success: false,
            error: 'No email address found for user'
          });
        } else {
          const emailResult = await sendOneSignalEmail({
            email: user.email,
            subject: `🧪 ${title}`,
            message: `${message}\n\nTest sent at: ${new Date().toLocaleString()}`,
            htmlBody: generateTestEmailHTML(title, message, user.name),
            data: {
              ...notificationData,
              user_id
            }
          });

          testResults.push({
            type: 'email',
            success: true,
            result: emailResult,
            message: `Email sent successfully to ${user.email}`
          });
        }
      } catch (error) {
        console.error('Email notification test failed:', error);
        testResults.push({
          type: 'email',
          success: false,
          error: error.message
        });
      }
    }

    // Send SMS notification test (if implemented)
    if (channels.includes('sms')) {
      if (preferences.sms_notifications === false) {
        testResults.push({
          type: 'sms',
          success: false,
          error: 'SMS notifications are disabled in user preferences'
        });
      } else if (!preferences.phone_number) {
        testResults.push({
          type: 'sms',
          success: false,
          error: 'No phone number found for user'
        });
      } else {
        // SMS implementation would go here
        testResults.push({
          type: 'sms',
          success: false,
          error: 'SMS notifications not yet implemented'
        });
      }
    }

    // Record test notification in database
    try {
      await supabase
        .from('notifications')
        .insert([
          {
            user_id,
            title: `🧪 ${title}`,
            message: `${message}\n\nThis was a test notification.`,
            notification_type: 'test',
            related_id: notificationData.test_id,
            is_read: false
          }
        ]);
    } catch (error) {
      console.error('Failed to record test notification:', error);
      // Don't fail the request if recording fails
    }

    // Determine overall success
    const hasSuccessfulDelivery = testResults.some(result => result.success);
    const allRequestedChannelsFailed = testResults.every(result => !result.success);

    return res.status(200).json({
      success: hasSuccessfulDelivery,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role
      },
      test_results: testResults,
      summary: {
        total_channels: testResults.length,
        successful: testResults.filter(r => r.success).length,
        failed: testResults.filter(r => !r.success).length,
        overall_status: hasSuccessfulDelivery ? 'success' : 'failed'
      },
      recommendations: generateRecommendations(testResults, preferences, user)
    });

  } catch (error) {
    console.error('Test notification error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to send test notification: ' + error.message
    });
  }
}

/**
 * Generate HTML email template for test notifications
 */
function generateTestEmailHTML(title, message, userName) {
  return `
    <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background: white;">
      <div style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); padding: 20px; text-align: center; border-radius: 8px 8px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 24px;">🧪 Test Notification</h1>
        <p style="color: #bfdbfe; margin: 8px 0 0 0; font-size: 14px;">Ocean Soul Sparkles Notification System</p>
      </div>
      
      <div style="padding: 30px; background: #f8fafc; border-radius: 0 0 8px 8px;">
        <h2 style="color: #1f2937; margin: 0 0 16px 0; font-size: 20px;">Hello ${userName || 'there'}!</h2>
        
        <div style="background: white; padding: 20px; border-radius: 6px; border: 1px solid #e5e7eb; margin-bottom: 20px;">
          <h3 style="color: #374151; margin: 0 0 12px 0; font-size: 18px;">${title}</h3>
          <p style="color: #6b7280; margin: 0; line-height: 1.6;">${message}</p>
        </div>
        
        <div style="background: #ecfdf5; border: 1px solid #10b981; border-radius: 6px; padding: 16px; margin-bottom: 20px;">
          <p style="color: #065f46; margin: 0; font-weight: 600;">✅ Test Successful!</p>
          <p style="color: #047857; margin: 4px 0 0 0; font-size: 14px;">
            Your email notifications are working correctly. You should receive this message in your inbox.
          </p>
        </div>
        
        <div style="text-align: center; padding: 20px; background: white; border-radius: 6px; border: 1px solid #e5e7eb;">
          <p style="color: #6b7280; margin: 0; font-size: 14px;">
            Test sent at: ${new Date().toLocaleString()}<br>
            Ocean Soul Sparkles Notification System
          </p>
        </div>
      </div>
    </div>
  `;
}

/**
 * Generate recommendations based on test results
 */
function generateRecommendations(testResults, preferences, user) {
  const recommendations = [];

  // Check for failed deliveries
  const failedResults = testResults.filter(r => !r.success);
  
  if (failedResults.length > 0) {
    failedResults.forEach(result => {
      switch (result.type) {
        case 'push':
          if (result.error.includes('disabled in user preferences')) {
            recommendations.push({
              type: 'preference',
              message: 'Enable push notifications in your notification preferences to receive instant alerts.'
            });
          } else {
            recommendations.push({
              type: 'technical',
              message: 'Push notification delivery failed. Check your device settings and ensure the app has notification permissions.'
            });
          }
          break;
          
        case 'email':
          if (result.error.includes('disabled in user preferences')) {
            recommendations.push({
              type: 'preference',
              message: 'Enable email notifications in your notification preferences to receive email alerts.'
            });
          } else if (result.error.includes('No email address')) {
            recommendations.push({
              type: 'profile',
              message: 'Add an email address to your profile to receive email notifications.'
            });
          } else {
            recommendations.push({
              type: 'technical',
              message: 'Email delivery failed. Check your email address and spam folder.'
            });
          }
          break;
          
        case 'sms':
          if (result.error.includes('disabled in user preferences')) {
            recommendations.push({
              type: 'preference',
              message: 'Enable SMS notifications in your notification preferences to receive text alerts.'
            });
          } else if (result.error.includes('No phone number')) {
            recommendations.push({
              type: 'profile',
              message: 'Add a phone number to your profile to receive SMS notifications.'
            });
          }
          break;
      }
    });
  }

  // Check for missing contact information
  if (!user.email) {
    recommendations.push({
      type: 'profile',
      message: 'Add an email address to your profile to enable email notifications.'
    });
  }

  if (!preferences.phone_number) {
    recommendations.push({
      type: 'profile',
      message: 'Add a phone number to your profile to enable SMS notifications (when available).'
    });
  }

  // Success message if all tests passed
  if (testResults.every(r => r.success)) {
    recommendations.push({
      type: 'success',
      message: 'All notification channels are working correctly! You\'re all set to receive notifications.'
    });
  }

  return recommendations;
}
