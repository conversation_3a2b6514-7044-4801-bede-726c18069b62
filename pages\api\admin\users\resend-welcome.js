import { getAdminClient } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'

/**
 * API endpoint for resending welcome emails with new application tokens
 * POST /api/admin/users/resend-welcome - Regenerate token and resend welcome email
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7)
  console.log(`[${requestId}] Resend welcome email API called`)

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Verify admin authentication
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.authorized) {
      return res.status(401).json({ error: authResult.error?.message || 'Unauthorized' })
    }

    const { user: adminUser, role: adminRole } = authResult

    // Ensure user has admin privileges
    if (!['dev', 'admin'].includes(adminRole)) {
      return res.status(403).json({ error: 'Admin privileges required' })
    }

    const { userId, forceResend = false } = req.body

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' })
    }

    console.log(`[${requestId}] Resending welcome email for user: ${userId}`)

    // Get admin client
    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Failed to initialize admin client' })
    }

    // Get user details
    const { data: userData, error: userError } = await adminClient.auth.admin.getUserById(userId)
    if (userError || !userData.user) {
      console.error(`[${requestId}] Error fetching user:`, userError)
      return res.status(404).json({ error: 'User not found' })
    }

    // Get user role
    const { data: userRole, error: roleError } = await adminClient
      .from('user_roles')
      .select('role')
      .eq('id', userId)
      .single()

    if (roleError || !userRole) {
      console.error(`[${requestId}] Error fetching user role:`, roleError)
      return res.status(404).json({ error: 'User role not found' })
    }

    // Verify user is artist or braider
    if (!['artist', 'braider'].includes(userRole.role)) {
      return res.status(400).json({ error: 'User must be an Artist or Braider to resend welcome email' })
    }

    // Get user profile for name
    const { data: userProfile } = await adminClient
      .from('user_profiles')
      .select('name')
      .eq('id', userId)
      .single()

    // Get or create application entry
    let { data: application, error: appError } = await adminClient
      .from('artist_braider_applications')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (appError && appError.code === 'PGRST116') {
      // Application doesn't exist, create it
      console.log(`[${requestId}] Creating new application entry for user`)
      const { data: newApplication, error: createError } = await adminClient
        .from('artist_braider_applications')
        .insert([
          {
            user_id: userId,
            application_type: userRole.role,
            status: 'pending',
            welcome_email_sent: false
          }
        ])
        .select()
        .single()

      if (createError) {
        console.error(`[${requestId}] Error creating application:`, createError)
        return res.status(500).json({ error: 'Failed to create application entry' })
      }

      application = newApplication
    } else if (appError) {
      console.error(`[${requestId}] Error fetching application:`, appError)
      return res.status(500).json({ error: 'Failed to fetch application' })
    }

    // Invalidate existing tokens for this user
    console.log(`[${requestId}] Invalidating existing tokens for user`)
    const { error: invalidateError } = await adminClient
      .from('application_tokens')
      .update({
        is_used: true,
        used_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('is_used', false)

    if (invalidateError) {
      console.error(`[${requestId}] Error invalidating existing tokens:`, invalidateError)
      // Continue anyway - not critical
    }

    // Generate new secure application token
    console.log(`[${requestId}] Generating new application token`)
    const { data: tokenData, error: tokenError } = await adminClient
      .rpc('generate_application_token')

    if (tokenError || !tokenData) {
      console.error(`[${requestId}] Error generating token:`, tokenError)
      return res.status(500).json({ error: 'Failed to generate application token' })
    }

    const applicationToken = tokenData
    console.log(`[${requestId}] Generated new token: ${applicationToken.substring(0, 8)}...`)

    // Store new token in database
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7) // Token expires in 7 days

    // Prepare token data
    const tokenInsertData = {
      user_id: userId,
      application_id: application.id,
      token: applicationToken,
      token_type: 'application_access',
      expires_at: expiresAt.toISOString()
    }

    // Only set created_by if the admin user exists in auth.users table
    // Skip for development admin to avoid foreign key constraint violation
    if (adminUser.id !== '00000000-0000-4000-8000-000000000001') {
      tokenInsertData.created_by = adminUser.id
    } else {
      console.log(`[${requestId}] Skipping created_by field for development admin to avoid foreign key constraint`)
    }

    const { error: tokenInsertError } = await adminClient
      .from('application_tokens')
      .insert([tokenInsertData])

    if (tokenInsertError) {
      console.error(`[${requestId}] Error storing token:`, tokenInsertError)
      return res.status(500).json({ error: 'Failed to store application token' })
    }

    // Update application status to pending and mark email as being resent
    const { error: updateError } = await adminClient
      .from('artist_braider_applications')
      .update({
        status: 'pending',
        welcome_email_sent: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', application.id)

    if (updateError) {
      console.error(`[${requestId}] Error updating application:`, updateError)
      // Continue anyway - not critical
    }

    // Send welcome email with new token
    console.log(`[${requestId}] Sending welcome email with new token`)
    let emailResult = { success: false, error: 'Email service not available' }

    try {
      const { sendWelcomeNotification } = await import('@/lib/notifications-server')

      emailResult = await sendWelcomeNotification({
        userId: userId,
        email: userData.user.email,
        name: userProfile?.name || userData.user.email,
        role: userRole.role,
        applicationToken: applicationToken
      })

      console.log(`[${requestId}] Welcome email result:`, emailResult)
    } catch (emailError) {
      console.error(`[${requestId}] Error sending welcome email:`, emailError)
      emailResult = { success: false, error: emailError.message }
    }

    // Update application to mark email as sent (if successful)
    if (emailResult.success) {
      await adminClient
        .from('artist_braider_applications')
        .update({
          welcome_email_sent: true,
          last_email_sent_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', application.id)
    }

    // Log the action
    try {
      await adminClient
        .from('admin_activity_log')
        .insert([
          {
            admin_user_id: adminUser.id,
            action: 'resend_welcome_email',
            target_type: 'user',
            target_id: userId,
            details: {
              user_email: userData.user.email,
              user_role: userRole.role,
              token_generated: true,
              email_sent: emailResult.success,
              application_id: application.id
            }
          }
        ])
    } catch (logError) {
      console.error(`[${requestId}] Error logging activity:`, logError)
      // Continue anyway
    }

    console.log(`[${requestId}] Welcome email resend completed successfully`)

    return res.status(200).json({
      success: true,
      message: 'Welcome email resent successfully',
      data: {
        userId: userId,
        email: userData.user.email,
        role: userRole.role,
        tokenGenerated: true,
        emailSent: emailResult.success,
        emailError: emailResult.success ? null : emailResult.error,
        applicationId: application.id,
        tokenExpires: expiresAt.toISOString()
      }
    })

  } catch (error) {
    console.error(`[${requestId}] Unexpected error in resend welcome email:`, error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
