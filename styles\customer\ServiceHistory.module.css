/* Service History Styles - Phase 8: Advanced Customer Experience */

.serviceHistory {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  padding: 1rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.count {
  font-size: 0.9rem;
  color: #666;
  background: #f8f9fa;
  padding: 0.5rem 1rem;
  border-radius: 20px;
}

/* Empty State */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.emptyTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.emptyDescription {
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.5;
}

.bookNowButton {
  background: #4ECDC4;
  color: white;
  text-decoration: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.bookNowButton:hover {
  background: #44A08D;
  transform: translateY(-1px);
}

/* History List */
.historyList {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.serviceCard {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.serviceCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Service Header */
.serviceHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.serviceInfo {
  flex: 1;
}

.serviceName {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.serviceDate {
  font-size: 0.9rem;
  color: #666;
}

.existingRating {
  display: flex;
  align-items: center;
}

.rateButton {
  background: #4ECDC4;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.rateButton:hover {
  background: #44A08D;
  transform: translateY(-1px);
}

/* Star Rating */
.starRating {
  display: flex;
  gap: 0.25rem;
}

.star {
  cursor: pointer;
  transition: all 0.2s ease;
}

.star.filled {
  color: #ffc107;
}

.star.empty {
  color: #e9ecef;
}

.star.interactive:hover {
  transform: scale(1.2);
}

.star.small {
  font-size: 0.9rem;
}

.star.medium {
  font-size: 1.2rem;
}

.star.large {
  font-size: 1.5rem;
}

/* Service Details */
.serviceDetails {
  margin-bottom: 1rem;
}

.detailRow {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f8f9fa;
}

.detailRow:last-child {
  border-bottom: none;
}

.detailIcon {
  font-size: 1rem;
  color: #4ECDC4;
  width: 20px;
  text-align: center;
}

.detailLabel {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
  min-width: 100px;
}

.detailValue {
  font-size: 0.9rem;
  color: #333;
  flex: 1;
}

/* Service Photos */
.servicePhotos {
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.photoSection {
  margin-bottom: 1rem;
}

.photoSection:last-child {
  margin-bottom: 0;
}

.photoTitle {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.photoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 0.5rem;
}

.servicePhoto {
  width: 100%;
  height: 100px;
  object-fit: cover;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.servicePhoto:hover {
  transform: scale(1.05);
}

/* Service Actions */
.serviceActions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.bookAgainButton,
.viewDetailsButton {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  text-align: center;
  transition: all 0.3s ease;
}

.bookAgainButton {
  background: #4ECDC4;
  color: white;
}

.bookAgainButton:hover {
  background: #44A08D;
  transform: translateY(-1px);
}

.viewDetailsButton {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e9ecef;
}

.viewDetailsButton:hover {
  background: #e9ecef;
  color: #333;
}

/* Rating Modal */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 12px;
  padding: 0;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
}

.modalTitle {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.closeButton:hover {
  background: #f8f9fa;
  color: #333;
}

.modalContent {
  padding: 1.5rem;
}

.serviceInfo h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.serviceInfo p {
  color: #666;
  margin: 0;
}

.ratingSection {
  margin: 1.5rem 0;
  text-align: center;
}

.ratingLabel {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.reviewSection {
  margin: 1.5rem 0;
}

.reviewLabel {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.reviewTextarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-family: inherit;
  font-size: 1rem;
  resize: vertical;
  transition: border-color 0.3s ease;
}

.reviewTextarea:focus {
  outline: none;
  border-color: #4ECDC4;
}

.modalActions {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e9ecef;
  justify-content: flex-end;
}

.cancelButton,
.submitButton {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancelButton {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e9ecef;
}

.cancelButton:hover {
  background: #e9ecef;
  color: #333;
}

.submitButton {
  background: #4ECDC4;
  color: white;
}

.submitButton:hover {
  background: #44A08D;
}

.submitButton:disabled {
  background: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .serviceHistory {
    padding: 0.5rem;
  }

  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .title {
    font-size: 1.5rem;
  }

  .serviceCard {
    padding: 1rem;
  }

  .serviceHeader {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .serviceActions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .bookAgainButton,
  .viewDetailsButton {
    width: 100%;
  }

  .detailLabel {
    min-width: 80px;
  }

  .photoGrid {
    grid-template-columns: repeat(3, 1fr);
  }

  .modal {
    width: 95%;
    margin: 1rem;
  }

  .modalActions {
    flex-direction: column;
  }

  .cancelButton,
  .submitButton {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.3rem;
  }

  .serviceCard {
    padding: 0.75rem;
  }

  .serviceName {
    font-size: 1.1rem;
  }

  .detailRow {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .detailLabel {
    min-width: auto;
    font-weight: 600;
  }

  .photoGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .emptyState {
    padding: 2rem 1rem;
  }

  .emptyIcon {
    font-size: 3rem;
  }

  .emptyTitle {
    font-size: 1.3rem;
  }
}
