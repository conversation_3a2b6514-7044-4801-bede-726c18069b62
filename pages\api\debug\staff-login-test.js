import { createClient } from '@supabase/supabase-js'

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const requestId = Math.random().toString(36).substring(7)
  console.log(`[${requestId}] Staff Login Debug Test - Starting`)

  try {
    const { email, password, action } = req.body

    if (!email || !password) {
      return res.status(400).json({ 
        error: 'Email and password are required',
        requestId 
      })
    }

    // Initialize Supabase admin client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          persistSession: false,
          autoRefreshToken: false
        }
      }
    )

    const debugInfo = {
      requestId,
      email,
      timestamp: new Date().toISOString(),
      steps: []
    }

    // Step 1: Check if user exists in auth.users
    debugInfo.steps.push('Checking user existence in auth.users...')
    const { data: userData, error: userError } = await supabase.auth.admin.listUsers()
    
    if (userError) {
      debugInfo.steps.push(`❌ Error fetching users: ${userError.message}`)
      return res.status(500).json({ error: 'Failed to fetch users', debugInfo })
    }

    const user = userData.users.find(u => u.email === email)
    if (!user) {
      debugInfo.steps.push('❌ User not found in auth.users')
      return res.status(404).json({ error: 'User not found', debugInfo })
    }

    debugInfo.steps.push(`✅ User found: ${user.id}`)
    debugInfo.steps.push(`   - Email confirmed: ${user.email_confirmed_at ? 'Yes' : 'No'}`)
    debugInfo.steps.push(`   - Created: ${user.created_at}`)
    debugInfo.steps.push(`   - Last sign in: ${user.last_sign_in_at || 'Never'}`)
    debugInfo.steps.push(`   - User metadata: ${JSON.stringify(user.user_metadata)}`)

    // Step 2: Check user role
    debugInfo.steps.push('Checking user role in user_roles table...')
    const { data: roleData, error: roleError } = await supabase
      .from('user_roles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (roleError) {
      debugInfo.steps.push(`❌ Error fetching role: ${roleError.message} (Code: ${roleError.code})`)
      if (roleError.code === 'PGRST116') {
        debugInfo.steps.push('   - This means no role record exists for this user')
      }
    } else {
      debugInfo.steps.push(`✅ Role found: ${roleData.role}`)
      debugInfo.steps.push(`   - Created: ${roleData.created_at}`)
      debugInfo.steps.push(`   - Updated: ${roleData.updated_at}`)
    }

    // Step 3: Check application status (for artist/braider)
    debugInfo.steps.push('Checking artist/braider application status...')
    const { data: appData, error: appError } = await supabase
      .from('artist_braider_applications')
      .select('*')
      .eq('user_id', user.id)
      .single()

    if (appError) {
      if (appError.code === 'PGRST116') {
        debugInfo.steps.push('   - No application record found (user might not be artist/braider)')
      } else {
        debugInfo.steps.push(`❌ Error fetching application: ${appError.message}`)
      }
    } else {
      debugInfo.steps.push(`✅ Application found: ${appData.application_type}`)
      debugInfo.steps.push(`   - Status: ${appData.status}`)
      debugInfo.steps.push(`   - Created: ${appData.created_at}`)
      debugInfo.steps.push(`   - Updated: ${appData.updated_at}`)
    }

    // Step 4: Test authentication if requested
    if (action === 'test_auth') {
      debugInfo.steps.push('Testing authentication...')
      
      // Create a regular client for authentication test
      const authClient = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
      )

      const { data: authData, error: authError } = await authClient.auth.signInWithPassword({
        email,
        password
      })

      if (authError) {
        debugInfo.steps.push(`❌ Authentication failed: ${authError.message}`)
      } else {
        debugInfo.steps.push('✅ Authentication successful')
        debugInfo.steps.push(`   - Session user ID: ${authData.user.id}`)
        debugInfo.steps.push(`   - Session expires: ${authData.session.expires_at}`)
        
        // Test role fetch with authenticated session
        debugInfo.steps.push('Testing role fetch with authenticated session...')
        const { data: sessionRoleData, error: sessionRoleError } = await authClient
          .from('user_roles')
          .select('role')
          .eq('id', authData.user.id)
          .single()

        if (sessionRoleError) {
          debugInfo.steps.push(`❌ Role fetch failed: ${sessionRoleError.message}`)
        } else {
          debugInfo.steps.push(`✅ Role fetch successful: ${sessionRoleData.role}`)
        }

        // Sign out to clean up
        await authClient.auth.signOut()
      }
    }

    return res.status(200).json({
      success: true,
      debugInfo,
      user: {
        id: user.id,
        email: user.email,
        emailConfirmed: !!user.email_confirmed_at,
        role: roleData?.role || null,
        applicationStatus: appData?.status || null
      }
    })

  } catch (error) {
    console.error(`[${requestId}] Staff Login Debug Test Error:`, error)
    return res.status(500).json({
      error: 'Internal server error',
      message: error.message,
      requestId
    })
  }
}
