#!/usr/bin/env node

/**
 * Test Improved Account Activation Page Script
 * Verifies password requirements and UI improvements
 */

import fs from 'fs'
import path from 'path'

async function testImprovedActivationPage() {
  console.log('🧪 Testing Improved Account Activation Page\n')

  let allPassed = true
  const results = []

  // Test 1: Verify password requirements section exists
  console.log('1. Testing password requirements section...')
  try {
    const activationPagePath = path.join(process.cwd(), 'pages', 'activate-account.js')
    const pageContent = fs.readFileSync(activationPagePath, 'utf8')
    
    // Check for password requirements components
    const hasPasswordRequirements = pageContent.includes('passwordRequirements')
    const hasRequirementsList = pageContent.includes('requirementsList')
    const hasValidationState = pageContent.includes('passwordValidation')
    const hasRealTimeValidation = pageContent.includes('validatePassword')
    
    if (!hasPasswordRequirements || !hasRequirementsList || !hasValidationState || !hasRealTimeValidation) {
      throw new Error('Password requirements section missing key components')
    }

    console.log('✅ Password requirements section implemented')
    console.log(`   Has requirements display: ${hasPasswordRequirements}`)
    console.log(`   Has requirements list: ${hasRequirementsList}`)
    console.log(`   Has validation state: ${hasValidationState}`)
    console.log(`   Has real-time validation: ${hasRealTimeValidation}`)
    results.push({ test: 'password_requirements_section', passed: true })
  } catch (error) {
    console.error('❌ Password requirements section test failed:', error.message)
    allPassed = false
    results.push({ test: 'password_requirements_section', passed: false, error: error.message })
  }

  // Test 2: Verify comprehensive password validation
  console.log('\n2. Testing comprehensive password validation...')
  try {
    const activationPagePath = path.join(process.cwd(), 'pages', 'activate-account.js')
    const pageContent = fs.readFileSync(activationPagePath, 'utf8')
    
    // Check for all password criteria
    const hasLowercaseCheck = pageContent.includes('hasLowercase')
    const hasUppercaseCheck = pageContent.includes('hasUppercase')
    const hasNumberCheck = pageContent.includes('hasNumber')
    const hasSpecialCharCheck = pageContent.includes('hasSpecialChar')
    const hasMinLengthCheck = pageContent.includes('hasMinLength')
    const hasPasswordMatchCheck = pageContent.includes('passwordsMatch')
    
    // Check for regex patterns
    const hasLowercaseRegex = pageContent.includes('/[a-z]/')
    const hasUppercaseRegex = pageContent.includes('/[A-Z]/')
    const hasNumberRegex = pageContent.includes('/[0-9]/')
    const hasSpecialCharRegex = pageContent.includes('!@#$%^&*')
    
    if (!hasLowercaseCheck || !hasUppercaseCheck || !hasNumberCheck || !hasSpecialCharCheck || !hasMinLengthCheck || !hasPasswordMatchCheck) {
      throw new Error('Missing password validation criteria')
    }
    
    if (!hasLowercaseRegex || !hasUppercaseRegex || !hasNumberRegex || !hasSpecialCharRegex) {
      throw new Error('Missing regex validation patterns')
    }

    console.log('✅ Comprehensive password validation implemented')
    console.log(`   Lowercase check: ${hasLowercaseCheck}`)
    console.log(`   Uppercase check: ${hasUppercaseCheck}`)
    console.log(`   Number check: ${hasNumberCheck}`)
    console.log(`   Special char check: ${hasSpecialCharCheck}`)
    console.log(`   Min length check: ${hasMinLengthCheck}`)
    console.log(`   Password match check: ${hasPasswordMatchCheck}`)
    results.push({ test: 'comprehensive_password_validation', passed: true })
  } catch (error) {
    console.error('❌ Comprehensive password validation test failed:', error.message)
    allPassed = false
    results.push({ test: 'comprehensive_password_validation', passed: false, error: error.message })
  }

  // Test 3: Verify improved success screen
  console.log('\n3. Testing improved success screen...')
  try {
    const activationPagePath = path.join(process.cwd(), 'pages', 'activate-account.js')
    const pageContent = fs.readFileSync(activationPagePath, 'utf8')
    
    // Check for success screen improvements
    const hasSuccessIcon = pageContent.includes('successIcon')
    const hasWelcomeMessage = pageContent.includes('Welcome to the Team!')
    const hasNextStepsBox = pageContent.includes('nextStepsBox')
    const hasImprovedButtons = pageContent.includes('🚀 Go to Staff Login')
    const hasHomeButton = pageContent.includes('🏠 Return to Home')
    const hasPrimaryButton = pageContent.includes('primaryButton')
    const hasSecondaryButton = pageContent.includes('secondaryButton')
    
    if (!hasSuccessIcon || !hasWelcomeMessage || !hasNextStepsBox || !hasImprovedButtons || !hasHomeButton) {
      throw new Error('Success screen missing improvements')
    }

    console.log('✅ Improved success screen implemented')
    console.log(`   Has success icon: ${hasSuccessIcon}`)
    console.log(`   Has welcome message: ${hasWelcomeMessage}`)
    console.log(`   Has next steps box: ${hasNextStepsBox}`)
    console.log(`   Has improved buttons: ${hasImprovedButtons}`)
    console.log(`   Has styled buttons: ${hasPrimaryButton && hasSecondaryButton}`)
    results.push({ test: 'improved_success_screen', passed: true })
  } catch (error) {
    console.error('❌ Improved success screen test failed:', error.message)
    allPassed = false
    results.push({ test: 'improved_success_screen', passed: false, error: error.message })
  }

  // Test 4: Verify CSS styles for new components
  console.log('\n4. Testing CSS styles for new components...')
  try {
    const cssPath = path.join(process.cwd(), 'styles', 'admin', 'Login.module.css')
    const cssContent = fs.readFileSync(cssPath, 'utf8')
    
    // Check for password requirements styles
    const hasPasswordRequirementsStyles = cssContent.includes('.passwordRequirements')
    const hasRequirementStyles = cssContent.includes('.requirement')
    const hasValidInvalidStyles = cssContent.includes('.valid') && cssContent.includes('.invalid')
    
    // Check for enhanced success styles
    const hasEnhancedSuccessStyles = cssContent.includes('Enhanced Success Styles')
    const hasNextStepsBoxStyles = cssContent.includes('.nextStepsBox')
    const hasButtonGroupStyles = cssContent.includes('.buttonGroup')
    const hasPrimarySecondaryStyles = cssContent.includes('.primaryButton') && cssContent.includes('.secondaryButton')
    
    // Check for welcome message styles
    const hasWelcomeMessageStyles = cssContent.includes('.welcomeMessage')
    
    if (!hasPasswordRequirementsStyles || !hasRequirementStyles || !hasValidInvalidStyles) {
      throw new Error('Missing password requirements CSS styles')
    }
    
    if (!hasEnhancedSuccessStyles || !hasNextStepsBoxStyles || !hasButtonGroupStyles || !hasPrimarySecondaryStyles) {
      throw new Error('Missing enhanced success screen CSS styles')
    }

    console.log('✅ CSS styles for new components implemented')
    console.log(`   Password requirements styles: ${hasPasswordRequirementsStyles}`)
    console.log(`   Requirement validation styles: ${hasValidInvalidStyles}`)
    console.log(`   Enhanced success styles: ${hasEnhancedSuccessStyles}`)
    console.log(`   Button group styles: ${hasButtonGroupStyles}`)
    console.log(`   Welcome message styles: ${hasWelcomeMessageStyles}`)
    results.push({ test: 'css_styles_new_components', passed: true })
  } catch (error) {
    console.error('❌ CSS styles test failed:', error.message)
    allPassed = false
    results.push({ test: 'css_styles_new_components', passed: false, error: error.message })
  }

  // Test 5: Verify real-time validation feedback
  console.log('\n5. Testing real-time validation feedback...')
  try {
    const activationPagePath = path.join(process.cwd(), 'pages', 'activate-account.js')
    const pageContent = fs.readFileSync(activationPagePath, 'utf8')
    
    // Check for real-time validation implementation
    const hasInputChangeHandler = pageContent.includes('handleInputChange')
    const hasValidationOnChange = pageContent.includes('validatePassword(')
    const hasVisualFeedback = pageContent.includes('✅') && pageContent.includes('❌')
    const hasConditionalStyling = pageContent.includes('passwordValidation.hasLowercase ? styles.valid : styles.invalid')
    
    if (!hasInputChangeHandler || !hasValidationOnChange || !hasVisualFeedback || !hasConditionalStyling) {
      throw new Error('Real-time validation feedback not properly implemented')
    }

    console.log('✅ Real-time validation feedback implemented')
    console.log(`   Input change handler: ${hasInputChangeHandler}`)
    console.log(`   Validation on change: ${hasValidationOnChange}`)
    console.log(`   Visual feedback icons: ${hasVisualFeedback}`)
    console.log(`   Conditional styling: ${hasConditionalStyling}`)
    results.push({ test: 'realtime_validation_feedback', passed: true })
  } catch (error) {
    console.error('❌ Real-time validation feedback test failed:', error.message)
    allPassed = false
    results.push({ test: 'realtime_validation_feedback', passed: false, error: error.message })
  }

  // Test 6: Verify enhanced error handling
  console.log('\n6. Testing enhanced error handling...')
  try {
    const activationPagePath = path.join(process.cwd(), 'pages', 'activate-account.js')
    const pageContent = fs.readFileSync(activationPagePath, 'utf8')
    
    // Check for specific error messages
    const hasLowercaseError = pageContent.includes('must contain at least one lowercase letter')
    const hasUppercaseError = pageContent.includes('must contain at least one uppercase letter')
    const hasNumberError = pageContent.includes('must contain at least one number')
    const hasSpecialCharError = pageContent.includes('must contain at least one special character')
    const hasLengthError = pageContent.includes('must be at least 8 characters long')
    const hasMatchError = pageContent.includes('Passwords do not match')
    
    if (!hasLowercaseError || !hasUppercaseError || !hasNumberError || !hasSpecialCharError || !hasLengthError || !hasMatchError) {
      throw new Error('Missing specific error messages for password requirements')
    }

    console.log('✅ Enhanced error handling implemented')
    console.log(`   Specific error messages for all requirements: ✅`)
    console.log(`   User-friendly error descriptions: ✅`)
    results.push({ test: 'enhanced_error_handling', passed: true })
  } catch (error) {
    console.error('❌ Enhanced error handling test failed:', error.message)
    allPassed = false
    results.push({ test: 'enhanced_error_handling', passed: false, error: error.message })
  }

  // Summary
  console.log('\n📊 Test Results Summary:')
  const passedCount = results.filter(r => r.passed).length
  const totalCount = results.length
  
  console.log(`   Passed tests: ${passedCount}/${totalCount}`)
  
  results.forEach(result => {
    const status = result.passed ? '✅' : '❌'
    const error = result.error ? ` (${result.error})` : ''
    console.log(`   ${status} ${result.test}${error}`)
  })

  console.log(`\n   Overall status: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`)

  if (allPassed) {
    console.log('\n🎉 Account activation page improvements are complete!')
    console.log('\n📋 Features Implemented:')
    console.log('   ✅ Comprehensive password requirements display')
    console.log('   ✅ Real-time validation feedback with visual indicators')
    console.log('   ✅ Enhanced success screen with next steps')
    console.log('   ✅ Improved button styling with icons')
    console.log('   ✅ Ocean Soul Sparkles branding and colors')
    console.log('   ✅ Mobile-responsive design')
    console.log('   ✅ User-friendly error messages')
    console.log('\n🎯 User Experience:')
    console.log('   - Users see password requirements before typing')
    console.log('   - Real-time feedback shows which requirements are met')
    console.log('   - Clear success screen guides users to next steps')
    console.log('   - Professional styling matches brand identity')
  } else {
    console.log('\n🔧 Issues to Address:')
    results.filter(r => !r.passed).forEach(result => {
      console.log(`   - ${result.test}: ${result.error}`)
    })
  }

  return allPassed
}

// Run the test script
testImprovedActivationPage()
  .then(success => {
    if (success) {
      console.log('\n✅ Account activation page improvements test completed successfully!')
      process.exit(0)
    } else {
      console.log('\n❌ Account activation page improvements test completed with failures!')
      process.exit(1)
    }
  })
  .catch(error => {
    console.error('\n💥 Test script error:', error)
    process.exit(1)
  })
