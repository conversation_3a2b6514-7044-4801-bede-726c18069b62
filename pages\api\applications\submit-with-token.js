import { getAdminClient } from '@/lib/supabase'

/**
 * API endpoint for submitting applications with token-based authentication
 * POST /api/applications/submit-with-token - Submit application using secure token
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7)
  console.log(`[${requestId}] Token-based application submission API called`)

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const {
      token,
      application_type,
      experience_years,
      portfolio_url,
      availability_preferences,
      service_specializations,
      previous_experience,
      references
    } = req.body

    // Validate required fields
    if (!token) {
      return res.status(400).json({ error: 'Token is required' })
    }

    if (!application_type || !['artist', 'braider'].includes(application_type)) {
      return res.status(400).json({ error: 'Valid application type is required' })
    }

    if (!experience_years || experience_years < 0) {
      return res.status(400).json({ error: 'Valid years of experience is required' })
    }

    if (!service_specializations || !Array.isArray(service_specializations) || service_specializations.length === 0) {
      return res.status(400).json({ error: 'At least one service specialization is required' })
    }

    if (!previous_experience || previous_experience.trim().length < 50) {
      return res.status(400).json({ error: 'Previous experience description must be at least 50 characters' })
    }

    // Get admin client
    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Failed to initialize admin client' })
    }

    console.log(`[${requestId}] Validating token for application submission`)

    // Validate token
    const { data: validationResult, error: validationError } = await adminClient
      .rpc('validate_application_token', { token_value: token })

    if (validationError) {
      console.error(`[${requestId}] Token validation error:`, validationError)
      return res.status(500).json({ error: 'Failed to validate token' })
    }

    const validation = validationResult[0]
    
    if (!validation.is_valid) {
      console.log(`[${requestId}] Token validation failed: ${validation.error_message}`)
      return res.status(400).json({ error: validation.error_message })
    }

    // Verify application type matches token
    const { data: existingApplication, error: appError } = await adminClient
      .from('artist_braider_applications')
      .select('application_type, status')
      .eq('id', validation.application_id)
      .single()

    if (appError || !existingApplication) {
      console.error(`[${requestId}] Error fetching application:`, appError)
      return res.status(500).json({ error: 'Failed to fetch application information' })
    }

    if (existingApplication.application_type !== application_type) {
      return res.status(400).json({ error: 'Application type does not match token' })
    }

    console.log(`[${requestId}] Updating application for user: ${validation.user_id}`)

    // Update the application with submitted data
    const { data: updatedApplication, error: updateError } = await adminClient
      .from('artist_braider_applications')
      .update({
        experience_years: parseInt(experience_years),
        portfolio_url: portfolio_url || null,
        availability_preferences: availability_preferences || {},
        service_specializations,
        previous_experience: previous_experience.trim(),
        professional_references: references || null,
        status: 'pending', // Reset to pending when resubmitted
        updated_at: new Date().toISOString()
      })
      .eq('id', validation.application_id)
      .select()
      .single()

    if (updateError) {
      console.error(`[${requestId}] Error updating application:`, updateError)
      return res.status(500).json({ error: 'Failed to update application' })
    }

    // Mark token as used to prevent reuse
    const { error: tokenUpdateError } = await adminClient
      .rpc('mark_token_as_used', { 
        token_value: token,
        client_ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
        client_user_agent: req.headers['user-agent']
      })

    if (tokenUpdateError) {
      console.error(`[${requestId}] Error marking token as used:`, tokenUpdateError)
      // Continue anyway - application was submitted successfully
    }

    console.log(`[${requestId}] Application submitted successfully`)

    return res.status(200).json({
      success: true,
      message: 'Application submitted successfully',
      application: updatedApplication,
      tokenUsed: true
    })

  } catch (error) {
    console.error(`[${requestId}] Unexpected error in token-based application submission:`, error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
