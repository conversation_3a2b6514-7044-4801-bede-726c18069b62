/* PWA Test Page Styles */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.header h1 {
  font-size: 32px;
  color: #1f2937;
  margin-bottom: 12px;
  font-weight: 700;
}

.header p {
  font-size: 16px;
  color: #6b7280;
  margin-bottom: 20px;
}

.main {
  max-width: 1200px;
  margin: 0 auto;
}

.section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section h2 {
  font-size: 24px;
  color: #1f2937;
  margin-bottom: 20px;
  font-weight: 600;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

.section h3 {
  font-size: 18px;
  color: #374151;
  margin: 20px 0 12px 0;
  font-weight: 600;
}

/* Status Grid */
.statusGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.statusItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.label {
  font-weight: 500;
  color: #374151;
}

.status {
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
}

.status.success {
  background: #d1fae5;
  color: #065f46;
}

.status.error {
  background: #fee2e2;
  color: #991b1b;
}

.status.warning {
  background: #fef3c7;
  color: #92400e;
}

.status.info {
  background: #dbeafe;
  color: #1e40af;
}

/* Device Info */
.deviceInfo {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e5e7eb;
}

.deviceInfo p {
  margin: 8px 0;
  font-size: 14px;
  color: #374151;
}

.deviceInfo strong {
  color: #1f2937;
}

/* Buttons */
.button {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 4px;
}

.button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Action Grid */
.actionGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 16px;
}

.actionGrid .button {
  margin: 0;
  width: 100%;
}

/* Test Results */
.testResults {
  margin-top: 20px;
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e5e7eb;
}

.testResult {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px 0;
  border-bottom: 1px solid #e5e7eb;
}

.testResult:last-child {
  border-bottom: none;
}

.testName {
  font-weight: 500;
  color: #374151;
  min-width: 150px;
}

.testValue {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  max-width: 60%;
  word-break: break-all;
  white-space: pre-wrap;
}

/* Photo Grid */
.photoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.photoItem {
  background: #f9fafb;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  text-align: center;
}

.photoItem img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: 6px;
  margin-bottom: 8px;
}

.photoItem p {
  margin: 4px 0;
  font-size: 12px;
  color: #6b7280;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .header {
    padding: 20px;
    margin-bottom: 24px;
  }
  
  .header h1 {
    font-size: 24px;
  }
  
  .section {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .section h2 {
    font-size: 20px;
  }
  
  .statusGrid {
    grid-template-columns: 1fr;
  }
  
  .statusItem {
    padding: 10px 12px;
  }
  
  .actionGrid {
    grid-template-columns: 1fr;
  }
  
  .testResult {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .testName {
    min-width: auto;
  }
  
  .testValue {
    max-width: 100%;
  }
  
  .photoGrid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
  
  .photoItem img {
    height: 120px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 12px;
  }
  
  .header {
    padding: 16px;
  }
  
  .header h1 {
    font-size: 20px;
  }
  
  .header p {
    font-size: 14px;
  }
  
  .section {
    padding: 12px;
  }
  
  .section h2 {
    font-size: 18px;
  }
  
  .button {
    padding: 10px 16px;
    font-size: 13px;
  }
  
  .photoGrid {
    grid-template-columns: 1fr 1fr;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .container {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  }
  
  .header,
  .section {
    background: #374151;
    color: #f9fafb;
  }
  
  .header h1,
  .section h2 {
    color: #f9fafb;
  }
  
  .header p {
    color: #d1d5db;
  }
  
  .section h2 {
    border-bottom-color: #4b5563;
  }
  
  .statusItem,
  .deviceInfo,
  .testResults,
  .photoItem {
    background: #4b5563;
    border-color: #6b7280;
    color: #f9fafb;
  }
  
  .label,
  .testName {
    color: #e5e7eb;
  }
  
  .deviceInfo strong {
    color: #f9fafb;
  }
  
  .deviceInfo p {
    color: #d1d5db;
  }
  
  .photoItem p {
    color: #9ca3af;
  }
}
