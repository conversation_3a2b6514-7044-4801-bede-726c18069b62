#!/usr/bin/env node

/**
 * Test script to verify admin navigation and authentication fixes
 */

import fs from 'fs';
import path from 'path';

console.log('🔧 TESTING ADMIN NAVIGATION FIXES');
console.log('=================================');

const testResults = {
  navigationFixes: [],
  authenticationFixes: [],
  componentIntegration: [],
  issues: []
};

/**
 * Test 1: Verify router.replace usage instead of router.push for auth redirects
 */
console.log('\n1. Testing Navigation Fixes...');

const checkNavigationFixes = (filePath, content) => {
  const lines = content.split('\n');
  let routerPushCount = 0;
  let routerReplaceCount = 0;
  
  lines.forEach((line, index) => {
    if (line.includes('router.push') && (line.includes('/admin') || line.includes('/staff-login'))) {
      routerPushCount++;
      testResults.issues.push({
        file: filePath,
        line: index + 1,
        type: 'navigation',
        severity: 'medium',
        issue: 'Still using router.push for auth redirects',
        code: line.trim()
      });
    }
    
    if (line.includes('router.replace') && (line.includes('/admin') || line.includes('/staff-login'))) {
      routerReplaceCount++;
      testResults.navigationFixes.push({
        file: filePath,
        line: index + 1,
        fix: 'Using router.replace for auth redirects',
        code: line.trim()
      });
    }
  });
  
  return { routerPushCount, routerReplaceCount };
};

/**
 * Test 2: Verify authentication flow improvements
 */
console.log('\n2. Testing Authentication Fixes...');

const checkAuthenticationFixes = (filePath, content) => {
  const lines = content.split('\n');
  
  lines.forEach((line, index) => {
    // Check for improved error handling
    if (line.includes('router.replace') && line.includes('/admin/login')) {
      testResults.authenticationFixes.push({
        file: filePath,
        line: index + 1,
        fix: 'Improved login redirect handling',
        code: line.trim()
      });
    }
    
    // Check for race condition fixes
    if (line.includes('await signOut()') && line.includes('router.replace')) {
      testResults.authenticationFixes.push({
        file: filePath,
        line: index + 1,
        fix: 'Fixed signOut race condition',
        code: line.trim()
      });
    }
  });
};

/**
 * Test 3: Verify component integration improvements
 */
console.log('\n3. Testing Component Integration...');

const checkComponentIntegration = (filePath, content) => {
  const lines = content.split('\n');
  
  lines.forEach((line, index) => {
    // Check for improved artist/braider path handling
    if (line.includes('/admin/complete-profile') || line.includes('/admin/my-profile')) {
      testResults.componentIntegration.push({
        file: filePath,
        line: index + 1,
        improvement: 'Enhanced artist/braider path access',
        code: line.trim()
      });
    }
    
    // Check for error boundary usage
    if (line.includes('DashboardErrorBoundary') || line.includes('ProtectedRoute')) {
      testResults.componentIntegration.push({
        file: filePath,
        line: index + 1,
        improvement: 'Proper error handling component',
        code: line.trim()
      });
    }
  });
};

// Files to test
const filesToTest = [
  'components/admin/ProtectedRoute.js',
  'components/admin/AdminLayout.js',
  'pages/admin/artist-braider-dashboard.js',
  'components/admin/ArtistBraiderDashboard.js',
  'contexts/AuthContext.js'
];

let totalRouterPush = 0;
let totalRouterReplace = 0;

filesToTest.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`   Checking ${file}...`);
    const content = fs.readFileSync(file, 'utf8');
    
    const navResults = checkNavigationFixes(file, content);
    totalRouterPush += navResults.routerPushCount;
    totalRouterReplace += navResults.routerReplaceCount;
    
    checkAuthenticationFixes(file, content);
    checkComponentIntegration(file, content);
  } else {
    console.log(`   ⚠️  File not found: ${file}`);
  }
});

/**
 * Test 4: Verify role-based access improvements
 */
console.log('\n4. Testing Role-Based Access...');

if (fs.existsSync('components/admin/ProtectedRoute.js')) {
  const protectedRouteContent = fs.readFileSync('components/admin/ProtectedRoute.js', 'utf8');
  
  if (protectedRouteContent.includes('/admin/complete-profile') && 
      protectedRouteContent.includes('/admin/my-profile')) {
    testResults.componentIntegration.push({
      file: 'components/admin/ProtectedRoute.js',
      improvement: 'Enhanced artist/braider allowed paths',
      details: 'Added profile completion and management paths'
    });
    console.log('   ✅ Artist/braider path access improved');
  } else {
    testResults.issues.push({
      file: 'components/admin/ProtectedRoute.js',
      type: 'access-control',
      severity: 'high',
      issue: 'Artist/braider paths not properly configured'
    });
    console.log('   ❌ Artist/braider path access needs improvement');
  }
}

/**
 * Generate Test Report
 */
console.log('\n📊 TEST RESULTS SUMMARY');
console.log('=======================');

console.log(`\n🔧 Navigation Fixes Applied: ${testResults.navigationFixes.length}`);
console.log(`🔐 Authentication Fixes Applied: ${testResults.authenticationFixes.length}`);
console.log(`🧩 Component Integration Improvements: ${testResults.componentIntegration.length}`);
console.log(`⚠️  Remaining Issues: ${testResults.issues.length}`);

console.log(`\n📈 Navigation Metrics:`);
console.log(`   router.push (auth redirects): ${totalRouterPush} (should be 0)`);
console.log(`   router.replace (auth redirects): ${totalRouterReplace} (good)`);

if (testResults.issues.length > 0) {
  console.log('\n❌ REMAINING ISSUES:');
  testResults.issues.forEach((issue, index) => {
    console.log(`\n${index + 1}. ${issue.type.toUpperCase()} - ${issue.severity.toUpperCase()}`);
    console.log(`   File: ${issue.file}:${issue.line}`);
    console.log(`   Issue: ${issue.issue}`);
    console.log(`   Code: ${issue.code}`);
  });
} else {
  console.log('\n✅ ALL NAVIGATION AND AUTH ISSUES RESOLVED!');
}

console.log('\n🎯 RECOMMENDATIONS:');
console.log('===================');

if (totalRouterPush > 0) {
  console.log('❌ Replace remaining router.push() with router.replace() for auth redirects');
}

if (testResults.authenticationFixes.length > 0) {
  console.log('✅ Authentication flow improvements implemented');
}

if (testResults.componentIntegration.length > 0) {
  console.log('✅ Component integration improvements implemented');
}

console.log('\n🚀 NEXT STEPS:');
console.log('==============');
console.log('1. Test the admin interface with different user roles');
console.log('2. Verify navigation flows work without unexpected redirects');
console.log('3. Check that authentication persists during normal operations');
console.log('4. Validate component loading and data fetching');
console.log('5. Test role-specific features are accessible to appropriate users');

// Save detailed results
const reportPath = 'admin-navigation-test-results.json';
fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
console.log(`\n📄 Detailed results saved to: ${reportPath}`);

// Exit with appropriate code
process.exit(testResults.issues.length > 0 ? 1 : 0);
