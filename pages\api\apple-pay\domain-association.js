/**
 * Apple Pay Domain Association API
 * Serves the Apple Pay domain verification file for Square Web Payments SDK
 */

import fs from 'fs'
import path from 'path'

export default function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Path to the domain association file
    const filePath = path.join(process.cwd(), 'public', '.well-known', 'apple-developer-merchantid-domain-association')
    
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      console.error('Apple Pay domain association file not found at:', filePath)
      return res.status(404).json({ 
        error: 'Domain association file not found',
        message: 'Please ensure the Apple Pay domain association file is properly uploaded'
      })
    }

    // Read the file content
    const fileContent = fs.readFileSync(filePath, 'utf8')
    
    // Set appropriate headers for Apple Pay domain verification
    res.setHeader('Content-Type', 'text/plain')
    res.setHeader('Cache-Control', 'public, max-age=86400') // Cache for 24 hours
    res.setHeader('Access-Control-Allow-Origin', '*')
    res.setHeader('Access-Control-Allow-Methods', 'GET')
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type')
    
    // Log successful access for monitoring
    console.log('Apple Pay domain association file served successfully')
    
    // Return the file content
    return res.status(200).send(fileContent)
    
  } catch (error) {
    console.error('Error serving Apple Pay domain association file:', error)
    return res.status(500).json({ 
      error: 'Internal server error',
      message: 'Failed to serve domain association file'
    })
  }
}

// Disable body parsing for this endpoint
export const config = {
  api: {
    bodyParser: false,
  },
}
