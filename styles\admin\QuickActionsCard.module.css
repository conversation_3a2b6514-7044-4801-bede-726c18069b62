/* Quick Actions Card Styles */
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid #e5e7eb;
}

.header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.header h3 {
  margin: 0 0 4px 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.subtitle {
  color: #6b7280;
  font-size: 0.875rem;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Actions Grid */
.actionsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 12px;
}

.actionButton {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: white;
  border: 1px solid #e5e7eb;
  border-left: 4px solid #3b82f6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.actionButton:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.actionButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.actionIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #f3f4f6;
  border-radius: 8px;
  flex-shrink: 0;
}

.actionContent {
  flex: 1;
}

.actionTitle {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.actionDescription {
  font-size: 0.875rem;
  color: #6b7280;
}

.actionArrow {
  color: #9ca3af;
  font-size: 1.25rem;
  font-weight: bold;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Status Summary */
.statusSummary h4 {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 1rem;
  font-weight: 600;
}

.statusGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.statusItem {
  text-align: center;
  padding: 12px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.statusLabel {
  display: block;
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  font-weight: 500;
  margin-bottom: 4px;
}

.statusValue {
  display: block;
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
}

/* Recent Activity */
.recentActivity h4 {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 1rem;
  font-weight: 600;
}

.activityList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activityItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.activityIcon {
  font-size: 1.25rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 6px;
  flex-shrink: 0;
}

.activityContent {
  flex: 1;
}

.activityText {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 2px;
}

.activityTime {
  display: block;
  font-size: 0.75rem;
  color: #9ca3af;
}

/* Emergency Section */
.emergencySection h4 {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 1rem;
  font-weight: 600;
}

.emergencyActions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.emergencyButton {
  padding: 12px 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.emergencyButton:hover {
  background: #fee2e2;
  border-color: #fca5a5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card {
    padding: 16px;
    margin-bottom: 16px;
  }

  .actionsGrid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .actionButton {
    padding: 12px;
    gap: 12px;
  }

  .actionIcon {
    width: 32px;
    height: 32px;
  }

  .statusGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .activityItem {
    padding: 8px;
    gap: 8px;
  }

  .emergencyActions {
    gap: 6px;
  }

  .emergencyButton {
    padding: 10px 12px;
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .statusGrid {
    grid-template-columns: 1fr;
  }

  .actionButton {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .actionArrow {
    display: none;
  }
}
