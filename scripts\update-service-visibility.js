#!/usr/bin/env node

/**
 * Bulk update script for service visibility settings
 * Updates services with durations 5-45 minutes to:
 * - visible_on_pos: true (show on POS Terminal)
 * - visible_on_public: false (hide from public booking page)
 */

const { supabaseAdmin } = require('../lib/supabase');

async function updateServiceVisibility() {
  console.log('🔧 Starting service visibility bulk update...');
  
  try {
    // First, fetch all services to see current state
    console.log('📊 Fetching all services...');
    const { data: allServices, error: fetchError } = await supabaseAdmin
      .from('services')
      .select('id, name, duration, visible_on_pos, visible_on_public, visible_on_events')
      .order('name');

    if (fetchError) {
      throw fetchError;
    }

    console.log(`✅ Found ${allServices.length} total services`);

    // Filter services with duration between 5-45 minutes
    const targetServices = allServices.filter(service => {
      const duration = parseInt(service.duration, 10);
      return duration >= 5 && duration <= 45;
    });

    console.log(`🎯 Found ${targetServices.length} services with duration 5-45 minutes:`);
    targetServices.forEach(service => {
      console.log(`  - ${service.name} (${service.duration} min) - POS: ${service.visible_on_pos}, Public: ${service.visible_on_public}`);
    });

    if (targetServices.length === 0) {
      console.log('ℹ️  No services found in the 5-45 minute range. Exiting.');
      return;
    }

    // Ask for confirmation
    console.log('\n🔄 Will update these services to:');
    console.log('   - visible_on_pos: true (show on POS Terminal)');
    console.log('   - visible_on_public: false (hide from public booking page)');
    console.log('   - visible_on_events: unchanged');

    // Get service IDs to update
    const serviceIds = targetServices.map(service => service.id);

    // Perform bulk update
    console.log('\n⚡ Performing bulk update...');
    const { data: updatedServices, error: updateError } = await supabaseAdmin
      .from('services')
      .update({
        visible_on_pos: true,
        visible_on_public: false,
        updated_at: new Date().toISOString()
      })
      .in('id', serviceIds)
      .select('id, name, duration, visible_on_pos, visible_on_public');

    if (updateError) {
      throw updateError;
    }

    console.log(`✅ Successfully updated ${updatedServices.length} services:`);
    updatedServices.forEach(service => {
      console.log(`  ✓ ${service.name} (${service.duration} min) - POS: ${service.visible_on_pos}, Public: ${service.visible_on_public}`);
    });

    console.log('\n🎉 Bulk update completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   - Services updated: ${updatedServices.length}`);
    console.log(`   - POS Terminal visibility: enabled for all updated services`);
    console.log(`   - Public booking visibility: disabled for all updated services`);
    console.log(`   - Events visibility: unchanged`);

  } catch (error) {
    console.error('❌ Error during bulk update:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  updateServiceVisibility()
    .then(() => {
      console.log('\n✨ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Script failed:', error);
      process.exit(1);
    });
}

module.exports = { updateServiceVisibility };
