/**
 * Secure Settings Manager for Ocean Soul Sparkles
 * Handles encrypted storage and retrieval of sensitive API credentials
 */

import { getAdminClient } from './supabase'
import { prepareForStorage, prepareFromStorage, maskValue } from './encryption'

/**
 * Setting categories and their configurations
 */
export const SETTING_CATEGORIES = {
  gmail: {
    title: 'Gmail SMTP',
    description: 'Gmail SMTP configuration for email sending',
    icon: '📧',
    settings: {
      gmail_smtp_user: {
        label: 'Gmail Email Address',
        type: 'email',
        required: true,
        sensitive: false,
        placeholder: '<EMAIL>'
      },
      gmail_smtp_password: {
        label: 'Gmail App Password',
        type: 'password',
        required: true,
        sensitive: true,
        placeholder: 'xxxx xxxx xxxx xxxx',
        help: 'Generate an App Password in your Google Account settings'
      },
      gmail_from_email: {
        label: 'From Email Address',
        type: 'email',
        required: false,
        sensitive: false,
        placeholder: '<EMAIL>'
      },
      gmail_from_name: {
        label: 'From Name',
        type: 'text',
        required: false,
        sensitive: false,
        placeholder: 'Ocean Soul Sparkles'
      }
    }
  },
  workspace: {
    title: 'Google Workspace SMTP',
    description: 'Google Workspace SMTP configuration',
    icon: '🏢',
    settings: {
      workspace_smtp_user: {
        label: 'Workspace Email',
        type: 'email',
        required: false,
        sensitive: false,
        placeholder: '<EMAIL>'
      },
      workspace_smtp_password: {
        label: 'Workspace App Password',
        type: 'password',
        required: false,
        sensitive: true,
        placeholder: 'xxxx xxxx xxxx xxxx'
      },
      workspace_from_email: {
        label: 'From Email Address',
        type: 'email',
        required: false,
        sensitive: false,
        placeholder: '<EMAIL>'
      },
      workspace_from_name: {
        label: 'From Name',
        type: 'text',
        required: false,
        sensitive: false,
        placeholder: 'Ocean Soul Sparkles'
      }
    }
  },
  onesignal: {
    title: 'OneSignal',
    description: 'OneSignal push notification and email service',
    icon: '🔔',
    settings: {
      onesignal_app_id: {
        label: 'OneSignal App ID',
        type: 'text',
        required: false,
        sensitive: false,
        placeholder: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx'
      },
      onesignal_rest_api_key: {
        label: 'OneSignal REST API Key',
        type: 'password',
        required: false,
        sensitive: true,
        placeholder: 'Basic xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
      }
    }
  },
  google_oauth: {
    title: 'Google OAuth',
    description: 'Google OAuth credentials for API access',
    icon: '🔐',
    settings: {
      google_client_id: {
        label: 'Google Client ID',
        type: 'text',
        required: false,
        sensitive: false,
        placeholder: 'xxxxxxxxx-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx.apps.googleusercontent.com'
      },
      google_client_secret: {
        label: 'Google Client Secret',
        type: 'password',
        required: false,
        sensitive: true,
        placeholder: 'GOCSPX-xxxxxxxxxxxxxxxxxxxxxxxx'
      },
      google_project_id: {
        label: 'Google Project ID',
        type: 'text',
        required: false,
        sensitive: false,
        placeholder: 'ocean-soul-sparkles'
      }
    }
  },
  email_config: {
    title: 'Email Configuration',
    description: 'General email service configuration',
    icon: '⚙️',
    settings: {
      email_service_priority: {
        label: 'Service Priority',
        type: 'select',
        required: true,
        sensitive: false,
        options: [
          { value: 'gmail,workspace,onesignal', label: 'Gmail → Workspace → OneSignal' },
          { value: 'workspace,gmail,onesignal', label: 'Workspace → Gmail → OneSignal' },
          { value: 'onesignal,gmail,workspace', label: 'OneSignal → Gmail → Workspace' },
          { value: 'gmail,onesignal', label: 'Gmail → OneSignal' },
          { value: 'workspace,onesignal', label: 'Workspace → OneSignal' },
          { value: 'onesignal', label: 'OneSignal Only' }
        ],
        default: 'gmail,workspace,onesignal'
      },
      system_email_enabled: {
        label: 'Email System Enabled',
        type: 'boolean',
        required: true,
        sensitive: false,
        default: 'true'
      }
    }
  }
}

/**
 * Get all settings with decryption
 */
export async function getAllSettings() {
  try {
    const adminClient = getAdminClient()
    if (!adminClient) {
      throw new Error('Failed to initialize admin client')
    }

    const { data: settings, error } = await adminClient
      .from('admin_settings')
      .select('setting_key, setting_value')

    if (error) {
      throw new Error(`Database error: ${error.message}`)
    }

    // Convert to object and decrypt sensitive values
    const settingsObj = {}
    if (settings) {
      settings.forEach(setting => {
        settingsObj[setting.setting_key] = prepareFromStorage(setting.setting_value)
      })
    }

    return settingsObj
  } catch (error) {
    console.error('Error getting settings:', error)
    throw error
  }
}

/**
 * Get settings for display (with masking)
 */
export async function getSettingsForDisplay() {
  try {
    const settings = await getAllSettings()
    const displaySettings = {}

    // Apply masking to sensitive settings
    Object.entries(SETTING_CATEGORIES).forEach(([categoryKey, category]) => {
      displaySettings[categoryKey] = {}
      
      Object.entries(category.settings).forEach(([settingKey, config]) => {
        const value = settings[settingKey] || ''
        
        if (config.sensitive && value) {
          displaySettings[categoryKey][settingKey] = {
            value: maskValue(value),
            masked: true,
            hasValue: !!value
          }
        } else {
          displaySettings[categoryKey][settingKey] = {
            value: value,
            masked: false,
            hasValue: !!value
          }
        }
      })
    })

    return displaySettings
  } catch (error) {
    console.error('Error getting settings for display:', error)
    throw error
  }
}

/**
 * Update a single setting
 */
export async function updateSetting(key, value, shouldEncrypt = null) {
  try {
    const adminClient = getAdminClient()
    if (!adminClient) {
      throw new Error('Failed to initialize admin client')
    }

    // Determine if setting should be encrypted
    const settingConfig = findSettingConfig(key)
    const encrypt = shouldEncrypt !== null ? shouldEncrypt : settingConfig?.sensitive || false

    // Prepare value for storage (encrypt if needed)
    const storedValue = prepareForStorage(key, value, encrypt)

    const { error } = await adminClient
      .from('admin_settings')
      .upsert([{
        setting_key: key,
        setting_value: storedValue,
        updated_at: new Date().toISOString()
      }], {
        onConflict: 'setting_key'
      })

    if (error) {
      throw new Error(`Database error: ${error.message}`)
    }

    return true
  } catch (error) {
    console.error('Error updating setting:', error)
    throw error
  }
}

/**
 * Update multiple settings
 */
export async function updateSettings(settingsObj) {
  try {
    const adminClient = getAdminClient()
    if (!adminClient) {
      throw new Error('Failed to initialize admin client')
    }

    const updates = Object.entries(settingsObj).map(([key, value]) => {
      const settingConfig = findSettingConfig(key)
      const encrypt = settingConfig?.sensitive || false
      const storedValue = prepareForStorage(key, value, encrypt)

      return {
        setting_key: key,
        setting_value: storedValue,
        updated_at: new Date().toISOString()
      }
    })

    const { error } = await adminClient
      .from('admin_settings')
      .upsert(updates, {
        onConflict: 'setting_key'
      })

    if (error) {
      throw new Error(`Database error: ${error.message}`)
    }

    return true
  } catch (error) {
    console.error('Error updating settings:', error)
    throw error
  }
}

/**
 * Find setting configuration by key
 */
function findSettingConfig(key) {
  for (const category of Object.values(SETTING_CATEGORIES)) {
    if (category.settings[key]) {
      return category.settings[key]
    }
  }
  return null
}

/**
 * Test email service configuration
 */
export async function testEmailService(serviceType, credentials) {
  try {
    switch (serviceType) {
      case 'gmail':
        return await testGmailSMTP(credentials)
      case 'workspace':
        return await testWorkspaceSMTP(credentials)
      case 'onesignal':
        return await testOneSignal(credentials)
      default:
        throw new Error(`Unknown service type: ${serviceType}`)
    }
  } catch (error) {
    console.error(`Error testing ${serviceType}:`, error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * Test Gmail SMTP configuration
 */
async function testGmailSMTP(credentials) {
  const nodemailer = await import('nodemailer')
  
  const transporter = nodemailer.default.createTransporter({
    service: 'gmail',
    auth: {
      user: credentials.gmail_smtp_user,
      pass: credentials.gmail_smtp_password
    }
  })

  await transporter.verify()
  
  return {
    success: true,
    message: 'Gmail SMTP connection verified successfully'
  }
}

/**
 * Test Workspace SMTP configuration
 */
async function testWorkspaceSMTP(credentials) {
  const nodemailer = await import('nodemailer')
  
  const transporter = nodemailer.default.createTransporter({
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    auth: {
      user: credentials.workspace_smtp_user,
      pass: credentials.workspace_smtp_password
    }
  })

  await transporter.verify()
  
  return {
    success: true,
    message: 'Workspace SMTP connection verified successfully'
  }
}

/**
 * Test OneSignal configuration
 */
async function testOneSignal(credentials) {
  const response = await fetch('https://onesignal.com/api/v1/apps', {
    headers: {
      'Authorization': `Basic ${credentials.onesignal_rest_api_key}`
    }
  })

  if (!response.ok) {
    throw new Error(`OneSignal API error: ${response.status}`)
  }

  return {
    success: true,
    message: 'OneSignal API connection verified successfully'
  }
}

export default {
  getAllSettings,
  getSettingsForDisplay,
  updateSetting,
  updateSettings,
  testEmailService,
  SETTING_CATEGORIES
}
