/* Emergency Notification Panel Styles */

.emergencyPanel {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 2px solid #fbbf24;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-radius: 8px;
  border: 1px solid #f59e0b;
}

.header h2 {
  color: #92400e;
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: 700;
}

.warning {
  color: #b45309;
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  background: rgba(251, 191, 36, 0.2);
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #f59e0b;
}

/* Access Denied */
.accessDenied {
  text-align: center;
  padding: 60px 20px;
  background: #fef2f2;
  border: 2px solid #fca5a5;
  border-radius: 8px;
}

.accessDenied h3 {
  color: #dc2626;
  font-size: 24px;
  margin-bottom: 12px;
}

.accessDenied p {
  color: #991b1b;
  font-size: 16px;
}

/* Content Layout */
.content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
}

.mainPanel {
  background: #f9fafb;
  padding: 24px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.sidebar {
  background: #f3f4f6;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #d1d5db;
}

.section {
  margin-bottom: 30px;
}

.section:last-child {
  margin-bottom: 0;
}

.section h3 {
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

/* Templates */
.templates {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.templateButton {
  padding: 12px 16px;
  border: 2px solid transparent;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  background: white;
}

.templateButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.templateButton.high {
  border-color: #f59e0b;
  color: #92400e;
}

.templateButton.high:hover {
  background: #fef3c7;
}

.templateButton.critical {
  border-color: #ef4444;
  color: #dc2626;
}

.templateButton.critical:hover {
  background: #fef2f2;
}

/* Form Elements */
.formGroup {
  margin-bottom: 20px;
}

.formGroup label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 6px;
}

.formGroup input,
.formGroup textarea,
.formGroup select {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s ease;
}

.formGroup input:focus,
.formGroup textarea:focus,
.formGroup select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.formGroup textarea {
  resize: vertical;
  min-height: 100px;
}

.charCount {
  text-align: right;
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

/* Channel Options */
.channelOptions {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  padding: 8px 12px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.checkboxLabel:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.checkboxLabel input[type="checkbox"] {
  margin-right: 8px;
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
}

/* Actions */
.actions {
  margin-top: 30px;
  text-align: center;
}

.sendButton {
  padding: 16px 32px;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}

.sendButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(239, 68, 68, 0.4);
}

.sendButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* History */
.loading,
.noHistory {
  text-align: center;
  padding: 20px;
  color: #6b7280;
  font-style: italic;
}

.historyList {
  max-height: 400px;
  overflow-y: auto;
}

.historyItem {
  padding: 12px;
  margin-bottom: 12px;
  background: white;
  border-radius: 6px;
  border-left: 4px solid #d1d5db;
}

.historyItem.high {
  border-left-color: #f59e0b;
}

.historyItem.critical {
  border-left-color: #ef4444;
}

.historyTitle {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.historyMeta {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 8px;
}

.historyMessage {
  font-size: 14px;
  color: #374151;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Guidelines */
.guidelines ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.guidelines li {
  padding: 8px 0;
  border-bottom: 1px solid #e5e7eb;
  color: #374151;
  font-size: 14px;
  position: relative;
  padding-left: 20px;
}

.guidelines li:before {
  content: '⚠️';
  position: absolute;
  left: 0;
  top: 8px;
}

.guidelines li:last-child {
  border-bottom: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .emergencyPanel {
    margin: 0;
    padding: 16px;
    border-radius: 0;
  }

  .content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .mainPanel,
  .sidebar {
    padding: 16px;
  }

  .templates {
    grid-template-columns: 1fr;
  }

  .formRow {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .channelOptions {
    flex-direction: column;
    gap: 8px;
  }

  .sendButton {
    width: 100%;
    padding: 14px 24px;
  }
}
