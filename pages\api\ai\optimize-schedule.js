/**
 * AI Schedule Optimization API Endpoint
 * Provides intelligent schedule optimization for Ocean Soul Sparkles artists
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { AISchedulingOptimizer } from '@/lib/ai/scheduling-optimizer'

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false,
      error: 'Method not allowed. Use POST.' 
    })
  }

  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] AI Schedule Optimization API called`)

  try {
    // Authenticate admin user
    const user = await authenticateAdminRequest(req)
    if (!user) {
      console.log(`[${requestId}] Authentication failed`)
      return res.status(401).json({ 
        success: false,
        error: 'Unauthorized. Admin access required.' 
      })
    }

    console.log(`[${requestId}] Authenticated user: ${user.email} (${user.role})`)

    // Validate request body
    const { artistId, date, newBooking, options = {} } = req.body

    if (!artistId || !date) {
      console.log(`[${requestId}] Missing required fields`)
      return res.status(400).json({ 
        success: false,
        error: 'Missing required fields: artistId and date are required' 
      })
    }

    // Validate date format
    const targetDate = new Date(date)
    if (isNaN(targetDate.getTime())) {
      console.log(`[${requestId}] Invalid date format: ${date}`)
      return res.status(400).json({ 
        success: false,
        error: 'Invalid date format. Use ISO 8601 format (YYYY-MM-DD)' 
      })
    }

    // Validate artistId format (should be UUID)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(artistId)) {
      console.log(`[${requestId}] Invalid artistId format: ${artistId}`)
      return res.status(400).json({ 
        success: false,
        error: 'Invalid artistId format. Must be a valid UUID.' 
      })
    }

    console.log(`[${requestId}] Optimizing schedule for artist ${artistId} on ${targetDate.toISOString().split('T')[0]}`)

    // Initialize and run optimization
    const optimizer = new AISchedulingOptimizer()
    await optimizer.initialize()

    const startTime = Date.now()
    const result = await optimizer.optimizeSchedule(artistId, targetDate, newBooking)
    const processingTime = Date.now() - startTime

    console.log(`[${requestId}] Optimization completed in ${processingTime}ms`)

    // Add metadata to response
    const response = {
      success: result.success,
      requestId,
      processingTimeMs: processingTime,
      optimization: result,
      timestamp: new Date().toISOString(),
      apiVersion: '1.0.0'
    }

    // Log optimization results
    if (result.success) {
      console.log(`[${requestId}] Optimization successful:`, {
        originalBookings: result.originalSchedule?.length || 0,
        optimizedBookings: result.optimizedSchedule?.length || 0,
        efficiencyImprovement: result.improvements?.efficiencyImprovement || 0,
        conflictsResolved: result.improvements?.conflictsResolved || 0,
        travelTimeSaved: result.improvements?.travelTimeSaved || 0
      })
    } else {
      console.error(`[${requestId}] Optimization failed:`, result.error)
    }

    res.status(200).json(response)

  } catch (error) {
    console.error(`[${requestId}] Schedule optimization API error:`, error)
    
    // Determine error type and status code
    let statusCode = 500
    let errorMessage = 'Internal server error during schedule optimization'
    
    if (error.message.includes('Google Maps API key not configured')) {
      statusCode = 503
      errorMessage = 'Google Maps API not configured. Please configure the API key in admin settings.'
    } else if (error.message.includes('Failed to fetch schedule')) {
      statusCode = 503
      errorMessage = 'Unable to fetch artist schedule from database'
    } else if (error.message.includes('Database error')) {
      statusCode = 503
      errorMessage = 'Database connection error'
    }

    res.status(statusCode).json({ 
      success: false,
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
      requestId,
      timestamp: new Date().toISOString()
    })
  }
}

/**
 * API Documentation
 * 
 * POST /api/ai/optimize-schedule
 * 
 * Request Body:
 * {
 *   "artistId": "uuid",           // Required: Artist UUID
 *   "date": "2025-01-15",         // Required: Date in YYYY-MM-DD format
 *   "newBooking": {               // Optional: New booking to include in optimization
 *     "id": "uuid",
 *     "start_time": "2025-01-15T10:00:00Z",
 *     "end_time": "2025-01-15T11:00:00Z",
 *     "location": "123 Main St, Melbourne VIC",
 *     "duration": 60,
 *     "customerName": "John Doe",
 *     "serviceName": "Face Painting"
 *   },
 *   "options": {                  // Optional: Optimization options
 *     "prioritizeTravel": true,   // Minimize travel time
 *     "allowRescheduling": false, // Allow time adjustments
 *     "maxTravelTime": 120        // Maximum travel time in minutes
 *   }
 * }
 * 
 * Response:
 * {
 *   "success": true,
 *   "requestId": "abc123",
 *   "processingTimeMs": 1250,
 *   "optimization": {
 *     "success": true,
 *     "originalSchedule": [...],
 *     "optimizedSchedule": [...],
 *     "improvements": {
 *       "travelTimeSaved": 45,
 *       "efficiencyImprovement": 18.5,
 *       "conflictsResolved": 2
 *     },
 *     "recommendations": [
 *       {
 *         "type": "warning",
 *         "priority": "high",
 *         "message": "Tight schedule between bookings",
 *         "suggestion": "Consider adding buffer time"
 *       }
 *     ],
 *     "optimizedAt": "2025-01-15T08:30:00Z",
 *     "artistId": "uuid",
 *     "date": "2025-01-15"
 *   },
 *   "timestamp": "2025-01-15T08:30:00Z",
 *   "apiVersion": "1.0.0"
 * }
 * 
 * Error Response:
 * {
 *   "success": false,
 *   "error": "Error message",
 *   "details": "Detailed error (development only)",
 *   "requestId": "abc123",
 *   "timestamp": "2025-01-15T08:30:00Z"
 * }
 * 
 * Status Codes:
 * - 200: Success
 * - 400: Bad Request (invalid parameters)
 * - 401: Unauthorized (authentication required)
 * - 405: Method Not Allowed (use POST)
 * - 503: Service Unavailable (Google Maps API not configured)
 * - 500: Internal Server Error
 * 
 * Authentication:
 * - Requires admin authentication
 * - Include JWT token in Authorization header: "Bearer <token>"
 * 
 * Rate Limiting:
 * - No specific rate limits, but optimization is computationally intensive
 * - Results are cached for 30 minutes per artist/date combination
 * 
 * Dependencies:
 * - Google Maps Distance Matrix API (for travel time calculations)
 * - Supabase database (for booking data)
 * - Artist assignment system (for availability checking)
 */
