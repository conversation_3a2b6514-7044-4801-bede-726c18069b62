/**
 * Multi-Factor Authentication (MFA) Manager for Ocean Soul Sparkles
 * Provides TOTP-based MFA, SMS backup, and recovery codes
 * 
 * Phase 9.1: Enhanced Authentication System
 */

import crypto from 'crypto'
import { authenticator } from 'otplib'
import QRCode from 'qrcode'
import { createClient } from '@supabase/supabase-js'
import { encrypt, decrypt } from '@/lib/encryption'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * MFA Manager Class
 * Handles all multi-factor authentication operations
 */
export class MFAManager {
  constructor() {
    this.appName = 'Ocean Soul Sparkles'
    this.issuer = 'oceansoulsparkles.com.au'
  }

  /**
   * Generate TOTP secret for a user
   * @param {string} userId - User ID
   * @param {string} userEmail - User email
   * @returns {Promise<Object>} - Secret and QR code
   */
  async generateTOTPSecret(userId, userEmail) {
    try {
      // Generate a new secret
      const secret = authenticator.generateSecret()
      
      // Create the service name for the authenticator app
      const serviceName = `${this.appName} (${userEmail})`
      
      // Generate the otpauth URL
      const otpauthUrl = authenticator.keyuri(userEmail, this.issuer, secret)
      
      // Generate QR code
      const qrCodeDataUrl = await QRCode.toDataURL(otpauthUrl)
      
      // Encrypt the secret before storing
      const encryptedSecret = encrypt(secret)
      
      // Store the encrypted secret in the database
      const { error } = await supabase
        .from('user_mfa_settings')
        .upsert({
          user_id: userId,
          totp_secret: encryptedSecret,
          mfa_enabled: false, // Not enabled until verified
          updated_at: new Date().toISOString()
        })
      
      if (error) {
        throw new Error(`Failed to store MFA secret: ${error.message}`)
      }
      
      return {
        secret,
        qrCode: qrCodeDataUrl,
        manualEntryKey: secret,
        serviceName
      }
    } catch (error) {
      console.error('Error generating TOTP secret:', error)
      throw error
    }
  }

  /**
   * Verify TOTP token and enable MFA
   * @param {string} userId - User ID
   * @param {string} token - TOTP token from authenticator app
   * @returns {Promise<Object>} - Verification result with backup codes
   */
  async verifyAndEnableMFA(userId, token) {
    try {
      // Get the user's MFA settings
      const { data: mfaSettings, error } = await supabase
        .from('user_mfa_settings')
        .select('totp_secret')
        .eq('user_id', userId)
        .single()
      
      if (error || !mfaSettings) {
        throw new Error('MFA settings not found')
      }
      
      // Decrypt the secret
      const secret = decrypt(mfaSettings.totp_secret)
      
      // Verify the token
      const isValid = authenticator.verify({ token, secret })
      
      if (!isValid) {
        // Log failed verification
        await this.logMFAAttempt(userId, 'totp', false, 'Invalid TOTP token')
        return { success: false, error: 'Invalid verification code' }
      }
      
      // Generate backup codes
      const backupCodes = this.generateBackupCodes()
      const encryptedBackupCodes = backupCodes.map(code => encrypt(code))
      
      // Enable MFA and store backup codes
      const { error: updateError } = await supabase
        .from('user_mfa_settings')
        .update({
          mfa_enabled: true,
          backup_codes: encryptedBackupCodes,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
      
      if (updateError) {
        throw new Error(`Failed to enable MFA: ${updateError.message}`)
      }
      
      // Log successful verification
      await this.logMFAAttempt(userId, 'totp', true, 'MFA enabled successfully')
      
      // Log security event
      await this.logSecurityEvent(userId, 'mfa_enabled', 'User enabled multi-factor authentication', 'medium')
      
      return {
        success: true,
        backupCodes: backupCodes // Return unencrypted codes for user to save
      }
    } catch (error) {
      console.error('Error verifying and enabling MFA:', error)
      throw error
    }
  }

  /**
   * Verify TOTP token for login
   * @param {string} userId - User ID
   * @param {string} token - TOTP token
   * @returns {Promise<boolean>} - Verification result
   */
  async verifyTOTP(userId, token) {
    try {
      // Get the user's MFA settings
      const { data: mfaSettings, error } = await supabase
        .from('user_mfa_settings')
        .select('totp_secret, mfa_enabled')
        .eq('user_id', userId)
        .single()
      
      if (error || !mfaSettings || !mfaSettings.mfa_enabled) {
        await this.logMFAAttempt(userId, 'totp', false, 'MFA not enabled')
        return false
      }
      
      // Decrypt the secret
      const secret = decrypt(mfaSettings.totp_secret)
      
      // Verify the token with window tolerance
      const isValid = authenticator.verify({ 
        token, 
        secret,
        window: 1 // Allow 1 step tolerance (30 seconds before/after)
      })
      
      // Log the attempt
      await this.logMFAAttempt(userId, 'totp', isValid, isValid ? 'Valid TOTP' : 'Invalid TOTP')
      
      if (!isValid) {
        // Log security event for failed MFA
        await this.logSecurityEvent(userId, 'mfa_failed', 'Failed TOTP verification attempt', 'medium')
      }
      
      return isValid
    } catch (error) {
      console.error('Error verifying TOTP:', error)
      await this.logMFAAttempt(userId, 'totp', false, `Error: ${error.message}`)
      return false
    }
  }

  /**
   * Verify backup code
   * @param {string} userId - User ID
   * @param {string} code - Backup code
   * @returns {Promise<boolean>} - Verification result
   */
  async verifyBackupCode(userId, code) {
    try {
      // Get the user's backup codes
      const { data: mfaSettings, error } = await supabase
        .from('user_mfa_settings')
        .select('backup_codes, mfa_enabled')
        .eq('user_id', userId)
        .single()
      
      if (error || !mfaSettings || !mfaSettings.mfa_enabled || !mfaSettings.backup_codes) {
        await this.logMFAAttempt(userId, 'backup_code', false, 'No backup codes available')
        return false
      }
      
      // Decrypt and check backup codes
      const backupCodes = mfaSettings.backup_codes.map(encryptedCode => decrypt(encryptedCode))
      const codeIndex = backupCodes.findIndex(backupCode => backupCode === code)
      
      if (codeIndex === -1) {
        await this.logMFAAttempt(userId, 'backup_code', false, 'Invalid backup code')
        await this.logSecurityEvent(userId, 'mfa_failed', 'Failed backup code verification attempt', 'medium')
        return false
      }
      
      // Remove the used backup code
      const updatedBackupCodes = [...mfaSettings.backup_codes]
      updatedBackupCodes.splice(codeIndex, 1)
      
      // Update the database
      const { error: updateError } = await supabase
        .from('user_mfa_settings')
        .update({
          backup_codes: updatedBackupCodes,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
      
      if (updateError) {
        console.error('Error updating backup codes:', updateError)
      }
      
      // Log successful verification
      await this.logMFAAttempt(userId, 'backup_code', true, 'Valid backup code used')
      await this.logSecurityEvent(userId, 'backup_code_used', 'User used a backup code for authentication', 'low')
      
      return true
    } catch (error) {
      console.error('Error verifying backup code:', error)
      await this.logMFAAttempt(userId, 'backup_code', false, `Error: ${error.message}`)
      return false
    }
  }

  /**
   * Generate new backup codes
   * @returns {Array<string>} - Array of backup codes
   */
  generateBackupCodes() {
    const codes = []
    for (let i = 0; i < 10; i++) {
      // Generate 8-digit backup codes
      const code = crypto.randomBytes(4).toString('hex').toUpperCase()
      codes.push(code)
    }
    return codes
  }

  /**
   * Regenerate backup codes for a user
   * @param {string} userId - User ID
   * @returns {Promise<Array<string>>} - New backup codes
   */
  async regenerateBackupCodes(userId) {
    try {
      // Check if MFA is enabled
      const { data: mfaSettings, error } = await supabase
        .from('user_mfa_settings')
        .select('mfa_enabled')
        .eq('user_id', userId)
        .single()
      
      if (error || !mfaSettings || !mfaSettings.mfa_enabled) {
        throw new Error('MFA is not enabled for this user')
      }
      
      // Generate new backup codes
      const backupCodes = this.generateBackupCodes()
      const encryptedBackupCodes = backupCodes.map(code => encrypt(code))
      
      // Update the database
      const { error: updateError } = await supabase
        .from('user_mfa_settings')
        .update({
          backup_codes: encryptedBackupCodes,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
      
      if (updateError) {
        throw new Error(`Failed to update backup codes: ${updateError.message}`)
      }
      
      // Log security event
      await this.logSecurityEvent(userId, 'backup_codes_regenerated', 'User regenerated backup codes', 'low')
      
      return backupCodes
    } catch (error) {
      console.error('Error regenerating backup codes:', error)
      throw error
    }
  }

  /**
   * Disable MFA for a user
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} - Success status
   */
  async disableMFA(userId) {
    try {
      const { error } = await supabase
        .from('user_mfa_settings')
        .update({
          mfa_enabled: false,
          totp_secret: null,
          backup_codes: null,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
      
      if (error) {
        throw new Error(`Failed to disable MFA: ${error.message}`)
      }
      
      // Log security event
      await this.logSecurityEvent(userId, 'mfa_disabled', 'User disabled multi-factor authentication', 'medium')
      
      return true
    } catch (error) {
      console.error('Error disabling MFA:', error)
      throw error
    }
  }

  /**
   * Get MFA status for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - MFA status
   */
  async getMFAStatus(userId) {
    try {
      const { data: mfaSettings, error } = await supabase
        .from('user_mfa_settings')
        .select('mfa_enabled, mfa_enforced, backup_codes')
        .eq('user_id', userId)
        .single()
      
      if (error && error.code !== 'PGRST116') { // Not found is OK
        throw error
      }
      
      const backupCodesCount = mfaSettings?.backup_codes?.length || 0
      
      return {
        enabled: mfaSettings?.mfa_enabled || false,
        enforced: mfaSettings?.mfa_enforced || false,
        backupCodesRemaining: backupCodesCount
      }
    } catch (error) {
      console.error('Error getting MFA status:', error)
      return {
        enabled: false,
        enforced: false,
        backupCodesRemaining: 0
      }
    }
  }

  /**
   * Log MFA verification attempt
   * @param {string} userId - User ID
   * @param {string} type - Verification type
   * @param {boolean} success - Success status
   * @param {string} reason - Failure reason or success message
   */
  async logMFAAttempt(userId, type, success, reason) {
    try {
      await supabase
        .from('mfa_verification_logs')
        .insert({
          user_id: userId,
          verification_type: type,
          success,
          failure_reason: success ? null : reason,
          ip_address: null, // Will be set by API endpoint
          user_agent: null  // Will be set by API endpoint
        })
    } catch (error) {
      console.error('Error logging MFA attempt:', error)
    }
  }

  /**
   * Log security event
   * @param {string} userId - User ID
   * @param {string} eventType - Event type
   * @param {string} description - Event description
   * @param {string} severity - Event severity
   */
  async logSecurityEvent(userId, eventType, description, severity) {
    try {
      await supabase
        .from('security_events')
        .insert({
          user_id: userId,
          event_type: eventType,
          event_description: description,
          severity,
          additional_data: {}
        })
    } catch (error) {
      console.error('Error logging security event:', error)
    }
  }
}

// Export singleton instance
export const mfaManager = new MFAManager()
export default mfaManager
