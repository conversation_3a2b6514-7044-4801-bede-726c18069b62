/**
 * Comprehensive Audit Logger for Ocean Soul Sparkles
 * Enhanced audit trail for all data access and modifications
 * 
 * Phase 9.2: Data Protection & Privacy Compliance
 */

import { createClient } from '@supabase/supabase-js'
import crypto from 'crypto'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * Audit Logger Class
 * Handles comprehensive audit logging for compliance
 */
export class AuditLogger {
  constructor() {
    this.dataClassifications = {
      'customers': 'confidential',
      'bookings': 'internal',
      'payments': 'restricted',
      'user_roles': 'confidential',
      'admin_settings': 'restricted',
      'mfa_settings': 'confidential',
      'security_events': 'confidential'
    }
    this.retentionPeriods = {
      'confidential': '7 years',
      'restricted': '10 years',
      'internal': '3 years',
      'public': '1 year'
    }
    this.legalBasisOptions = [
      'consent',
      'contract',
      'legal_obligation',
      'vital_interests',
      'public_task',
      'legitimate_interests'
    ]
  }

  /**
   * Log data access event
   * @param {Object} accessData - Access event data
   * @returns {Promise<string>} - Log entry ID
   */
  async logDataAccess(accessData) {
    try {
      const {
        userId,
        customerId = null,
        tableName,
        recordId = null,
        accessType,
        fieldsAccessed = [],
        queryType = null,
        ipAddress = null,
        userAgent = null,
        sessionId = null,
        apiEndpoint = null,
        requestMethod = null,
        responseStatus = null,
        processingTimeMs = null,
        dataVolumeBytes = null,
        purpose = 'Business operation',
        legalBasis = 'legitimate_interests'
      } = accessData

      const dataClassification = this.dataClassifications[tableName] || 'internal'
      const retentionPeriod = this.retentionPeriods[dataClassification]

      const { data: logEntry, error } = await supabase
        .from('data_access_logs')
        .insert({
          user_id: userId,
          customer_id: customerId,
          table_name: tableName,
          record_id: recordId,
          access_type: accessType,
          data_classification: dataClassification,
          fields_accessed: fieldsAccessed,
          query_type: queryType,
          ip_address: ipAddress,
          user_agent: userAgent,
          session_id: sessionId,
          api_endpoint: apiEndpoint,
          request_method: requestMethod,
          response_status: responseStatus,
          processing_time_ms: processingTimeMs,
          data_volume_bytes: dataVolumeBytes,
          purpose,
          legal_basis: legalBasis,
          retention_period: retentionPeriod
        })
        .select('id')
        .single()

      if (error) {
        console.error('Error logging data access:', error)
        throw error
      }

      return logEntry.id
    } catch (error) {
      console.error('Error in logDataAccess:', error)
      throw error
    }
  }

  /**
   * Log data modification event
   * @param {Object} modificationData - Modification event data
   * @returns {Promise<string>} - Log entry ID
   */
  async logDataModification(modificationData) {
    try {
      const {
        userId,
        tableName,
        recordId,
        operation,
        oldValues = null,
        newValues = null,
        changedFields = [],
        changeReason = 'User modification',
        ipAddress = null,
        userAgent = null,
        sessionId = null
      } = modificationData

      const { data: logEntry, error } = await supabase
        .from('data_modification_logs')
        .insert({
          user_id: userId,
          table_name: tableName,
          record_id: recordId,
          operation,
          old_values: oldValues,
          new_values: newValues,
          changed_fields: changedFields,
          change_reason: changeReason,
          ip_address: ipAddress,
          user_agent: userAgent,
          session_id: sessionId
        })
        .select('id')
        .single()

      if (error) {
        console.error('Error logging data modification:', error)
        throw error
      }

      return logEntry.id
    } catch (error) {
      console.error('Error in logDataModification:', error)
      throw error
    }
  }

  /**
   * Log data retention event
   * @param {Object} retentionData - Retention event data
   * @returns {Promise<string>} - Log entry ID
   */
  async logDataRetention(retentionData) {
    try {
      const {
        tableName,
        recordId,
        retentionPolicy,
        retentionPeriod,
        deletionScheduledAt = null,
        deletionExecutedAt = null,
        deletionMethod = 'soft_delete',
        deletionReason,
        dataBackupLocation = null,
        executedBy = null
      } = retentionData

      const { data: logEntry, error } = await supabase
        .from('data_retention_logs')
        .insert({
          table_name: tableName,
          record_id: recordId,
          retention_policy: retentionPolicy,
          retention_period: retentionPeriod,
          deletion_scheduled_at: deletionScheduledAt,
          deletion_executed_at: deletionExecutedAt,
          deletion_method: deletionMethod,
          deletion_reason: deletionReason,
          data_backup_location: dataBackupLocation,
          executed_by: executedBy
        })
        .select('id')
        .single()

      if (error) {
        console.error('Error logging data retention:', error)
        throw error
      }

      return logEntry.id
    } catch (error) {
      console.error('Error in logDataRetention:', error)
      throw error
    }
  }

  /**
   * Create audit middleware for API endpoints
   * @param {Object} options - Middleware options
   * @returns {Function} - Express middleware function
   */
  createAuditMiddleware(options = {}) {
    const {
      logLevel = 'all', // 'all', 'modifications', 'sensitive'
      excludePaths = ['/health', '/metrics'],
      sensitiveEndpoints = ['/api/customers', '/api/bookings', '/api/payments']
    } = options

    return async (req, res, next) => {
      const startTime = Date.now()
      
      // Skip excluded paths
      if (excludePaths.some(path => req.path.startsWith(path))) {
        return next()
      }

      // Capture original res.json to log response
      const originalJson = res.json
      let responseData = null

      res.json = function(data) {
        responseData = data
        return originalJson.call(this, data)
      }

      // Continue with request
      next()

      // Log after response
      res.on('finish', async () => {
        try {
          const processingTime = Date.now() - startTime
          const shouldLog = this.shouldLogRequest(req, logLevel, sensitiveEndpoints)

          if (shouldLog) {
            await this.logApiRequest({
              req,
              res,
              processingTime,
              responseData
            })
          }
        } catch (error) {
          console.error('Error in audit middleware:', error)
        }
      })
    }
  }

  /**
   * Determine if request should be logged
   * @param {Object} req - Express request object
   * @param {string} logLevel - Logging level
   * @param {Array} sensitiveEndpoints - Sensitive endpoints
   * @returns {boolean} - Whether to log
   */
  shouldLogRequest(req, logLevel, sensitiveEndpoints) {
    if (logLevel === 'all') return true
    
    if (logLevel === 'modifications') {
      return ['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method)
    }
    
    if (logLevel === 'sensitive') {
      return sensitiveEndpoints.some(endpoint => req.path.startsWith(endpoint))
    }
    
    return false
  }

  /**
   * Log API request
   * @param {Object} requestData - Request data
   */
  async logApiRequest(requestData) {
    try {
      const { req, res, processingTime, responseData } = requestData
      
      const userId = req.user?.id || null
      const customerId = req.body?.customer_id || req.params?.customerId || null
      
      // Determine table name from endpoint
      const tableName = this.extractTableNameFromPath(req.path)
      const recordId = req.params?.id || null
      
      // Calculate data volume
      const requestSize = JSON.stringify(req.body || {}).length
      const responseSize = JSON.stringify(responseData || {}).length
      const dataVolumeBytes = requestSize + responseSize

      await this.logDataAccess({
        userId,
        customerId,
        tableName,
        recordId,
        accessType: req.method.toLowerCase(),
        fieldsAccessed: this.extractFieldsFromRequest(req, responseData),
        queryType: req.method,
        ipAddress: this.getClientIP(req),
        userAgent: req.get('User-Agent'),
        sessionId: req.sessionID,
        apiEndpoint: req.path,
        requestMethod: req.method,
        responseStatus: res.statusCode,
        processingTimeMs: processingTime,
        dataVolumeBytes,
        purpose: this.determinePurpose(req.path, req.method),
        legalBasis: this.determineLegalBasis(req.path)
      })
    } catch (error) {
      console.error('Error logging API request:', error)
    }
  }

  /**
   * Extract table name from API path
   * @param {string} path - API path
   * @returns {string} - Table name
   */
  extractTableNameFromPath(path) {
    const pathSegments = path.split('/').filter(segment => segment)
    
    // Map API paths to table names
    const pathToTable = {
      'customers': 'customers',
      'bookings': 'bookings',
      'services': 'services',
      'artists': 'artist_profiles',
      'events': 'events',
      'payments': 'payments',
      'admin': 'admin_settings',
      'security': 'security_events'
    }
    
    for (const segment of pathSegments) {
      if (pathToTable[segment]) {
        return pathToTable[segment]
      }
    }
    
    return 'unknown'
  }

  /**
   * Extract fields from request and response
   * @param {Object} req - Request object
   * @param {Object} responseData - Response data
   * @returns {Array} - Field names
   */
  extractFieldsFromRequest(req, responseData) {
    const fields = new Set()
    
    // Add fields from request body
    if (req.body && typeof req.body === 'object') {
      Object.keys(req.body).forEach(key => fields.add(key))
    }
    
    // Add fields from response data
    if (responseData && typeof responseData === 'object') {
      if (Array.isArray(responseData)) {
        responseData.forEach(item => {
          if (typeof item === 'object') {
            Object.keys(item).forEach(key => fields.add(key))
          }
        })
      } else {
        Object.keys(responseData).forEach(key => fields.add(key))
      }
    }
    
    return Array.from(fields)
  }

  /**
   * Get client IP address
   * @param {Object} req - Request object
   * @returns {string} - IP address
   */
  getClientIP(req) {
    return req.headers['x-forwarded-for']?.split(',')[0] ||
           req.headers['x-real-ip'] ||
           req.connection?.remoteAddress ||
           req.socket?.remoteAddress ||
           'unknown'
  }

  /**
   * Determine purpose from request
   * @param {string} path - API path
   * @param {string} method - HTTP method
   * @returns {string} - Purpose
   */
  determinePurpose(path, method) {
    if (path.includes('/admin')) return 'Administrative operation'
    if (path.includes('/booking')) return 'Booking management'
    if (path.includes('/customer')) return 'Customer service'
    if (path.includes('/payment')) return 'Payment processing'
    if (path.includes('/security')) return 'Security operation'
    
    return 'Business operation'
  }

  /**
   * Determine legal basis from request
   * @param {string} path - API path
   * @returns {string} - Legal basis
   */
  determineLegalBasis(path) {
    if (path.includes('/payment')) return 'contract'
    if (path.includes('/security')) return 'legal_obligation'
    if (path.includes('/marketing')) return 'consent'
    
    return 'legitimate_interests'
  }

  /**
   * Get audit statistics
   * @param {Object} filters - Filter options
   * @returns {Promise<Object>} - Audit statistics
   */
  async getAuditStatistics(filters = {}) {
    try {
      const {
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        endDate = new Date(),
        tableName = null,
        userId = null
      } = filters

      // Get access logs statistics
      let accessQuery = supabase
        .from('data_access_logs')
        .select('access_type, data_classification, table_name, created_at')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString())

      if (tableName) accessQuery = accessQuery.eq('table_name', tableName)
      if (userId) accessQuery = accessQuery.eq('user_id', userId)

      const { data: accessLogs, error: accessError } = await accessQuery

      if (accessError) throw accessError

      // Get modification logs statistics
      let modificationQuery = supabase
        .from('data_modification_logs')
        .select('operation, table_name, created_at')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString())

      if (tableName) modificationQuery = modificationQuery.eq('table_name', tableName)
      if (userId) modificationQuery = modificationQuery.eq('user_id', userId)

      const { data: modificationLogs, error: modificationError } = await modificationQuery

      if (modificationError) throw modificationError

      // Calculate statistics
      const stats = {
        totalAccessEvents: accessLogs?.length || 0,
        totalModificationEvents: modificationLogs?.length || 0,
        accessByType: {},
        accessByClassification: {},
        accessByTable: {},
        modificationsByOperation: {},
        modificationsByTable: {},
        dailyActivity: {}
      }

      // Process access logs
      accessLogs?.forEach(log => {
        stats.accessByType[log.access_type] = (stats.accessByType[log.access_type] || 0) + 1
        stats.accessByClassification[log.data_classification] = (stats.accessByClassification[log.data_classification] || 0) + 1
        stats.accessByTable[log.table_name] = (stats.accessByTable[log.table_name] || 0) + 1
        
        const date = new Date(log.created_at).toISOString().split('T')[0]
        stats.dailyActivity[date] = (stats.dailyActivity[date] || 0) + 1
      })

      // Process modification logs
      modificationLogs?.forEach(log => {
        stats.modificationsByOperation[log.operation] = (stats.modificationsByOperation[log.operation] || 0) + 1
        stats.modificationsByTable[log.table_name] = (stats.modificationsByTable[log.table_name] || 0) + 1
        
        const date = new Date(log.created_at).toISOString().split('T')[0]
        stats.dailyActivity[date] = (stats.dailyActivity[date] || 0) + 1
      })

      return stats
    } catch (error) {
      console.error('Error getting audit statistics:', error)
      throw error
    }
  }

  /**
   * Search audit logs
   * @param {Object} searchCriteria - Search criteria
   * @returns {Promise<Array>} - Matching audit logs
   */
  async searchAuditLogs(searchCriteria) {
    try {
      const {
        tableName,
        userId,
        accessType,
        startDate,
        endDate,
        ipAddress,
        limit = 100,
        offset = 0
      } = searchCriteria

      let query = supabase
        .from('data_access_logs')
        .select('*')
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1)

      if (tableName) query = query.eq('table_name', tableName)
      if (userId) query = query.eq('user_id', userId)
      if (accessType) query = query.eq('access_type', accessType)
      if (startDate) query = query.gte('created_at', startDate)
      if (endDate) query = query.lte('created_at', endDate)
      if (ipAddress) query = query.eq('ip_address', ipAddress)

      const { data: logs, error } = await query

      if (error) throw error

      return logs || []
    } catch (error) {
      console.error('Error searching audit logs:', error)
      throw error
    }
  }
}

// Export singleton instance
export const auditLogger = new AuditLogger()
export default auditLogger
