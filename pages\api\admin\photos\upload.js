/**
 * Photo Upload API Endpoint
 * Ocean Soul Sparkles - <PERSON><PERSON> photo uploads with Supabase storage integration
 */

import { createClient } from '@supabase/supabase-js'
import formidable from 'formidable'
import fs from 'fs'
import path from 'path'
import { v4 as uuidv4 } from 'uuid'

// Disable default body parser
export const config = {
  api: {
    bodyParser: false,
  },
}

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

const supabase = createClient(supabaseUrl, supabaseServiceKey)

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Parse form data
    const form = formidable({
      maxFileSize: 10 * 1024 * 1024, // 10MB limit
      keepExtensions: true,
      multiples: false
    })

    const [fields, files] = await form.parse(req)
    
    const photo = files.photo?.[0]
    if (!photo) {
      return res.status(400).json({ error: 'No photo file provided' })
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(photo.mimetype)) {
      return res.status(400).json({ 
        error: 'Invalid file type. Only JPEG, PNG, and WebP are allowed.' 
      })
    }

    // Extract metadata
    const type = fields.type?.[0] || 'portfolio'
    const bookingId = fields.bookingId?.[0] || null
    const timestamp = fields.timestamp?.[0] || new Date().toISOString()

    // Generate unique filename
    const fileExtension = path.extname(photo.originalFilename || '.jpg')
    const fileName = `${type}_${uuidv4()}${fileExtension}`
    const filePath = `photos/${type}/${fileName}`

    // Read file data
    const fileData = fs.readFileSync(photo.filepath)

    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('ocean-soul-sparkles')
      .upload(filePath, fileData, {
        contentType: photo.mimetype,
        cacheControl: '3600',
        upsert: false
      })

    if (uploadError) {
      console.error('Supabase upload error:', uploadError)
      return res.status(500).json({ error: 'Failed to upload photo' })
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('ocean-soul-sparkles')
      .getPublicUrl(filePath)

    // Save photo metadata to database
    const photoRecord = {
      id: uuidv4(),
      filename: fileName,
      original_filename: photo.originalFilename,
      file_path: filePath,
      file_size: photo.size,
      mime_type: photo.mimetype,
      type: type,
      booking_id: bookingId,
      public_url: urlData.publicUrl,
      uploaded_at: timestamp,
      created_at: new Date().toISOString(),
      metadata: {
        width: null, // Could be extracted with image processing library
        height: null,
        device_info: req.headers['user-agent']
      }
    }

    const { data: dbData, error: dbError } = await supabase
      .from('photos')
      .insert([photoRecord])
      .select()
      .single()

    if (dbError) {
      console.error('Database insert error:', dbError)
      
      // Clean up uploaded file if database insert fails
      await supabase.storage
        .from('ocean-soul-sparkles')
        .remove([filePath])
      
      return res.status(500).json({ error: 'Failed to save photo metadata' })
    }

    // Clean up temporary file
    fs.unlinkSync(photo.filepath)

    // If this is a booking photo, update the booking record
    if (bookingId && (type === 'before' || type === 'after')) {
      try {
        const updateField = type === 'before' ? 'before_photo_url' : 'after_photo_url'
        
        await supabase
          .from('bookings')
          .update({ [updateField]: urlData.publicUrl })
          .eq('id', bookingId)
      } catch (error) {
        console.warn('Failed to update booking with photo URL:', error)
        // Don't fail the request if booking update fails
      }
    }

    return res.status(200).json({
      success: true,
      photo: {
        id: dbData.id,
        filename: dbData.filename,
        url: dbData.public_url,
        type: dbData.type,
        size: dbData.file_size,
        uploadedAt: dbData.uploaded_at
      }
    })

  } catch (error) {
    console.error('Photo upload error:', error)
    return res.status(500).json({ 
      error: 'Internal server error',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

// Helper function to create photos table if it doesn't exist
export async function ensurePhotosTable() {
  const { error } = await supabase.rpc('create_photos_table_if_not_exists')
  
  if (error) {
    console.error('Failed to ensure photos table exists:', error)
  }
}

// SQL function to create photos table (should be run in Supabase SQL editor)
/*
CREATE OR REPLACE FUNCTION create_photos_table_if_not_exists()
RETURNS void AS $$
BEGIN
  IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'photos') THEN
    CREATE TABLE photos (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      filename TEXT NOT NULL,
      original_filename TEXT,
      file_path TEXT NOT NULL,
      file_size INTEGER,
      mime_type TEXT,
      type TEXT NOT NULL CHECK (type IN ('before', 'after', 'portfolio', 'receipt')),
      booking_id UUID REFERENCES bookings(id) ON DELETE SET NULL,
      public_url TEXT NOT NULL,
      uploaded_at TIMESTAMPTZ DEFAULT NOW(),
      created_at TIMESTAMPTZ DEFAULT NOW(),
      metadata JSONB DEFAULT '{}',
      
      -- Indexes
      INDEX idx_photos_type (type),
      INDEX idx_photos_booking_id (booking_id),
      INDEX idx_photos_uploaded_at (uploaded_at)
    );
    
    -- Enable RLS
    ALTER TABLE photos ENABLE ROW LEVEL SECURITY;
    
    -- Create policies
    CREATE POLICY "Photos are viewable by authenticated users" ON photos
      FOR SELECT USING (auth.role() = 'authenticated');
    
    CREATE POLICY "Photos can be inserted by authenticated users" ON photos
      FOR INSERT WITH CHECK (auth.role() = 'authenticated');
    
    CREATE POLICY "Photos can be updated by authenticated users" ON photos
      FOR UPDATE USING (auth.role() = 'authenticated');
    
    CREATE POLICY "Photos can be deleted by authenticated users" ON photos
      FOR DELETE USING (auth.role() = 'authenticated');
  END IF;
END;
$$ LANGUAGE plpgsql;
*/
