import { useState } from 'react'
import CustomerInfoForm from './CustomerInfoForm'
import PaymentMethodSelector from './PaymentMethodSelector'
import POSSquarePaymentNew from './POSSquarePaymentNew'
import POSSquareTerminal from './POSSquareTerminal'
import POSSquareReader from './POSSquareReader'
import { safeRender } from '@/lib/safe-render-utils'
import styles from '@/styles/admin/POS.module.css'

/**
 * POSCheckout component for handling the complete checkout process
 *
 * @param {Object} props - Component props
 * @param {Object} props.service - Selected service
 * @param {Object} props.artist - Selected artist
 * @param {Object} props.tier - Selected pricing tier
 * @param {Object} props.timeSlot - Selected time slot
 * @param {Function} props.onBack - Callback to go back
 * @param {Function} props.onComplete - Callback when transaction is complete
 * @returns {JSX.Element}
 */
export default function POSCheckout({ service, artist, tier, timeSlot, onBack, onComplete }) {
  const [checkoutStep, setCheckoutStep] = useState('customer') // 'customer', 'payment', 'processing'
  const [customerData, setCustomerData] = useState(null)
  const [paymentMethod, setPaymentMethod] = useState(null)
  const [paymentMethodData, setPaymentMethodData] = useState(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState(null)

  // Calculate total amount
  const totalAmount = parseFloat(tier?.price || 0)

  // Format duration
  const formatDuration = (minutes) => {
    if (!minutes) return 'Duration not specified'
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours === 0) return `${mins} minutes`
    if (mins === 0) return `${hours} hour${hours > 1 ? 's' : ''}`
    return `${hours}h ${mins}m`
  }

  const handleCustomerData = (data) => {
    setCustomerData(data)
    setCheckoutStep('payment')
  }

  const handlePaymentMethodSelect = (method, methodData) => {
    setPaymentMethod(method)
    setPaymentMethodData(methodData)

    if (method === 'cash') {
      // For cash payments, process with the cash details from the form
      processCashPayment(methodData)
    }
    // For card and terminal payments, the respective Square components will handle the flow
  }

  const processCashPayment = async (methodData) => {
    try {
      setIsProcessing(true)
      setError(null)

      // Create booking and payment record with cash details
      const result = await createBookingAndPayment('cash', {
        cashReceived: methodData?.cashReceived,
        changeAmount: methodData?.changeAmount,
        totalAmount: methodData?.totalAmount || totalAmount
      })

      if (result.success) {
        onComplete(result)
      } else {
        throw new Error(result.error || 'Failed to process cash payment')
      }
    } catch (err) {
      console.error('Cash payment error:', err)
      setError(err.message)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleSquarePaymentSuccess = async (paymentResult) => {
    try {
      setIsProcessing(true)
      setError(null)

      // Create booking and payment record with Square transaction details
      const paymentMethodType = paymentResult.paymentDetails?.deviceId ? 'square_terminal' : 'card'
      const result = await createBookingAndPayment(paymentMethodType, paymentResult)

      if (result.success) {
        onComplete(result)
      } else {
        throw new Error(result.error || 'Failed to record payment')
      }
    } catch (err) {
      console.error('Payment completion error:', err)
      setError(err.message)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleSquarePaymentError = (error) => {
    console.error('Square payment error:', error)
    setError(error.message || 'Card payment failed')
  }

  const createBookingAndPayment = async (paymentMethod, paymentDetails = null) => {
    try {
      console.log('🔄 Creating POS booking...')

      // Get authentication token from Supabase session (simple approach)
      const { supabase } = await import('@/lib/supabase')

      // Get current session without forcing refresh
      const { data: { session }, error } = await supabase.auth.getSession()

      console.log('Booking authentication check:', {
        hasSession: !!session,
        hasUser: !!session?.user,
        hasToken: !!session?.access_token,
        userEmail: session?.user?.email,
        error: error?.message
      })

      if (!session?.access_token) {
        const errorMsg = error?.message || 'No active session'
        console.error('❌ Booking authentication failed:', errorMsg)
        throw new Error(`Authentication required for booking creation. Please refresh the page and log in again. (${errorMsg})`)
      }

      console.log('✅ Booking authentication successful for user:', session.user?.email)

      const response = await fetch('/api/admin/pos/create-booking', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({
          service: {
            id: service.id,
            name: service.name
          },
          artist: {
            id: artist.id,
            name: artist.name
          },
          tier: {
            id: tier.id,
            name: tier.name,
            duration: tier.duration,
            price: tier.price
          },
          timeSlot: {
            time: timeSlot.time,
            date: new Date(timeSlot.time).toISOString().split('T')[0]
          },
          customer: customerData,
          payment: {
            method: paymentMethod,
            amount: totalAmount,
            currency: 'AUD',
            details: paymentDetails
          }
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create booking')
      }

      return data
    } catch (error) {
      console.error('Booking creation error:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  const renderOrderSummary = () => (
    <div className={styles.orderSummary}>
      <h4>Order Summary</h4>
      <div className={styles.summaryItem}>
        <span className={styles.serviceName}>
          {safeRender(service.name, 'Service')}
        </span>
        <span className={styles.tierName}>
          {safeRender(tier.name, 'Tier')} - {formatDuration(tier.duration)}
        </span>
        <span className={styles.artistName}>
          Artist: {safeRender(artist.name, 'Artist')}
        </span>
        <span className={styles.timeSlot}>
          {timeSlot ? new Date(timeSlot.time).toLocaleString('en-AU', {
            weekday: 'short',
            year: 'numeric',
            month: 'short', 
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          }) : 'Time not selected'}
        </span>
        <span className={styles.price}>
          ${totalAmount.toFixed(2)}
        </span>
      </div>
      <div className={styles.summaryTotal}>
        <strong>Total: ${totalAmount.toFixed(2)} AUD</strong>
      </div>
    </div>
  )

  return (
    <div className={styles.checkoutContainer}>
      <div className={styles.checkoutHeader}>
        <h2>Checkout</h2>
        {renderOrderSummary()}
      </div>

      {error && (
        <div className={styles.checkoutError}>
          <span className={styles.errorIcon}>⚠️</span>
          {error}
          <button
            onClick={() => setError(null)}
            className={styles.dismissError}
          >
            ×
          </button>
        </div>
      )}

      <div className={styles.checkoutContent}>
        {checkoutStep === 'customer' && (
          <CustomerInfoForm
            onCustomerData={handleCustomerData}
            isLoading={isProcessing}
          />
        )}

        {checkoutStep === 'payment' && !paymentMethod && (
          <PaymentMethodSelector
            onPaymentMethodSelect={handlePaymentMethodSelect}
            amount={totalAmount}
            isLoading={isProcessing}
          />
        )}

        {checkoutStep === 'payment' && paymentMethod === 'cash' && isProcessing && (
          <div className={styles.processingCash}>
            <div className={styles.processingIcon}>💵</div>
            <h3>Processing Cash Payment</h3>
            <p>Recording transaction...</p>
            <div className={styles.loadingSpinner}></div>
          </div>
        )}

        {checkoutStep === 'payment' && paymentMethod === 'card' && (
          <POSSquarePaymentNew
            amount={paymentMethodData?.totalAmount || totalAmount}
            currency="AUD"
            onSuccess={handleSquarePaymentSuccess}
            onError={handleSquarePaymentError}
            orderDetails={{
              service: service.name,
              tier: tier.name,
              customer: customerData?.name || 'Walk-in Customer',
              originalAmount: paymentMethodData?.originalAmount,
              processingFee: paymentMethodData?.processingFee,
              totalAmount: paymentMethodData?.totalAmount
            }}
          />
        )}

        {checkoutStep === 'payment' && paymentMethod === 'square_terminal' && (
          <POSSquareTerminal
            amount={totalAmount}
            currency="AUD"
            onSuccess={handleSquarePaymentSuccess}
            onError={handleSquarePaymentError}
            onCancel={() => setPaymentMethod(null)}
            orderDetails={{
              service: service.name,
              tier: tier.name,
              customer: customerData?.name || 'Walk-in Customer',
              orderId: `pos_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`
            }}
          />
        )}

        {checkoutStep === 'payment' && paymentMethod === 'square_reader' && (
          <POSSquareReader
            amount={paymentMethodData?.totalAmount || totalAmount}
            currency="AUD"
            onSuccess={handleSquarePaymentSuccess}
            onError={handleSquarePaymentError}
            onCancel={() => setPaymentMethod(null)}
            orderDetails={{
              service: service.name,
              tier: tier.name,
              customer: customerData?.name || 'Walk-in Customer',
              orderId: `pos_reader_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`,
              originalAmount: paymentMethodData?.originalAmount,
              processingFee: paymentMethodData?.processingFee,
              totalAmount: paymentMethodData?.totalAmount
            }}
          />
        )}
      </div>

      <div className={styles.checkoutNavigation}>
        {checkoutStep === 'customer' && (
          <button
            className={styles.backButton}
            onClick={onBack}
            disabled={isProcessing}
          >
            ← Back to Duration
          </button>
        )}

        {checkoutStep === 'payment' && !isProcessing && (
          <button
            className={styles.backButton}
            onClick={() => setCheckoutStep('customer')}
          >
            ← Back to Customer Info
          </button>
        )}
      </div>
    </div>
  )
}
