/**
 * Comprehensive Database Schema and RLS Verification for Phase 6 AI Features
 * Ocean Soul Sparkles - AI Database Verification Script
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });
dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Error: Supabase credentials not found');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🔍 Starting Comprehensive AI Database Schema Verification...\n');

// Test results tracking
const results = {
  tables: {},
  columns: {},
  indexes: {},
  constraints: {},
  rls: {},
  policies: {},
  settings: {},
  errors: []
};

/**
 * Execute SQL query with error handling
 */
async function executeSQL(query, description) {
  try {
    const { data, error } = await supabase.rpc('execute_sql', { sql: query });
    if (error) {
      console.error(`❌ ${description}:`, error.message);
      results.errors.push(`${description}: ${error.message}`);
      return null;
    }
    return data;
  } catch (error) {
    console.error(`❌ ${description}:`, error.message);
    results.errors.push(`${description}: ${error.message}`);
    return null;
  }
}

/**
 * 1. Verify AI Tables Exist and Structure
 */
async function verifyAITables() {
  console.log('📊 1. VERIFYING AI TABLES STRUCTURE\n');
  
  const aiTables = [
    'ai_insights_cache',
    'ai_recommendations', 
    'ai_compatibility_scores',
    'ai_travel_time_cache',
    'ai_optimization_history'
  ];
  
  for (const tableName of aiTables) {
    console.log(`🔍 Checking table: ${tableName}`);
    
    // Check if table exists
    const tableQuery = `
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = '${tableName}'
      );
    `;
    
    const tableExists = await executeSQL(tableQuery, `Check table ${tableName} exists`);
    
    if (tableExists && tableExists[0]?.exists) {
      console.log(`  ✅ Table ${tableName} exists`);
      results.tables[tableName] = 'exists';
      
      // Get table structure
      const structureQuery = `
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = '${tableName}'
        ORDER BY ordinal_position;
      `;
      
      const structure = await executeSQL(structureQuery, `Get ${tableName} structure`);
      if (structure) {
        console.log(`  📋 Columns (${structure.length}):`, structure.map(c => `${c.column_name}(${c.data_type})`).join(', '));
        results.columns[tableName] = structure;
      }
      
    } else {
      console.log(`  ❌ Table ${tableName} does not exist`);
      results.tables[tableName] = 'missing';
    }
    console.log('');
  }
}

/**
 * 2. Verify Enhanced Existing Tables
 */
async function verifyEnhancedTables() {
  console.log('🔧 2. VERIFYING ENHANCED EXISTING TABLES\n');
  
  const enhancements = {
    'artist_profiles': [
      'ai_optimization_enabled',
      'last_ai_analysis', 
      'ai_performance_score'
    ],
    'bookings': [
      'ai_optimized',
      'ai_confidence_score',
      'travel_time_minutes',
      'ai_optimization_applied_at',
      'original_start_time'
    ]
  };
  
  for (const [tableName, expectedColumns] of Object.entries(enhancements)) {
    console.log(`🔍 Checking enhanced table: ${tableName}`);
    
    for (const columnName of expectedColumns) {
      const columnQuery = `
        SELECT EXISTS (
          SELECT FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = '${tableName}'
          AND column_name = '${columnName}'
        );
      `;
      
      const columnExists = await executeSQL(columnQuery, `Check column ${tableName}.${columnName}`);
      
      if (columnExists && columnExists[0]?.exists) {
        console.log(`  ✅ Column ${columnName} exists`);
      } else {
        console.log(`  ❌ Column ${columnName} missing`);
        results.errors.push(`Missing column: ${tableName}.${columnName}`);
      }
    }
    console.log('');
  }
}

/**
 * 3. Verify Indexes
 */
async function verifyIndexes() {
  console.log('📇 3. VERIFYING INDEXES\n');
  
  const expectedIndexes = [
    'idx_ai_insights_cache_key',
    'idx_ai_insights_cache_date', 
    'idx_ai_insights_cache_expires',
    'idx_ai_recommendations_type',
    'idx_ai_recommendations_status',
    'idx_ai_compatibility_customer',
    'idx_ai_compatibility_artist',
    'idx_ai_travel_cache_locations',
    'idx_ai_travel_cache_expires'
  ];
  
  for (const indexName of expectedIndexes) {
    const indexQuery = `
      SELECT EXISTS (
        SELECT FROM pg_indexes 
        WHERE schemaname = 'public' 
        AND indexname = '${indexName}'
      );
    `;
    
    const indexExists = await executeSQL(indexQuery, `Check index ${indexName}`);
    
    if (indexExists && indexExists[0]?.exists) {
      console.log(`✅ Index ${indexName} exists`);
      results.indexes[indexName] = 'exists';
    } else {
      console.log(`❌ Index ${indexName} missing`);
      results.indexes[indexName] = 'missing';
    }
  }
  console.log('');
}

/**
 * 4. Verify RLS is Enabled
 */
async function verifyRLSEnabled() {
  console.log('🔒 4. VERIFYING ROW LEVEL SECURITY\n');
  
  const aiTables = [
    'ai_insights_cache',
    'ai_recommendations',
    'ai_compatibility_scores', 
    'ai_travel_time_cache',
    'ai_optimization_history'
  ];
  
  for (const tableName of aiTables) {
    const rlsQuery = `
      SELECT relrowsecurity 
      FROM pg_class 
      WHERE relname = '${tableName}' 
      AND relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');
    `;
    
    const rlsEnabled = await executeSQL(rlsQuery, `Check RLS on ${tableName}`);
    
    if (rlsEnabled && rlsEnabled[0]?.relrowsecurity) {
      console.log(`✅ RLS enabled on ${tableName}`);
      results.rls[tableName] = 'enabled';
    } else {
      console.log(`❌ RLS not enabled on ${tableName}`);
      results.rls[tableName] = 'disabled';
    }
  }
  console.log('');
}

/**
 * 5. Verify RLS Policies
 */
async function verifyRLSPolicies() {
  console.log('🛡️ 5. VERIFYING RLS POLICIES\n');
  
  const policiesQuery = `
    SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
    FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename LIKE 'ai_%'
    ORDER BY tablename, policyname;
  `;
  
  const policies = await executeSQL(policiesQuery, 'Get AI table policies');
  
  if (policies && policies.length > 0) {
    console.log(`✅ Found ${policies.length} RLS policies for AI tables:`);
    policies.forEach(policy => {
      console.log(`  📋 ${policy.tablename}.${policy.policyname} (${policy.cmd})`);
      console.log(`     Roles: ${policy.roles || 'all'}`);
      console.log(`     Condition: ${policy.qual || 'none'}`);
    });
    results.policies.ai_tables = policies;
  } else {
    console.log(`❌ No RLS policies found for AI tables`);
    results.errors.push('No RLS policies found for AI tables');
  }
  console.log('');
}

/**
 * 6. Verify AI Configuration Settings
 */
async function verifyAISettings() {
  console.log('⚙️ 6. VERIFYING AI CONFIGURATION SETTINGS\n');
  
  const expectedSettings = [
    'ai_features_enabled',
    'ai_cache_duration',
    'ai_max_recommendations', 
    'ai_confidence_threshold'
  ];
  
  for (const settingKey of expectedSettings) {
    try {
      const { data, error } = await supabase
        .from('admin_settings')
        .select('setting_key, setting_value')
        .eq('setting_key', settingKey)
        .single();
      
      if (error) {
        console.log(`❌ Setting ${settingKey}: ${error.message}`);
        results.settings[settingKey] = 'missing';
      } else {
        console.log(`✅ Setting ${settingKey} = ${data.setting_value}`);
        results.settings[settingKey] = data.setting_value;
      }
    } catch (error) {
      console.log(`❌ Setting ${settingKey}: ${error.message}`);
      results.settings[settingKey] = 'error';
    }
  }
  console.log('');
}

/**
 * 7. Test Database Performance
 */
async function testDatabasePerformance() {
  console.log('⚡ 7. TESTING DATABASE PERFORMANCE\n');
  
  // Test basic queries on AI tables
  const performanceTests = [
    {
      name: 'AI Insights Cache Query',
      query: 'SELECT COUNT(*) FROM ai_insights_cache;'
    },
    {
      name: 'AI Recommendations Query', 
      query: 'SELECT COUNT(*) FROM ai_recommendations;'
    },
    {
      name: 'AI Compatibility Scores Query',
      query: 'SELECT COUNT(*) FROM ai_compatibility_scores;'
    }
  ];
  
  for (const test of performanceTests) {
    const startTime = Date.now();
    const result = await executeSQL(test.query, test.name);
    const duration = Date.now() - startTime;
    
    if (result) {
      console.log(`✅ ${test.name}: ${duration}ms`);
    } else {
      console.log(`❌ ${test.name}: Failed`);
    }
  }
  console.log('');
}

/**
 * 8. Test RLS Policy Enforcement
 */
async function testRLSPolicyEnforcement() {
  console.log('🧪 8. TESTING RLS POLICY ENFORCEMENT\n');
  
  // Test admin access (should work)
  try {
    const { data, error } = await supabase
      .from('ai_insights_cache')
      .select('id')
      .limit(1);
    
    if (!error) {
      console.log('✅ Admin access to ai_insights_cache: Working');
    } else {
      console.log(`⚠️ Admin access to ai_insights_cache: ${error.message}`);
    }
  } catch (error) {
    console.log(`❌ Admin access test failed: ${error.message}`);
  }
  
  // Test table accessibility
  const testTables = ['ai_recommendations', 'ai_compatibility_scores', 'ai_travel_time_cache'];
  
  for (const tableName of testTables) {
    try {
      const { data, error } = await supabase
        .from(tableName)
        .select('id')
        .limit(1);
      
      if (!error) {
        console.log(`✅ Access to ${tableName}: Working`);
      } else {
        console.log(`⚠️ Access to ${tableName}: ${error.message}`);
      }
    } catch (error) {
      console.log(`❌ Access test for ${tableName} failed: ${error.message}`);
    }
  }
  console.log('');
}

/**
 * Generate Final Report
 */
function generateReport() {
  console.log('📋 FINAL VERIFICATION REPORT');
  console.log('='.repeat(50));
  
  // Tables Summary
  const tableCount = Object.keys(results.tables).length;
  const existingTables = Object.values(results.tables).filter(status => status === 'exists').length;
  console.log(`\n📊 AI TABLES: ${existingTables}/${tableCount} exist`);
  
  // Indexes Summary  
  const indexCount = Object.keys(results.indexes).length;
  const existingIndexes = Object.values(results.indexes).filter(status => status === 'exists').length;
  console.log(`📇 INDEXES: ${existingIndexes}/${indexCount} exist`);
  
  // RLS Summary
  const rlsCount = Object.keys(results.rls).length;
  const enabledRLS = Object.values(results.rls).filter(status => status === 'enabled').length;
  console.log(`🔒 RLS ENABLED: ${enabledRLS}/${rlsCount} tables`);
  
  // Settings Summary
  const settingsCount = Object.keys(results.settings).length;
  const configuredSettings = Object.values(results.settings).filter(status => status !== 'missing' && status !== 'error').length;
  console.log(`⚙️ AI SETTINGS: ${configuredSettings}/${settingsCount} configured`);
  
  // Errors Summary
  if (results.errors.length > 0) {
    console.log(`\n❌ ERRORS FOUND (${results.errors.length}):`);
    results.errors.forEach(error => console.log(`   • ${error}`));
  } else {
    console.log(`\n✅ NO ERRORS FOUND`);
  }
  
  // Overall Status
  const totalIssues = results.errors.length;
  const missingTables = Object.values(results.tables).filter(status => status === 'missing').length;
  const missingIndexes = Object.values(results.indexes).filter(status => status === 'missing').length;
  const disabledRLS = Object.values(results.rls).filter(status => status === 'disabled').length;
  
  console.log('\n🎯 OVERALL STATUS:');
  if (totalIssues === 0 && missingTables === 0 && missingIndexes === 0 && disabledRLS === 0) {
    console.log('✅ PHASE 6 AI DATABASE SCHEMA: FULLY CONFIGURED AND READY!');
  } else {
    console.log('⚠️ PHASE 6 AI DATABASE SCHEMA: ISSUES FOUND - REVIEW REQUIRED');
  }
  
  console.log('\n🚀 Verification Complete!');
}

/**
 * Main Execution
 */
async function main() {
  try {
    // Test connection first
    const { data, error } = await supabase.from('admin_settings').select('setting_key').limit(1);
    if (error) {
      console.error('❌ Database connection failed:', error.message);
      return;
    }
    console.log('✅ Database connection successful\n');
    
    // Run all verification steps
    await verifyAITables();
    await verifyEnhancedTables();
    await verifyIndexes();
    await verifyRLSEnabled();
    await verifyRLSPolicies();
    await verifyAISettings();
    await testDatabasePerformance();
    await testRLSPolicyEnforcement();
    
    // Generate final report
    generateReport();
    
  } catch (error) {
    console.error('❌ Verification failed:', error);
    process.exit(1);
  }
}

main();
