/**
 * Real-time Dashboard Test Page
 * Ocean Soul Sparkles - Test WebSocket functionality
 */

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRealTimeDashboard } from '@/lib/hooks/useRealTimeDashboard'
import ConnectionStatusIndicator, { ConnectionStatusBanner, ConnectionStatusDot } from '@/components/admin/ConnectionStatusIndicator'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import AdminLayout from '@/components/admin/AdminLayout'
import { toast } from 'react-toastify'
import styles from '@/styles/admin/TestRealtime.module.css'

export default function TestRealtime() {
  const { user } = useAuth()
  const [testMessages, setTestMessages] = useState([])
  const [connectionLogs, setConnectionLogs] = useState([])

  const {
    connectionStatus,
    isConnected,
    error,
    dashboardData,
    lastUpdated,
    connect,
    disconnect,
    refresh,
    websocketClient
  } = useRealTimeDashboard({
    autoConnect: true,
    fallbackRefreshInterval: 30000, // 30 seconds for testing
    enableNotifications: true
  })

  // Log connection events
  useEffect(() => {
    const logEvent = (event, data) => {
      const timestamp = new Date().toLocaleTimeString()
      setConnectionLogs(prev => [...prev.slice(-19), { // Keep last 20 logs
        timestamp,
        event,
        data: JSON.stringify(data, null, 2)
      }])
    }

    // Add event listeners for testing
    websocketClient.on('connection', (data) => logEvent('connection', data))
    websocketClient.on('dashboardUpdate', (data) => logEvent('dashboardUpdate', data))
    websocketClient.on('bookingNotification', (data) => logEvent('bookingNotification', data))
    websocketClient.on('availabilityUpdate', (data) => logEvent('availabilityUpdate', data))
    websocketClient.on('error', (data) => logEvent('error', data))

    return () => {
      websocketClient.off('connection')
      websocketClient.off('dashboardUpdate')
      websocketClient.off('bookingNotification')
      websocketClient.off('availabilityUpdate')
      websocketClient.off('error')
    }
  }, [websocketClient])

  const sendTestMessage = () => {
    if (isConnected) {
      websocketClient.send({
        type: 'test_message',
        payload: {
          message: 'Test message from client',
          timestamp: new Date().toISOString()
        }
      })
      toast.info('Test message sent')
    } else {
      toast.error('Not connected to WebSocket')
    }
  }

  const triggerTestNotification = () => {
    // Simulate a booking notification
    const testNotification = {
      type: 'booking_notification',
      payload: {
        type: 'new_booking',
        booking: {
          id: 'test-' + Date.now(),
          start_time: new Date(Date.now() + 60000).toISOString(),
          service_name: 'Test Service',
          customer_name: 'Test Customer'
        }
      }
    }
    
    setTestMessages(prev => [...prev, testNotification])
    toast.success('Test notification triggered')
  }

  const clearLogs = () => {
    setConnectionLogs([])
    setTestMessages([])
  }

  return (
    <ProtectedRoute>
      <AdminLayout>
        <div className={styles.container}>
          <div className={styles.header}>
            <h1>Real-time Dashboard Test</h1>
            <p>Test WebSocket connectivity and real-time features</p>
          </div>

          {/* Connection Status Section */}
          <div className={styles.section}>
            <h2>Connection Status</h2>
            <div className={styles.statusGrid}>
              <div className={styles.statusCard}>
                <h3>Current Status</h3>
                <ConnectionStatusIndicator
                  connectionStatus={connectionStatus}
                  lastUpdated={lastUpdated}
                  onRetry={connect}
                />
                <div className={styles.statusDetails}>
                  <p><strong>Connected:</strong> {isConnected ? 'Yes' : 'No'}</p>
                  <p><strong>Status:</strong> {connectionStatus}</p>
                  <p><strong>Last Updated:</strong> {lastUpdated ? lastUpdated.toLocaleTimeString() : 'Never'}</p>
                  <p><strong>Error:</strong> {error || 'None'}</p>
                </div>
              </div>

              <div className={styles.statusCard}>
                <h3>Connection Controls</h3>
                <div className={styles.controls}>
                  <button 
                    onClick={connect}
                    disabled={isConnected}
                    className={styles.button}
                  >
                    Connect
                  </button>
                  <button 
                    onClick={disconnect}
                    disabled={!isConnected}
                    className={styles.button}
                  >
                    Disconnect
                  </button>
                  <button 
                    onClick={refresh}
                    className={styles.button}
                  >
                    Refresh Data
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Test Controls Section */}
          <div className={styles.section}>
            <h2>Test Controls</h2>
            <div className={styles.controls}>
              <button 
                onClick={sendTestMessage}
                disabled={!isConnected}
                className={styles.button}
              >
                Send Test Message
              </button>
              <button 
                onClick={triggerTestNotification}
                className={styles.button}
              >
                Trigger Test Notification
              </button>
              <button 
                onClick={clearLogs}
                className={styles.button}
              >
                Clear Logs
              </button>
            </div>
          </div>

          {/* Dashboard Data Section */}
          <div className={styles.section}>
            <h2>Dashboard Data</h2>
            <div className={styles.dataCard}>
              {dashboardData ? (
                <pre className={styles.dataDisplay}>
                  {JSON.stringify(dashboardData, null, 2)}
                </pre>
              ) : (
                <p>No dashboard data available</p>
              )}
            </div>
          </div>

          {/* Connection Logs Section */}
          <div className={styles.section}>
            <h2>Connection Logs</h2>
            <div className={styles.logsContainer}>
              {connectionLogs.length > 0 ? (
                connectionLogs.map((log, index) => (
                  <div key={index} className={styles.logEntry}>
                    <span className={styles.logTimestamp}>{log.timestamp}</span>
                    <span className={styles.logEvent}>{log.event}</span>
                    <pre className={styles.logData}>{log.data}</pre>
                  </div>
                ))
              ) : (
                <p>No connection logs yet</p>
              )}
            </div>
          </div>

          {/* Test Messages Section */}
          <div className={styles.section}>
            <h2>Test Messages</h2>
            <div className={styles.messagesContainer}>
              {testMessages.length > 0 ? (
                testMessages.map((message, index) => (
                  <div key={index} className={styles.messageEntry}>
                    <span className={styles.messageType}>{message.type}</span>
                    <pre className={styles.messageData}>
                      {JSON.stringify(message.payload, null, 2)}
                    </pre>
                  </div>
                ))
              ) : (
                <p>No test messages yet</p>
              )}
            </div>
          </div>

          {/* Connection Banner Test */}
          <ConnectionStatusBanner
            connectionStatus={connectionStatus}
            onRetry={connect}
            onDismiss={() => console.log('Banner dismissed')}
          />
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}
