/* =============================================
   ADMIN SETTINGS MODULE STYLES
   Ocean Soul Sparkles Admin Interface
   ============================================= */

/* Main Container */
.settingsContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: #f8f9fa;
  min-height: calc(100vh - 80px);
}

.settingsHeader {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  margin-bottom: 2rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.settingsHeader h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.settingsHeader p {
  font-size: 1.2rem;
  margin: 0;
  opacity: 0.9;
  font-weight: 300;
}

.settingsContent {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Settings Sections */
.settingsSection {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
}

.settingsSection h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 1.5rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 3px solid #667eea;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.sectionHeader h2 {
  margin: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.sectionActions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* Instructions List */
.instructionsList {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.instructionStep {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.stepNumber {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1rem;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.stepContent {
  flex: 1;
}

.stepContent h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.stepContent p {
  font-size: 1rem;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

.stepContent code {
  background: #f1f3f4;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: #d63384;
}

/* Buttons */
.primaryButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.primaryButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.primaryButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.secondaryButton {
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.secondaryButton:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Error Messages */
.errorMessage {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.errorIcon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

/* Verification Results */
.verificationResults {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.overallStatus {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  border: 2px solid #e9ecef;
  margin-bottom: 1.5rem;
  text-align: center;
}

.overallStatus h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
}

.overallStatus p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

/* Checks Grid */
.checksGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.checkItem {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
}

.checkItem:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.checkIcon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.checkLabel {
  font-weight: 600;
  color: #333;
  flex: 1;
}

.checkValue {
  font-size: 0.9rem;
  color: #666;
  font-family: 'Courier New', monospace;
}

/* Recommendations */
.recommendations {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.recommendations h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #856404;
  margin: 0 0 1rem 0;
}

.recommendations ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #856404;
}

.recommendations li {
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

/* Technical Details */
.technicalDetails {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
}

.technicalDetails h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 1rem 0;
}

.detailsGrid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.detailItem {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.detailItem:last-child {
  border-bottom: none;
}

.detailLabel {
  font-weight: 600;
  color: #333;
  min-width: 120px;
  flex-shrink: 0;
}

.detailValue {
  color: #666;
  word-break: break-all;
  flex: 1;
}

.detailValue a {
  color: #667eea;
  text-decoration: none;
}

.detailValue a:hover {
  text-decoration: underline;
}

/* Next Steps */
.nextStepsList {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  border: 1px solid #bee5eb;
  border-radius: 8px;
  padding: 1.5rem;
}

.nextStepsList p {
  font-weight: 600;
  color: #0c5460;
  margin: 0 0 1rem 0;
}

.nextStepsList ol {
  margin: 0;
  padding-left: 1.5rem;
  color: #0c5460;
}

.nextStepsList li {
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .settingsContainer {
    padding: 1rem;
  }

  .settingsHeader {
    padding: 1.5rem;
  }

  .settingsHeader h1 {
    font-size: 2rem;
  }

  .settingsHeader p {
    font-size: 1rem;
  }

  .settingsSection {
    padding: 1.5rem;
  }

  .sectionHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .sectionActions {
    justify-content: center;
  }

  .instructionStep {
    flex-direction: column;
    text-align: center;
  }

  .stepNumber {
    align-self: center;
  }

  .checksGrid {
    grid-template-columns: 1fr;
  }

  .detailItem {
    flex-direction: column;
    gap: 0.5rem;
  }

  .detailLabel {
    min-width: auto;
  }
}
