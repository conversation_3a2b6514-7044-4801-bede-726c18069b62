/**
 * WebSocket Client for Real-time Dashboard Updates
 * Ocean Soul Sparkles - Artist Dashboard Real-time Features
 */

class WebSocketClient {
  constructor() {
    this.ws = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectDelay = 1000
    this.heartbeatInterval = null
    this.listeners = new Map()
    this.connectionStatus = 'disconnected'
    this.userId = null
    this.userRole = null
  }

  /**
   * Initialize WebSocket connection
   * @param {string} userId - User ID for authentication
   * @param {string} userRole - User role (artist/braider)
   * @param {string} token - Authentication token
   */
  connect(userId, userRole, token) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log('[WebSocket] Already connected')
      return
    }

    this.userId = userId
    this.userRole = userRole
    
    try {
      // Use secure WebSocket in production, regular in development
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      const wsUrl = `${protocol}//${window.location.host}/api/websocket/dashboard?token=${encodeURIComponent(token)}&userId=${encodeURIComponent(userId)}&role=${encodeURIComponent(userRole)}`
      
      console.log('[WebSocket] Connecting to:', wsUrl.replace(/token=[^&]+/, 'token=***'))
      
      this.ws = new WebSocket(wsUrl)
      this.setupEventHandlers()
      
    } catch (error) {
      console.error('[WebSocket] Connection error:', error)
      this.handleConnectionError()
    }
  }

  /**
   * Setup WebSocket event handlers
   */
  setupEventHandlers() {
    this.ws.onopen = () => {
      console.log('[WebSocket] Connected successfully')
      this.connectionStatus = 'connected'
      this.reconnectAttempts = 0
      this.startHeartbeat()
      this.emit('connection', { status: 'connected' })
    }

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        console.log('[WebSocket] Message received:', data.type)
        this.handleMessage(data)
      } catch (error) {
        console.error('[WebSocket] Message parsing error:', error)
      }
    }

    this.ws.onclose = (event) => {
      console.log('[WebSocket] Connection closed:', event.code, event.reason)
      this.connectionStatus = 'disconnected'
      this.stopHeartbeat()
      this.emit('connection', { status: 'disconnected' })
      
      // Attempt reconnection if not intentionally closed
      if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
        this.scheduleReconnect()
      }
    }

    this.ws.onerror = (error) => {
      console.error('[WebSocket] Error:', error)
      this.connectionStatus = 'error'
      this.emit('connection', { status: 'error', error })
      this.handleConnectionError()
    }
  }

  /**
   * Handle incoming WebSocket messages
   * @param {Object} data - Message data
   */
  handleMessage(data) {
    switch (data.type) {
      case 'dashboard_update':
        this.emit('dashboardUpdate', data.payload)
        break
      case 'booking_notification':
        this.emit('bookingNotification', data.payload)
        break
      case 'availability_update':
        this.emit('availabilityUpdate', data.payload)
        break
      case 'performance_metrics_update':
        this.emit('performanceUpdate', data.payload)
        break
      case 'heartbeat':
        // Respond to server heartbeat
        this.send({ type: 'heartbeat_response' })
        break
      case 'error':
        console.error('[WebSocket] Server error:', data.payload)
        this.emit('error', data.payload)
        break
      default:
        console.warn('[WebSocket] Unknown message type:', data.type)
    }
  }

  /**
   * Send message to server
   * @param {Object} data - Message data
   */
  send(data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data))
    } else {
      console.warn('[WebSocket] Cannot send message - connection not open')
    }
  }

  /**
   * Subscribe to dashboard updates for specific data types
   * @param {Array} dataTypes - Types of data to subscribe to
   */
  subscribe(dataTypes = ['bookings', 'availability', 'metrics']) {
    this.send({
      type: 'subscribe',
      payload: {
        userId: this.userId,
        userRole: this.userRole,
        dataTypes
      }
    })
  }

  /**
   * Start heartbeat to keep connection alive
   */
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.send({ type: 'heartbeat' })
      }
    }, 30000) // Send heartbeat every 30 seconds
  }

  /**
   * Stop heartbeat
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  /**
   * Schedule reconnection attempt
   */
  scheduleReconnect() {
    this.reconnectAttempts++
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1) // Exponential backoff

    console.log(`[WebSocket] Scheduling reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`)

    setTimeout(() => {
      if (this.userId && this.userRole) {
        // We need to get a fresh token for reconnection
        this.emit('reconnectNeeded', { attempt: this.reconnectAttempts })
      }
    }, delay)
  }

  /**
   * Handle connection errors
   */
  handleConnectionError() {
    this.connectionStatus = 'error'
    this.stopHeartbeat()

    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.scheduleReconnect()
    } else {
      console.error('[WebSocket] Max reconnection attempts reached')
      this.emit('maxReconnectAttemptsReached')
    }
  }

  /**
   * Add event listener
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }

  /**
   * Remove event listener
   * @param {string} event - Event name
   * @param {Function} callback - Callback function to remove
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  /**
   * Emit event to listeners
   * @param {string} event - Event name
   * @param {*} data - Event data
   */
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`[WebSocket] Error in event listener for ${event}:`, error)
        }
      })
    }
  }

  /**
   * Get current connection status
   * @returns {string} Connection status
   */
  getConnectionStatus() {
    return this.connectionStatus
  }

  /**
   * Disconnect WebSocket
   */
  disconnect() {
    console.log('[WebSocket] Disconnecting...')
    this.stopHeartbeat()

    if (this.ws) {
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
    }

    this.connectionStatus = 'disconnected'
    this.userId = null
    this.userRole = null
    this.reconnectAttempts = 0
  }

  /**
   * Request immediate dashboard data refresh
   */
  requestRefresh() {
    this.send({
      type: 'refresh_request',
      payload: {
        userId: this.userId,
        timestamp: Date.now()
      }
    })
  }
}

// Create singleton instance
const websocketClient = new WebSocketClient()

export default websocketClient
