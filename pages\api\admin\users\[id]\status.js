import { getAdminClient } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'

/**
 * API endpoint for managing user account status
 * PATCH /api/admin/users/[id]/status - Update user status (deactivate/reactivate)
 * DELETE /api/admin/users/[id]/status - Delete user account (with safety checks)
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7)
  const { id: userId } = req.query

  console.log(`[${requestId}] User status management API called: ${req.method} for user: ${userId}`)

  try {
    // Verify admin authentication
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.authorized) {
      return res.status(401).json({ error: authResult.error?.message || 'Unauthorized' })
    }

    const { user: adminUser, role: adminRole } = authResult

    // Ensure user has admin privileges
    if (!['dev', 'admin'].includes(adminRole)) {
      return res.status(403).json({ error: 'Admin privileges required' })
    }

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' })
    }

    // Get admin client
    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Failed to initialize admin client' })
    }

    switch (req.method) {
      case 'PATCH':
        return await handleUpdateStatus(req, res, adminClient, adminUser, userId, requestId)
      case 'DELETE':
        return await handleDeleteUser(req, res, adminClient, adminUser, userId, requestId)
      default:
        return res.status(405).json({ error: 'Method not allowed' })
    }
  } catch (error) {
    console.error(`[${requestId}] Unexpected error in user status management:`, error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}

// Handle status updates (deactivate/reactivate)
async function handleUpdateStatus(req, res, adminClient, adminUser, userId, requestId) {
  const { status, reason = 'Updated by administrator' } = req.body

  if (!status || !['active', 'inactive', 'deactivated'].includes(status)) {
    return res.status(400).json({ error: 'Valid status is required (active, inactive, deactivated)' })
  }

  console.log(`[${requestId}] Updating user status to: ${status}`)

  // Get current user details
  const { data: userData, error: userError } = await adminClient.auth.admin.getUserById(userId)
  if (userError || !userData.user) {
    console.error(`[${requestId}] Error fetching user:`, userError)
    return res.status(404).json({ error: 'User not found' })
  }

  // Get user profile
  const { data: userProfile, error: profileError } = await adminClient
    .from('user_profiles')
    .select('*')
    .eq('id', userId)
    .single()

  if (profileError) {
    console.error(`[${requestId}] Error fetching user profile:`, profileError)
    return res.status(404).json({ error: 'User profile not found' })
  }

  // Prepare update data
  const updateData = {
    user_status: status,
    updated_at: new Date().toISOString()
  }

  // Add deactivation fields if deactivating
  if (status === 'deactivated') {
    updateData.deactivated_at = new Date().toISOString()
    // Validate admin user ID is a proper UUID before setting deactivated_by
    const adminUserId = adminUser.id
    if (adminUserId && typeof adminUserId === 'string' && adminUserId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      // Special handling for development admin UUID - don't set deactivated_by if it's the dev admin
      if (adminUserId === '00000000-0000-4000-8000-000000000001') {
        console.log(`[${requestId}] Development admin detected, skipping deactivated_by field to avoid foreign key constraint`)
        // Don't set deactivated_by for development admin to avoid foreign key issues
      } else {
        updateData.deactivated_by = adminUserId
      }
    } else {
      console.warn(`[${requestId}] Invalid admin user ID format: ${adminUserId}, skipping deactivated_by field`)
      // Don't set deactivated_by if admin ID is not a valid UUID
    }
  } else if (userProfile.user_status === 'deactivated' && status === 'active') {
    // Clear deactivation fields when reactivating
    updateData.deactivated_at = null
    updateData.deactivated_by = null
  }

  // Update user profile
  const { error: updateError } = await adminClient
    .from('user_profiles')
    .update(updateData)
    .eq('id', userId)

  if (updateError) {
    console.error(`[${requestId}] Error updating user status:`, updateError)
    return res.status(500).json({ error: 'Failed to update user status' })
  }

  // Log the action
  try {
    await adminClient
      .from('admin_activity_log')
      .insert([
        {
          admin_user_id: adminUser.id,
          action: `user_status_${status}`,
          target_type: 'user',
          target_id: userId,
          details: {
            previous_status: userProfile.user_status,
            new_status: status,
            reason: reason,
            user_email: userData.user.email,
            updated_at: new Date().toISOString()
          }
        }
      ])
  } catch (logError) {
    console.error(`[${requestId}] Error logging activity:`, logError)
    // Continue anyway
  }

  console.log(`[${requestId}] User status updated successfully`)

  return res.status(200).json({
    success: true,
    message: `User status updated to ${status}`,
    data: {
      userId: userId,
      email: userData.user.email,
      previousStatus: userProfile.user_status,
      newStatus: status,
      updatedAt: new Date().toISOString()
    }
  })
}

// Handle user deletion (with safety checks)
async function handleDeleteUser(req, res, adminClient, adminUser, userId, requestId) {
  const { force = false } = req.body

  console.log(`[${requestId}] Attempting to delete user: ${userId}`)

  // Get user details
  const { data: userData, error: userError } = await adminClient.auth.admin.getUserById(userId)
  if (userError || !userData.user) {
    console.error(`[${requestId}] Error fetching user:`, userError)
    return res.status(404).json({ error: 'User not found' })
  }

  // Safety checks - look for associated data
  const safetyChecks = []

  // Check for bookings
  const { count: bookingsCount } = await adminClient
    .from('bookings')
    .select('*', { count: 'exact', head: true })
    .eq('customer_id', userId)

  if (bookingsCount > 0) {
    safetyChecks.push(`${bookingsCount} booking(s)`)
  }

  // Check for payments
  const { count: paymentsCount } = await adminClient
    .from('payments')
    .select('*', { count: 'exact', head: true })
    .eq('customer_id', userId)

  if (paymentsCount > 0) {
    safetyChecks.push(`${paymentsCount} payment(s)`)
  }

  // Check for applications
  const { count: applicationsCount } = await adminClient
    .from('artist_braider_applications')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', userId)

  if (applicationsCount > 0) {
    safetyChecks.push(`${applicationsCount} application(s)`)
  }

  // If there are safety concerns and not forced, return error
  if (safetyChecks.length > 0 && !force) {
    return res.status(400).json({
      error: 'Cannot delete user with associated data',
      message: `User has ${safetyChecks.join(', ')}. Consider deactivating instead of deleting.`,
      associatedData: {
        bookings: bookingsCount,
        payments: paymentsCount,
        applications: applicationsCount
      },
      canDelete: false
    })
  }

  // Proceed with deletion
  console.log(`[${requestId}] Proceeding with user deletion`)

  // Delete related data first (in order)
  const deletionSteps = []

  // Delete application tokens
  const { error: tokensError } = await adminClient
    .from('application_tokens')
    .delete()
    .eq('user_id', userId)

  if (tokensError) {
    console.warn(`[${requestId}] Warning: Could not delete application tokens:`, tokensError)
  } else {
    deletionSteps.push('application_tokens')
  }

  // Delete applications (if forced)
  if (applicationsCount > 0) {
    const { error: appsError } = await adminClient
      .from('artist_braider_applications')
      .delete()
      .eq('user_id', userId)

    if (appsError) {
      console.error(`[${requestId}] Error deleting applications:`, appsError)
      return res.status(500).json({ error: 'Failed to delete user applications' })
    } else {
      deletionSteps.push('applications')
    }
  }

  // Delete user profile
  const { error: profileError } = await adminClient
    .from('user_profiles')
    .delete()
    .eq('id', userId)

  if (profileError) {
    console.error(`[${requestId}] Error deleting user profile:`, profileError)
    return res.status(500).json({ error: 'Failed to delete user profile' })
  } else {
    deletionSteps.push('user_profile')
  }

  // Delete user role
  const { error: roleError } = await adminClient
    .from('user_roles')
    .delete()
    .eq('id', userId)

  if (roleError) {
    console.error(`[${requestId}] Error deleting user role:`, roleError)
    return res.status(500).json({ error: 'Failed to delete user role' })
  } else {
    deletionSteps.push('user_role')
  }

  // Finally, delete the auth user
  const { error: authDeleteError } = await adminClient.auth.admin.deleteUser(userId)

  if (authDeleteError) {
    console.error(`[${requestId}] Error deleting auth user:`, authDeleteError)
    return res.status(500).json({ error: 'Failed to delete user from authentication system' })
  } else {
    deletionSteps.push('auth_user')
  }

  // Log the deletion
  try {
    await adminClient
      .from('admin_activity_log')
      .insert([
        {
          admin_user_id: adminUser.id,
          action: 'delete_user',
          target_type: 'user',
          target_id: userId,
          details: {
            user_email: userData.user.email,
            deletion_forced: force,
            deleted_components: deletionSteps,
            associated_data: {
              bookings: bookingsCount,
              payments: paymentsCount,
              applications: applicationsCount
            },
            deleted_at: new Date().toISOString()
          }
        }
      ])
  } catch (logError) {
    console.error(`[${requestId}] Error logging deletion:`, logError)
    // Continue anyway
  }

  console.log(`[${requestId}] User deleted successfully`)

  return res.status(200).json({
    success: true,
    message: 'User deleted successfully',
    data: {
      userId: userId,
      email: userData.user.email,
      deletedComponents: deletionSteps,
      deletedAt: new Date().toISOString()
    }
  })
}
