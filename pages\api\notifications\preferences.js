import { supabase } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * Notification Preferences API Endpoint
 * Handles CRUD operations for user notification preferences
 */
export default async function handler(req, res) {
  try {
    // Authenticate request
    const authResult = await authenticateAdminRequest(req);
    if (!authResult.success) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const { user_id } = req.query;
    const targetUserId = user_id || authResult.user.id;

    // Check if user can access these preferences
    if (targetUserId !== authResult.user.id && !['admin', 'dev'].includes(authResult.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions to access these preferences'
      });
    }

    switch (req.method) {
      case 'GET':
        return await getNotificationPreferences(req, res, targetUserId);
      case 'POST':
      case 'PUT':
        return await updateNotificationPreferences(req, res, targetUserId);
      case 'DELETE':
        return await resetNotificationPreferences(req, res, targetUserId);
      default:
        return res.status(405).json({ 
          success: false, 
          error: 'Method not allowed' 
        });
    }

  } catch (error) {
    console.error('Notification preferences error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to manage notification preferences: ' + error.message
    });
  }
}

/**
 * Get notification preferences for a user
 */
async function getNotificationPreferences(req, res, userId) {
  try {
    const { data: preferences, error } = await supabase
      .from('notification_preferences')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw error;
    }

    // If no preferences exist, return default preferences
    if (!preferences) {
      const defaultPreferences = {
        user_id: userId,
        email_notifications: true,
        sms_notifications: false,
        push_notifications: true,
        booking_reminders: true,
        reminder_minutes: 10,
        payment_notifications: true,
        event_notifications: true,
        marketing_notifications: false,
        phone_number: null,
        booking_alerts: {
          new_booking: true,
          booking_changes: true,
          cancellations: true,
          customer_inquiries: true,
          schedule_conflicts: true
        },
        revenue_alerts: {
          daily_summary: false,
          weekly_summary: true,
          milestone_notifications: true,
          payment_received: true
        },
        system_alerts: {
          maintenance_notifications: true,
          security_alerts: true,
          feature_updates: false,
          emergency_broadcasts: true
        },
        quiet_hours: {
          enabled: false,
          start_time: '22:00',
          end_time: '08:00',
          timezone: 'Australia/Sydney'
        },
        notification_channels: {
          urgent: ['push', 'email'],
          normal: ['push'],
          marketing: ['email']
        }
      };

      return res.status(200).json({
        success: true,
        data: defaultPreferences,
        is_default: true
      });
    }

    return res.status(200).json({
      success: true,
      data: preferences,
      is_default: false
    });

  } catch (error) {
    console.error('Error getting notification preferences:', error);
    throw error;
  }
}

/**
 * Update notification preferences for a user
 */
async function updateNotificationPreferences(req, res, userId) {
  try {
    const {
      email_notifications,
      sms_notifications,
      push_notifications,
      booking_reminders,
      reminder_minutes,
      payment_notifications,
      event_notifications,
      marketing_notifications,
      phone_number,
      booking_alerts,
      revenue_alerts,
      system_alerts,
      quiet_hours,
      notification_channels
    } = req.body;

    // Validate reminder_minutes
    if (reminder_minutes !== undefined && (reminder_minutes < 1 || reminder_minutes > 1440)) {
      return res.status(400).json({
        success: false,
        error: 'Reminder minutes must be between 1 and 1440 (24 hours)'
      });
    }

    // Validate phone number format if provided
    if (phone_number && !isValidPhoneNumber(phone_number)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid phone number format'
      });
    }

    // Validate quiet hours
    if (quiet_hours && quiet_hours.enabled) {
      if (!quiet_hours.start_time || !quiet_hours.end_time) {
        return res.status(400).json({
          success: false,
          error: 'Start time and end time are required when quiet hours are enabled'
        });
      }
    }

    // Validate notification channels
    if (notification_channels) {
      const validChannels = ['push', 'email', 'sms'];
      for (const [priority, channels] of Object.entries(notification_channels)) {
        if (!Array.isArray(channels)) {
          return res.status(400).json({
            success: false,
            error: `Notification channels for ${priority} must be an array`
          });
        }
        
        for (const channel of channels) {
          if (!validChannels.includes(channel)) {
            return res.status(400).json({
              success: false,
              error: `Invalid notification channel: ${channel}`
            });
          }
        }
      }
    }

    // Prepare update data
    const updateData = {
      user_id: userId,
      updated_at: new Date().toISOString()
    };

    // Only include provided fields
    if (email_notifications !== undefined) updateData.email_notifications = email_notifications;
    if (sms_notifications !== undefined) updateData.sms_notifications = sms_notifications;
    if (push_notifications !== undefined) updateData.push_notifications = push_notifications;
    if (booking_reminders !== undefined) updateData.booking_reminders = booking_reminders;
    if (reminder_minutes !== undefined) updateData.reminder_minutes = reminder_minutes;
    if (payment_notifications !== undefined) updateData.payment_notifications = payment_notifications;
    if (event_notifications !== undefined) updateData.event_notifications = event_notifications;
    if (marketing_notifications !== undefined) updateData.marketing_notifications = marketing_notifications;
    if (phone_number !== undefined) updateData.phone_number = phone_number;
    if (booking_alerts !== undefined) updateData.booking_alerts = booking_alerts;
    if (revenue_alerts !== undefined) updateData.revenue_alerts = revenue_alerts;
    if (system_alerts !== undefined) updateData.system_alerts = system_alerts;
    if (quiet_hours !== undefined) updateData.quiet_hours = quiet_hours;
    if (notification_channels !== undefined) updateData.notification_channels = notification_channels;

    // Upsert preferences
    const { data: preferences, error } = await supabase
      .from('notification_preferences')
      .upsert(updateData, { onConflict: 'user_id' })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return res.status(200).json({
      success: true,
      data: preferences,
      message: 'Notification preferences updated successfully'
    });

  } catch (error) {
    console.error('Error updating notification preferences:', error);
    throw error;
  }
}

/**
 * Reset notification preferences to defaults
 */
async function resetNotificationPreferences(req, res, userId) {
  try {
    const { error } = await supabase
      .from('notification_preferences')
      .delete()
      .eq('user_id', userId);

    if (error) {
      throw error;
    }

    return res.status(200).json({
      success: true,
      message: 'Notification preferences reset to defaults'
    });

  } catch (error) {
    console.error('Error resetting notification preferences:', error);
    throw error;
  }
}

/**
 * Validate phone number format
 */
function isValidPhoneNumber(phoneNumber) {
  // Basic validation for Australian phone numbers
  // Accepts formats like: +***********, 0412345678, ***********
  const phoneRegex = /^(\+?61|0)?[2-9]\d{8}$/;
  const cleanedNumber = phoneNumber.replace(/[\s\-\(\)]/g, '');
  return phoneRegex.test(cleanedNumber);
}

/**
 * Export function for use in other modules
 */
export async function getUserNotificationPreferences(userId) {
  try {
    const { data: preferences, error } = await supabase
      .from('notification_preferences')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw error;
    }

    // Return default preferences if none exist
    if (!preferences) {
      return {
        user_id: userId,
        email_notifications: true,
        sms_notifications: false,
        push_notifications: true,
        booking_reminders: true,
        reminder_minutes: 10,
        payment_notifications: true,
        event_notifications: true,
        marketing_notifications: false,
        phone_number: null,
        booking_alerts: {
          new_booking: true,
          booking_changes: true,
          cancellations: true,
          customer_inquiries: true,
          schedule_conflicts: true
        },
        revenue_alerts: {
          daily_summary: false,
          weekly_summary: true,
          milestone_notifications: true,
          payment_received: true
        },
        system_alerts: {
          maintenance_notifications: true,
          security_alerts: true,
          feature_updates: false,
          emergency_broadcasts: true
        },
        quiet_hours: {
          enabled: false,
          start_time: '22:00',
          end_time: '08:00',
          timezone: 'Australia/Sydney'
        },
        notification_channels: {
          urgent: ['push', 'email'],
          normal: ['push'],
          marketing: ['email']
        }
      };
    }

    return preferences;

  } catch (error) {
    console.error('Error getting user notification preferences:', error);
    throw error;
  }
}
