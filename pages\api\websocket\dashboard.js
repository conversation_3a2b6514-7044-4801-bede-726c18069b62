/**
 * WebSocket Server for Real-time Dashboard Updates
 * Ocean Soul Sparkles - Artist Dashboard Real-time Features
 */

import { Server } from 'socket.io'
import { authenticateAdminRequest } from '@/lib/admin-auth'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

// Store active connections
const activeConnections = new Map()

export default function handler(req, res) {
  // Initialize Socket.IO server if not already done
  if (!res.socket.server.io) {
    console.log('[WebSocket] Initializing Socket.IO server...')
    
    const io = new Server(res.socket.server, {
      path: '/api/websocket/dashboard',
      addTrailingSlash: false,
      cors: {
        origin: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
        methods: ['GET', 'POST']
      }
    })

    // Handle new connections
    io.on('connection', async (socket) => {
      console.log(`[WebSocket] New connection: ${socket.id}`)
      
      try {
        // Extract authentication info from handshake
        const { token, userId, role } = socket.handshake.query
        
        if (!token || !userId || !role) {
          console.error('[WebSocket] Missing authentication parameters')
          socket.emit('error', { message: 'Authentication required' })
          socket.disconnect()
          return
        }

        // Verify authentication
        const authResult = await verifyWebSocketAuth(token, userId, role)
        if (!authResult.authorized) {
          console.error('[WebSocket] Authentication failed:', authResult.error)
          socket.emit('error', { message: 'Authentication failed' })
          socket.disconnect()
          return
        }

        // Store connection info
        const connectionInfo = {
          socket,
          userId,
          role,
          connectedAt: new Date(),
          lastHeartbeat: new Date()
        }
        
        activeConnections.set(socket.id, connectionInfo)
        console.log(`[WebSocket] User ${userId} (${role}) authenticated and connected`)

        // Join user-specific room
        socket.join(`user_${userId}`)
        socket.join(`role_${role}`)

        // Send connection confirmation
        socket.emit('connection', { status: 'connected', userId, role })

        // Handle subscription requests
        socket.on('subscribe', (data) => {
          handleSubscription(socket, data)
        })

        // Handle heartbeat
        socket.on('heartbeat', () => {
          connectionInfo.lastHeartbeat = new Date()
          socket.emit('heartbeat')
        })

        // Handle heartbeat response
        socket.on('heartbeat_response', () => {
          connectionInfo.lastHeartbeat = new Date()
        })

        // Handle refresh requests
        socket.on('refresh_request', async (data) => {
          await handleRefreshRequest(socket, connectionInfo, data)
        })

        // Handle disconnection
        socket.on('disconnect', (reason) => {
          console.log(`[WebSocket] User ${userId} disconnected: ${reason}`)
          activeConnections.delete(socket.id)
        })

      } catch (error) {
        console.error('[WebSocket] Connection setup error:', error)
        socket.emit('error', { message: 'Connection setup failed' })
        socket.disconnect()
      }
    })

    // Set up periodic cleanup of stale connections
    setInterval(() => {
      cleanupStaleConnections(io)
    }, 60000) // Check every minute

    res.socket.server.io = io
    console.log('[WebSocket] Socket.IO server initialized')
  }

  res.end()
}

/**
 * Verify WebSocket authentication
 */
async function verifyWebSocketAuth(token, userId, role) {
  try {
    // Create a mock request object for authentication
    const mockReq = {
      headers: {
        authorization: `Bearer ${token}`
      }
    }

    const authResult = await authenticateAdminRequest(mockReq)
    
    if (!authResult.authorized) {
      return { authorized: false, error: 'Invalid token' }
    }

    // Verify user ID and role match
    if (authResult.user.id !== userId) {
      return { authorized: false, error: 'User ID mismatch' }
    }

    if (!['artist', 'braider', 'admin', 'dev'].includes(role)) {
      return { authorized: false, error: 'Invalid role' }
    }

    return { authorized: true, user: authResult.user, role: authResult.role }
    
  } catch (error) {
    console.error('[WebSocket] Auth verification error:', error)
    return { authorized: false, error: 'Authentication error' }
  }
}

/**
 * Handle subscription requests
 */
function handleSubscription(socket, data) {
  const { dataTypes = [] } = data
  
  console.log(`[WebSocket] User subscribing to: ${dataTypes.join(', ')}`)
  
  // Join specific data type rooms
  dataTypes.forEach(dataType => {
    socket.join(`updates_${dataType}`)
  })
  
  socket.emit('subscription_confirmed', { dataTypes })
}

/**
 * Handle refresh requests
 */
async function handleRefreshRequest(socket, connectionInfo, data) {
  try {
    console.log(`[WebSocket] Refresh request from user ${connectionInfo.userId}`)
    
    // Fetch fresh dashboard data
    const dashboardData = await fetchDashboardData(connectionInfo.userId, connectionInfo.role)
    
    // Send updated data
    socket.emit('dashboard_update', {
      type: 'full_refresh',
      data: dashboardData,
      timestamp: new Date().toISOString()
    })
    
  } catch (error) {
    console.error('[WebSocket] Refresh request error:', error)
    socket.emit('error', { message: 'Failed to refresh data' })
  }
}

/**
 * Fetch dashboard data for user
 */
async function fetchDashboardData(userId, role) {
  try {
    // Get artist profile
    const { data: artistProfile } = await supabase
      .from('artist_profiles')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (!artistProfile) {
      throw new Error('Artist profile not found')
    }

    // Get recent bookings
    const { data: bookings } = await supabase
      .from('bookings')
      .select(`
        *,
        customers:customers(name, email),
        services:services(name, duration, price)
      `)
      .or(`assigned_artist_id.eq.${artistProfile.id},preferred_artist_id.eq.${artistProfile.id}`)
      .gte('start_time', new Date().toISOString())
      .order('start_time', { ascending: true })
      .limit(10)

    // Get today's bookings count
    const today = new Date()
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000)

    const { count: todaysBookings } = await supabase
      .from('bookings')
      .select('*', { count: 'exact', head: true })
      .or(`assigned_artist_id.eq.${artistProfile.id},preferred_artist_id.eq.${artistProfile.id}`)
      .gte('start_time', todayStart.toISOString())
      .lt('start_time', todayEnd.toISOString())

    return {
      profile: artistProfile,
      upcomingBookings: bookings || [],
      todaysBookings: todaysBookings || 0,
      lastUpdated: new Date().toISOString()
    }
    
  } catch (error) {
    console.error('[WebSocket] Error fetching dashboard data:', error)
    throw error
  }
}

/**
 * Clean up stale connections
 */
function cleanupStaleConnections(io) {
  const now = new Date()
  const staleThreshold = 5 * 60 * 1000 // 5 minutes

  activeConnections.forEach((connection, socketId) => {
    const timeSinceHeartbeat = now - connection.lastHeartbeat
    
    if (timeSinceHeartbeat > staleThreshold) {
      console.log(`[WebSocket] Cleaning up stale connection: ${socketId}`)
      connection.socket.disconnect()
      activeConnections.delete(socketId)
    }
  })
}

/**
 * Broadcast update to specific users
 */
export function broadcastToUser(userId, eventType, data) {
  if (res?.socket?.server?.io) {
    res.socket.server.io.to(`user_${userId}`).emit(eventType, data)
  }
}

/**
 * Broadcast update to all users of a specific role
 */
export function broadcastToRole(role, eventType, data) {
  if (res?.socket?.server?.io) {
    res.socket.server.io.to(`role_${role}`).emit(eventType, data)
  }
}
