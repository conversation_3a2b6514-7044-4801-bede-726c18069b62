/* Dynamic Pricing Engine Styles */

.pricingEngine {
  padding: 20px;
  background: #f8fafc;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header h2 {
  color: #1f2937;
  font-size: 28px;
  font-weight: 700;
  margin: 0;
}

.controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.serviceSelect,
.strategySelect {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.serviceSelect:focus,
.strategySelect:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.simulationButton {
  padding: 8px 16px;
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.simulationButton:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.simulationButton.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

/* Loading and Error States */
.loadingContainer,
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer h3 {
  color: #dc2626;
  margin-bottom: 12px;
}

.retryButton {
  padding: 10px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 16px;
}

.retryButton:hover {
  background: #2563eb;
}

/* Recommendations Grid */
.recommendationsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.recommendationCard {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  border-left: 4px solid #3b82f6;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.recommendationCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.recommendationCard.high {
  border-left-color: #dc2626;
}

.recommendationCard.medium {
  border-left-color: #f59e0b;
}

.recommendationCard.low {
  border-left-color: #059669;
}

.recommendationCard h3 {
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.currentPrice,
.recommendedPrice {
  font-size: 16px;
  margin-bottom: 8px;
}

.currentPrice {
  color: #6b7280;
}

.recommendedPrice {
  color: #059669;
  font-weight: 600;
}

.priceChange {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
}

.expectedImpact {
  color: #3b82f6;
  font-size: 14px;
  margin-bottom: 8px;
}

.confidence {
  color: #6b7280;
  font-size: 12px;
  margin-bottom: 16px;
}

.simulateButton {
  width: 100%;
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.simulateButton:hover {
  background: #2563eb;
}

/* Charts Grid */
.chartsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.chartCard {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.chartCard h3 {
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 20px 0;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.chartContainer {
  height: 300px;
  position: relative;
}

/* Insights Section */
.insightsSection {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  margin-bottom: 30px;
}

.insightsSection h3 {
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 20px 0;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.insightsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.insightCard {
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
  background: #f8fafc;
}

.insightCard.opportunity {
  border-left-color: #059669;
  background: #ecfdf5;
}

.insightCard.warning {
  border-left-color: #f59e0b;
  background: #fffbeb;
}

.insightCard.critical {
  border-left-color: #dc2626;
  background: #fef2f2;
}

.insightCard h4 {
  color: #374151;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.insightCard p {
  color: #6b7280;
  font-size: 14px;
  margin: 0 0 12px 0;
  line-height: 1.5;
}

.recommendation {
  background: rgba(59, 130, 246, 0.1);
  padding: 12px;
  border-radius: 6px;
  margin-top: 12px;
  font-size: 14px;
  color: #1e40af;
}

.impact {
  background: rgba(16, 185, 129, 0.1);
  padding: 12px;
  border-radius: 6px;
  margin-top: 8px;
  font-size: 14px;
  color: #047857;
}

/* Elasticity Section */
.elasticitySection {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.elasticitySection h3 {
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 20px 0;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.elasticityCards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.elasticityCard {
  background: #f9fafb;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  text-align: center;
}

.elasticityCard h4 {
  color: #6b7280;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 12px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.elasticityValue {
  color: #1f2937;
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 8px;
}

.elasticityType {
  color: #6b7280;
  font-size: 12px;
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .chartsGrid {
    grid-template-columns: 1fr;
  }
  
  .chartContainer {
    height: 250px;
  }
}

@media (max-width: 768px) {
  .pricingEngine {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .controls {
    justify-content: center;
    flex-wrap: wrap;
  }

  .recommendationsGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .recommendationCard {
    padding: 20px;
  }

  .chartsGrid {
    gap: 16px;
  }

  .chartCard {
    padding: 20px;
  }

  .chartContainer {
    height: 200px;
  }

  .insightsGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .elasticityCards {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .pricingEngine {
    padding: 12px;
  }

  .header h2 {
    font-size: 24px;
  }

  .controls {
    flex-direction: column;
    gap: 8px;
  }

  .serviceSelect,
  .strategySelect,
  .simulationButton {
    width: 100%;
  }

  .chartContainer {
    height: 180px;
  }
}

/* Animation for data updates */
.recommendationCard,
.chartCard,
.insightCard,
.elasticityCard {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus states for accessibility */
.serviceSelect:focus,
.strategySelect:focus,
.simulationButton:focus,
.simulateButton:focus,
.retryButton:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .pricingEngine {
    background: white;
    padding: 0;
  }

  .controls {
    display: none;
  }

  .chartCard,
  .recommendationCard,
  .insightCard,
  .elasticityCard {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #e5e7eb;
  }
}
