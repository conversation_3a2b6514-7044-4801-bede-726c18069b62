/**
 * Calendar Disconnect API Endpoint for Ocean Soul Sparkles
 * Handles disconnection of calendar integrations
 * 
 * Phase 7: Advanced Integrations & Ecosystem
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { integrationRateLimit } from '@/lib/integrations/rate-limiter'
import { RequestV<PERSON>da<PERSON>, AuditLogger } from '@/lib/integrations/security-utils'
import oauthManager from '@/lib/integrations/oauth-manager'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * Calendar Disconnect Handler
 * POST /api/integrations/calendar/disconnect - Disconnect calendar integration
 */
export default async function handler(req, res) {
  // Apply rate limiting
  await new Promise((resolve, reject) => {
    integrationRateLimit(req, res, (error) => {
      if (error) reject(error)
      else resolve()
    })
  })

  const startTime = Date.now()

  try {
    // Validate request method
    RequestValidator.validateEndpointAccess(req, ['POST'])

    // Authenticate user
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    const { user } = authResult
    const userId = user.id

    const { provider } = req.body

    if (!provider) {
      return res.status(400).json({
        error: 'Missing provider',
        message: 'Provider is required'
      })
    }

    // Validate provider
    const validProviders = ['google_calendar', 'outlook', 'apple_calendar']
    if (!validProviders.includes(provider)) {
      return res.status(400).json({
        error: 'Invalid provider',
        message: `Provider must be one of: ${validProviders.join(', ')}`
      })
    }

    // Check if integration exists
    const integrationStatus = await oauthManager.getIntegrationStatus(userId, provider)
    const integration = integrationStatus.find(i => i.provider === provider)

    if (!integration || !integration.connected) {
      return res.status(404).json({
        error: 'Integration not found',
        message: `No active ${provider} integration found`
      })
    }

    // Perform disconnection
    const disconnectionResult = await performDisconnection(userId, provider)

    await AuditLogger.logIntegrationActivity(
      userId,
      provider,
      'integration_disconnected',
      'success',
      disconnectionResult
    )

    await AuditLogger.logApiAccess(
      userId,
      req.url,
      req.method,
      200,
      Date.now() - startTime,
      { action: 'disconnect_calendar', provider }
    )

    return res.status(200).json({
      success: true,
      message: `${provider} integration disconnected successfully`,
      result: disconnectionResult
    })

  } catch (error) {
    console.error('Calendar disconnect error:', error)

    await AuditLogger.logSecurityEvent(
      req.user?.id || null,
      'calendar_disconnect_error',
      {
        error: error.message,
        provider: req.body?.provider,
        userAgent: req.headers['user-agent'],
        ipAddress: req.headers['x-forwarded-for'] || req.connection?.remoteAddress
      },
      'error'
    )

    await AuditLogger.logApiAccess(
      req.user?.id || null,
      req.url,
      req.method,
      500,
      Date.now() - startTime,
      { error: error.message }
    )

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to disconnect calendar integration'
    })
  }
}

/**
 * Perform the disconnection process
 */
async function performDisconnection(userId, provider) {
  const result = {
    credentialsRemoved: false,
    settingsRemoved: false,
    syncStatusCleared: false,
    errors: []
  }

  try {
    // Remove OAuth credentials
    await oauthManager.removeIntegration(userId, provider)
    result.credentialsRemoved = true

  } catch (error) {
    console.error('Failed to remove credentials:', error)
    result.errors.push(`Failed to remove credentials: ${error.message}`)
  }

  try {
    // Remove integration settings
    const { error: settingsError } = await supabase
      .from('integration_settings')
      .delete()
      .eq('user_id', userId)
      .eq('provider', provider)

    if (settingsError) {
      throw settingsError
    }
    result.settingsRemoved = true

  } catch (error) {
    console.error('Failed to remove settings:', error)
    result.errors.push(`Failed to remove settings: ${error.message}`)
  }

  try {
    // Clear sync status
    const { error: syncError } = await supabase
      .from('integration_sync_status')
      .delete()
      .eq('user_id', userId)
      .eq('provider', provider)

    if (syncError) {
      throw syncError
    }
    result.syncStatusCleared = true

  } catch (error) {
    console.error('Failed to clear sync status:', error)
    result.errors.push(`Failed to clear sync status: ${error.message}`)
  }

  try {
    // Update bookings to remove external calendar sync flags
    const { error: bookingsError } = await supabase
      .from('bookings')
      .update({
        external_calendar_synced: false,
        external_calendar_data: null,
        updated_at: new Date().toISOString()
      })
      .eq('customer_id', userId)
      .not('external_calendar_data', 'is', null)

    if (bookingsError) {
      throw bookingsError
    }

  } catch (error) {
    console.error('Failed to update bookings:', error)
    result.errors.push(`Failed to update bookings: ${error.message}`)
  }

  // Log cleanup activity
  try {
    await AuditLogger.logIntegrationActivity(
      userId,
      provider,
      'integration_cleanup_completed',
      result.errors.length === 0 ? 'success' : 'warning',
      {
        result,
        cleanupSteps: {
          credentialsRemoved: result.credentialsRemoved,
          settingsRemoved: result.settingsRemoved,
          syncStatusCleared: result.syncStatusCleared,
          errorCount: result.errors.length
        }
      }
    )

  } catch (error) {
    console.error('Failed to log cleanup activity:', error)
  }

  return result
}

/**
 * API Route Configuration
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
