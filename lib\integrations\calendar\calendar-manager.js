/**
 * Calendar Manager for Ocean Soul Sparkles
 * Universal calendar interface supporting multiple providers
 * 
 * Phase 7: Advanced Integrations & Ecosystem
 */

import GoogleCalendarClient from './google-calendar'
import { AuditLogger } from '../security-utils'
import { getIntegrationSettings, updateIntegrationSettings } from '@/lib/supabase'

/**
 * Supported Calendar Providers
 */
const CALENDAR_PROVIDERS = {
  google_calendar: {
    name: 'Google Calendar',
    clientClass: GoogleCalendarClient,
    features: ['read', 'write', 'delete', 'freebusy', 'conflicts']
  }
  // Future providers can be added here:
  // outlook: { name: 'Outlook Calendar', clientClass: OutlookCalendarClient },
  // apple: { name: 'Apple Calendar', clientClass: AppleCalendarClient }
}

/**
 * Calendar Manager Class
 * Provides unified interface for calendar operations across providers
 */
export class CalendarManager {
  constructor(userId) {
    this.userId = userId
    this.clients = new Map()
  }

  /**
   * Get calendar client for a specific provider
   */
  async getClient(provider) {
    if (!CALENDAR_PROVIDERS[provider]) {
      throw new Error(`Unsupported calendar provider: ${provider}`)
    }

    // Return cached client if available
    if (this.clients.has(provider)) {
      return this.clients.get(provider)
    }

    // Create new client
    const ClientClass = CALENDAR_PROVIDERS[provider].clientClass
    const client = new ClientClass(this.userId)

    // Initialize client
    const initialized = await client.initialize()
    if (!initialized) {
      throw new Error(`Failed to initialize ${provider} client`)
    }

    // Cache client
    this.clients.set(provider, client)
    return client
  }

  /**
   * Get all connected calendar providers for user
   */
  async getConnectedProviders() {
    const settings = await getIntegrationSettings(this.userId)
    
    return settings
      .filter(setting => 
        CALENDAR_PROVIDERS[setting.provider] && 
        setting.enabled
      )
      .map(setting => ({
        provider: setting.provider,
        name: CALENDAR_PROVIDERS[setting.provider].name,
        features: CALENDAR_PROVIDERS[setting.provider].features,
        settings: setting.settings
      }))
  }

  /**
   * Get calendars from all connected providers
   */
  async getAllCalendars() {
    const connectedProviders = await this.getConnectedProviders()
    const allCalendars = []

    for (const providerInfo of connectedProviders) {
      try {
        const client = await this.getClient(providerInfo.provider)
        const calendars = await client.getCalendars()
        
        // Add provider information to each calendar
        const providerCalendars = calendars.map(calendar => ({
          ...calendar,
          provider: providerInfo.provider,
          providerName: providerInfo.name
        }))
        
        allCalendars.push(...providerCalendars)
      } catch (error) {
        console.error(`Failed to get calendars from ${providerInfo.provider}:`, error)
        
        await AuditLogger.logIntegrationActivity(
          this.userId,
          providerInfo.provider,
          'get_calendars_failed',
          'error',
          { error: error.message }
        )
      }
    }

    return allCalendars
  }

  /**
   * Get events from all connected calendars
   */
  async getAllEvents(timeMin = null, timeMax = null, maxResults = 250) {
    const connectedProviders = await this.getConnectedProviders()
    const allEvents = []

    for (const providerInfo of connectedProviders) {
      try {
        const client = await this.getClient(providerInfo.provider)
        const settings = providerInfo.settings || {}
        
        // Get calendars to sync (or default to primary)
        const calendarsToSync = settings.calendarsToSync || ['primary']
        
        for (const calendarId of calendarsToSync) {
          const events = await client.getEvents(calendarId, timeMin, timeMax, maxResults)
          
          // Add provider and calendar information to each event
          const providerEvents = events.map(event => ({
            ...event,
            provider: providerInfo.provider,
            providerName: providerInfo.name,
            calendarId
          }))
          
          allEvents.push(...providerEvents)
        }
      } catch (error) {
        console.error(`Failed to get events from ${providerInfo.provider}:`, error)
        
        await AuditLogger.logIntegrationActivity(
          this.userId,
          providerInfo.provider,
          'get_events_failed',
          'error',
          { error: error.message }
        )
      }
    }

    // Sort events by start time
    return allEvents.sort((a, b) => new Date(a.start) - new Date(b.start))
  }

  /**
   * Create event in specified calendar
   */
  async createEvent(provider, calendarId, eventData) {
    const client = await this.getClient(provider)
    return await client.createEvent(calendarId, eventData)
  }

  /**
   * Update event in specified calendar
   */
  async updateEvent(provider, calendarId, eventId, eventData) {
    const client = await this.getClient(provider)
    return await client.updateEvent(calendarId, eventId, eventData)
  }

  /**
   * Delete event from specified calendar
   */
  async deleteEvent(provider, calendarId, eventId) {
    const client = await this.getClient(provider)
    return await client.deleteEvent(calendarId, eventId)
  }

  /**
   * Check for conflicts across all calendars
   */
  async checkConflicts(startTime, endTime, excludeEvents = []) {
    const connectedProviders = await this.getConnectedProviders()
    const allConflicts = []

    for (const providerInfo of connectedProviders) {
      try {
        const client = await this.getClient(providerInfo.provider)
        const settings = providerInfo.settings || {}
        const calendarsToCheck = settings.calendarsToSync || ['primary']
        
        for (const calendarId of calendarsToCheck) {
          const conflicts = await client.checkConflicts(
            calendarId, 
            startTime, 
            endTime,
            excludeEvents.find(e => e.provider === providerInfo.provider)?.eventId
          )
          
          // Add provider information to conflicts
          const providerConflicts = conflicts.map(conflict => ({
            ...conflict,
            provider: providerInfo.provider,
            providerName: providerInfo.name,
            calendarId
          }))
          
          allConflicts.push(...providerConflicts)
        }
      } catch (error) {
        console.error(`Failed to check conflicts in ${providerInfo.provider}:`, error)
      }
    }

    return allConflicts
  }

  /**
   * Get free/busy information across all calendars
   */
  async getFreeBusy(timeMin, timeMax) {
    const connectedProviders = await this.getConnectedProviders()
    const freeBusyData = {}

    for (const providerInfo of connectedProviders) {
      try {
        const client = await this.getClient(providerInfo.provider)
        const settings = providerInfo.settings || {}
        const calendarsToCheck = settings.calendarsToSync || ['primary']
        
        if (client.getFreeBusy) {
          const freeBusy = await client.getFreeBusy(calendarsToCheck, timeMin, timeMax)
          freeBusyData[providerInfo.provider] = freeBusy
        }
      } catch (error) {
        console.error(`Failed to get free/busy from ${providerInfo.provider}:`, error)
      }
    }

    return freeBusyData
  }

  /**
   * Sync Ocean Soul Sparkles booking to external calendars
   */
  async syncBookingToCalendars(booking, action = 'create') {
    const connectedProviders = await this.getConnectedProviders()
    const syncResults = []

    // Convert booking to calendar event format
    const eventData = this.formatBookingAsEvent(booking)

    for (const providerInfo of connectedProviders) {
      try {
        const settings = providerInfo.settings || {}
        
        // Check if auto-sync is enabled
        if (!settings.autoSync) {
          continue
        }

        const client = await this.getClient(providerInfo.provider)
        const calendarId = settings.defaultCalendar || 'primary'
        
        let result
        switch (action) {
          case 'create':
            result = await client.createEvent(calendarId, eventData)
            break
          case 'update':
            if (booking.externalEventId) {
              result = await client.updateEvent(calendarId, booking.externalEventId, eventData)
            }
            break
          case 'delete':
            if (booking.externalEventId) {
              result = await client.deleteEvent(calendarId, booking.externalEventId)
            }
            break
        }

        syncResults.push({
          provider: providerInfo.provider,
          success: true,
          eventId: result?.id,
          calendarId
        })

        await AuditLogger.logIntegrationActivity(
          this.userId,
          providerInfo.provider,
          `booking_sync_${action}`,
          'success',
          { 
            bookingId: booking.id,
            eventId: result?.id,
            calendarId
          }
        )

      } catch (error) {
        console.error(`Failed to sync booking to ${providerInfo.provider}:`, error)
        
        syncResults.push({
          provider: providerInfo.provider,
          success: false,
          error: error.message
        })

        await AuditLogger.logIntegrationActivity(
          this.userId,
          providerInfo.provider,
          `booking_sync_${action}_failed`,
          'error',
          { 
            bookingId: booking.id,
            error: error.message
          }
        )
      }
    }

    return syncResults
  }

  /**
   * Format Ocean Soul Sparkles booking as calendar event
   */
  formatBookingAsEvent(booking) {
    return {
      summary: `${booking.service_name} - ${booking.customer_name}`,
      description: `Ocean Soul Sparkles Booking\n\nService: ${booking.service_name}\nCustomer: ${booking.customer_name}\nArtist: ${booking.artist_name || 'TBD'}\n\nBooking ID: ${booking.id}`,
      start: booking.booking_date,
      end: booking.end_time || booking.booking_date, // Calculate end time based on service duration
      location: booking.location || 'Ocean Soul Sparkles Studio',
      attendees: [
        {
          email: booking.customer_email,
          name: booking.customer_name
        }
      ],
      reminders: [
        { method: 'email', minutes: 60 },
        { method: 'popup', minutes: 15 }
      ]
    }
  }

  /**
   * Test connections to all calendar providers
   */
  async testAllConnections() {
    const connectedProviders = await this.getConnectedProviders()
    const testResults = []

    for (const providerInfo of connectedProviders) {
      try {
        const client = await this.getClient(providerInfo.provider)
        const result = await client.testConnection()
        
        testResults.push({
          provider: providerInfo.provider,
          name: providerInfo.name,
          ...result
        })
      } catch (error) {
        testResults.push({
          provider: providerInfo.provider,
          name: providerInfo.name,
          success: false,
          error: error.message
        })
      }
    }

    return testResults
  }

  /**
   * Get available calendar providers
   */
  static getAvailableProviders() {
    return Object.keys(CALENDAR_PROVIDERS).map(key => ({
      id: key,
      name: CALENDAR_PROVIDERS[key].name,
      features: CALENDAR_PROVIDERS[key].features
    }))
  }
}

export default CalendarManager
