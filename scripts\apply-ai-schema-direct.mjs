/**
 * Apply AI Database Schema Directly via Supabase Client
 * Ocean Soul Sparkles - Phase 6 AI Schema Application
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });
dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Error: Supabase credentials not found');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🚀 Applying AI Database Schema Directly...\n');

/**
 * Execute SQL using Supabase REST API directly
 */
async function executeSQLDirect(sql, description) {
  try {
    console.log(`⚡ ${description}...`);
    
    const response = await fetch(`${supabaseUrl}/rest/v1/rpc/execute_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'apikey': supabaseServiceKey
      },
      body: JSON.stringify({ sql })
    });

    if (!response.ok) {
      // Try alternative approach using pg_stat_statements or direct table creation
      console.log(`⚠️ execute_sql not available, using alternative approach...`);
      return await executeViaSupabaseClient(sql, description);
    }

    const result = await response.json();
    console.log(`✅ ${description} completed`);
    return result;
    
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    return null;
  }
}

/**
 * Execute via Supabase client for table operations
 */
async function executeViaSupabaseClient(sql, description) {
  // For table creation, we'll use a different approach
  if (sql.includes('CREATE TABLE')) {
    console.log(`⚠️ Cannot execute CREATE TABLE via client, skipping: ${description}`);
    return null;
  }
  
  // For column additions, we'll use ALTER TABLE approach
  if (sql.includes('ALTER TABLE') && sql.includes('ADD COLUMN')) {
    console.log(`⚠️ Cannot execute ALTER TABLE via client, skipping: ${description}`);
    return null;
  }
  
  return null;
}

/**
 * Apply AI schema step by step
 */
async function applyAISchema() {
  console.log('📊 Creating AI Tables...\n');
  
  // 1. Add columns to existing tables first
  const alterStatements = [
    {
      sql: `ALTER TABLE artist_profiles ADD COLUMN IF NOT EXISTS ai_optimization_enabled BOOLEAN DEFAULT true;`,
      description: 'Add ai_optimization_enabled to artist_profiles'
    },
    {
      sql: `ALTER TABLE artist_profiles ADD COLUMN IF NOT EXISTS last_ai_analysis TIMESTAMP;`,
      description: 'Add last_ai_analysis to artist_profiles'
    },
    {
      sql: `ALTER TABLE artist_profiles ADD COLUMN IF NOT EXISTS ai_performance_score DECIMAL(3,2);`,
      description: 'Add ai_performance_score to artist_profiles'
    },
    {
      sql: `ALTER TABLE bookings ADD COLUMN IF NOT EXISTS ai_optimized BOOLEAN DEFAULT false;`,
      description: 'Add ai_optimized to bookings'
    },
    {
      sql: `ALTER TABLE bookings ADD COLUMN IF NOT EXISTS ai_confidence_score DECIMAL(3,2);`,
      description: 'Add ai_confidence_score to bookings'
    },
    {
      sql: `ALTER TABLE bookings ADD COLUMN IF NOT EXISTS travel_time_minutes INTEGER;`,
      description: 'Add travel_time_minutes to bookings'
    },
    {
      sql: `ALTER TABLE bookings ADD COLUMN IF NOT EXISTS ai_optimization_applied_at TIMESTAMP;`,
      description: 'Add ai_optimization_applied_at to bookings'
    },
    {
      sql: `ALTER TABLE bookings ADD COLUMN IF NOT EXISTS original_start_time TIMESTAMP;`,
      description: 'Add original_start_time to bookings'
    }
  ];

  // Execute ALTER statements
  for (const statement of alterStatements) {
    await executeSQLDirect(statement.sql, statement.description);
  }

  // 2. Create AI tables
  const createTableStatements = [
    {
      sql: `
        CREATE TABLE IF NOT EXISTS ai_insights_cache (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          cache_key VARCHAR(255) UNIQUE NOT NULL,
          insights_data JSONB NOT NULL,
          insight_type VARCHAR(50) DEFAULT 'daily',
          target_date DATE NOT NULL,
          confidence_level DECIMAL(3,2) DEFAULT 0.5,
          created_at TIMESTAMP DEFAULT NOW(),
          expires_at TIMESTAMP NOT NULL,
          CONSTRAINT valid_confidence_level CHECK (confidence_level >= 0 AND confidence_level <= 1),
          CONSTRAINT valid_insight_type CHECK (insight_type IN ('daily', 'weekly', 'monthly'))
        );
      `,
      description: 'Create ai_insights_cache table'
    },
    {
      sql: `
        CREATE TABLE IF NOT EXISTS ai_recommendations (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          type VARCHAR(50) NOT NULL,
          category VARCHAR(50) NOT NULL,
          priority VARCHAR(20) DEFAULT 'medium',
          target_id UUID,
          target_type VARCHAR(50),
          recommendation_data JSONB NOT NULL,
          confidence_score DECIMAL(3,2) DEFAULT 0.5,
          status VARCHAR(20) DEFAULT 'pending',
          created_at TIMESTAMP DEFAULT NOW(),
          applied_at TIMESTAMP,
          dismissed_at TIMESTAMP,
          created_by UUID,
          CONSTRAINT valid_priority CHECK (priority IN ('low', 'medium', 'high')),
          CONSTRAINT valid_status CHECK (status IN ('pending', 'applied', 'dismissed', 'expired')),
          CONSTRAINT valid_confidence CHECK (confidence_score >= 0 AND confidence_score <= 1)
        );
      `,
      description: 'Create ai_recommendations table'
    },
    {
      sql: `
        CREATE TABLE IF NOT EXISTS ai_compatibility_scores (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          customer_id UUID NOT NULL,
          artist_id UUID NOT NULL,
          compatibility_score DECIMAL(4,3) NOT NULL,
          confidence_level DECIMAL(3,2) NOT NULL,
          factors JSONB,
          last_calculated TIMESTAMP DEFAULT NOW(),
          calculation_version VARCHAR(10) DEFAULT '1.0',
          UNIQUE(customer_id, artist_id),
          CONSTRAINT valid_compatibility_score CHECK (compatibility_score >= 0 AND compatibility_score <= 1),
          CONSTRAINT valid_confidence_level CHECK (confidence_level >= 0 AND confidence_level <= 1)
        );
      `,
      description: 'Create ai_compatibility_scores table'
    },
    {
      sql: `
        CREATE TABLE IF NOT EXISTS ai_travel_time_cache (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          origin_location TEXT NOT NULL,
          destination_location TEXT NOT NULL,
          travel_time_minutes INTEGER NOT NULL,
          distance_meters INTEGER,
          traffic_factor DECIMAL(3,2) DEFAULT 1.0,
          departure_time_slot VARCHAR(10),
          day_of_week INTEGER,
          cached_at TIMESTAMP DEFAULT NOW(),
          expires_at TIMESTAMP NOT NULL,
          source VARCHAR(20) DEFAULT 'google_maps',
          UNIQUE(origin_location, destination_location, departure_time_slot, day_of_week),
          CONSTRAINT valid_day_of_week CHECK (day_of_week >= 0 AND day_of_week <= 6),
          CONSTRAINT valid_travel_time CHECK (travel_time_minutes >= 0),
          CONSTRAINT valid_distance CHECK (distance_meters >= 0)
        );
      `,
      description: 'Create ai_travel_time_cache table'
    },
    {
      sql: `
        CREATE TABLE IF NOT EXISTS ai_optimization_history (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          artist_id UUID NOT NULL,
          optimization_date DATE NOT NULL,
          optimization_type VARCHAR(50) NOT NULL,
          original_schedule JSONB,
          optimized_schedule JSONB,
          improvements JSONB,
          applied BOOLEAN DEFAULT false,
          created_at TIMESTAMP DEFAULT NOW(),
          applied_at TIMESTAMP,
          created_by UUID,
          CONSTRAINT valid_optimization_type CHECK (optimization_type IN ('schedule', 'travel', 'conflict_resolution', 'efficiency'))
        );
      `,
      description: 'Create ai_optimization_history table'
    }
  ];

  // Execute CREATE TABLE statements
  for (const statement of createTableStatements) {
    await executeSQLDirect(statement.sql, statement.description);
  }

  // 3. Create indexes
  const indexStatements = [
    {
      sql: `CREATE INDEX IF NOT EXISTS idx_ai_insights_cache_key ON ai_insights_cache(cache_key);`,
      description: 'Create index on ai_insights_cache.cache_key'
    },
    {
      sql: `CREATE INDEX IF NOT EXISTS idx_ai_insights_cache_date ON ai_insights_cache(target_date);`,
      description: 'Create index on ai_insights_cache.target_date'
    },
    {
      sql: `CREATE INDEX IF NOT EXISTS idx_ai_insights_cache_expires ON ai_insights_cache(expires_at);`,
      description: 'Create index on ai_insights_cache.expires_at'
    },
    {
      sql: `CREATE INDEX IF NOT EXISTS idx_ai_recommendations_type ON ai_recommendations(type);`,
      description: 'Create index on ai_recommendations.type'
    },
    {
      sql: `CREATE INDEX IF NOT EXISTS idx_ai_recommendations_status ON ai_recommendations(status);`,
      description: 'Create index on ai_recommendations.status'
    },
    {
      sql: `CREATE INDEX IF NOT EXISTS idx_ai_compatibility_customer ON ai_compatibility_scores(customer_id);`,
      description: 'Create index on ai_compatibility_scores.customer_id'
    },
    {
      sql: `CREATE INDEX IF NOT EXISTS idx_ai_compatibility_artist ON ai_compatibility_scores(artist_id);`,
      description: 'Create index on ai_compatibility_scores.artist_id'
    },
    {
      sql: `CREATE INDEX IF NOT EXISTS idx_ai_travel_cache_locations ON ai_travel_time_cache(origin_location, destination_location);`,
      description: 'Create index on ai_travel_time_cache locations'
    },
    {
      sql: `CREATE INDEX IF NOT EXISTS idx_ai_travel_cache_expires ON ai_travel_time_cache(expires_at);`,
      description: 'Create index on ai_travel_time_cache.expires_at'
    }
  ];

  // Execute INDEX statements
  for (const statement of indexStatements) {
    await executeSQLDirect(statement.sql, statement.description);
  }

  console.log('\n🎯 AI Schema Application Complete!');
  console.log('⚠️ Note: Some operations may require direct SQL execution in Supabase dashboard');
  console.log('📋 Please run the verification script again to check results');
}

/**
 * Main execution
 */
async function main() {
  try {
    // Test connection
    const { data, error } = await supabase.from('admin_settings').select('setting_key').limit(1);
    if (error) {
      console.error('❌ Database connection failed:', error.message);
      return;
    }
    console.log('✅ Database connection successful\n');
    
    await applyAISchema();
    
  } catch (error) {
    console.error('❌ Schema application failed:', error);
    process.exit(1);
  }
}

main();
