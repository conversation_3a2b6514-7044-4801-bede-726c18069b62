/**
 * Social Media Analytics API Endpoint for Ocean Soul Sparkles
 * Provides analytics data from connected social media platforms
 * 
 * Phase 7.3: Social Media Integration Layer
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { integrationRateLimit } from '@/lib/integrations/rate-limiter'
import { RequestValidator, AuditLogger } from '@/lib/integrations/security-utils'
import SocialMediaManager from '@/lib/integrations/social/social-manager'

/**
 * Social Media Analytics Handler
 * GET /api/integrations/social/analytics - Get analytics from connected social media accounts
 */
export default async function handler(req, res) {
  // Apply rate limiting
  await new Promise((resolve, reject) => {
    integrationRateLimit(req, res, (error) => {
      if (error) reject(error)
      else resolve()
    })
  })

  const startTime = Date.now()

  try {
    // Validate request method
    RequestValidator.validateEndpointAccess(req, ['GET'])

    // Authenticate user
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    const { user } = authResult
    const userId = user.id

    // Get query parameters
    const { period = 'day', provider } = req.query

    // Validate period
    const validPeriods = ['day', 'week', 'days_28']
    if (!validPeriods.includes(period)) {
      return res.status(400).json({
        error: 'Invalid period',
        message: `Period must be one of: ${validPeriods.join(', ')}`
      })
    }

    // Initialize social media manager
    const socialManager = new SocialMediaManager(userId)

    let analytics
    if (provider) {
      // Get analytics for specific provider
      try {
        const client = await socialManager.getClient(provider)
        const insights = await client.getInsights(null, period)
        
        analytics = {
          [provider]: {
            provider,
            providerName: getProviderName(provider),
            insights,
            period
          }
        }
      } catch (error) {
        analytics = {
          [provider]: {
            provider,
            providerName: getProviderName(provider),
            error: error.message,
            period
          }
        }
      }
    } else {
      // Get analytics from all connected providers
      analytics = await socialManager.getAllAnalytics(period)
    }

    // Calculate summary statistics
    const summary = calculateSummaryStats(analytics)

    await AuditLogger.logApiAccess(
      userId,
      req.url,
      req.method,
      200,
      Date.now() - startTime,
      { 
        action: 'get_social_analytics',
        period,
        provider,
        providerCount: Object.keys(analytics).length
      }
    )

    return res.status(200).json({
      success: true,
      analytics,
      summary,
      period
    })

  } catch (error) {
    console.error('Social media analytics error:', error)

    await AuditLogger.logApiAccess(
      req.user?.id || null,
      req.url,
      req.method,
      500,
      Date.now() - startTime,
      { error: error.message }
    )

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get social media analytics'
    })
  }
}

/**
 * Get provider display name
 */
function getProviderName(provider) {
  const names = {
    instagram_business: 'Instagram Business',
    facebook_business: 'Facebook Business',
    tiktok: 'TikTok Business',
    linkedin: 'LinkedIn Business'
  }
  return names[provider] || provider
}

/**
 * Calculate summary statistics across all providers
 */
function calculateSummaryStats(analytics) {
  const summary = {
    totalReach: 0,
    totalImpressions: 0,
    totalEngagement: 0,
    totalFollowers: 0,
    averageEngagementRate: 0,
    providerCount: 0,
    errorCount: 0
  }

  let totalEngagementRate = 0
  let engagementRateCount = 0

  for (const [provider, data] of Object.entries(analytics)) {
    if (data.error) {
      summary.errorCount++
      continue
    }

    summary.providerCount++

    if (data.insights) {
      // Instagram metrics
      if (data.insights.reach) {
        summary.totalReach += data.insights.reach.value || 0
      }
      if (data.insights.impressions) {
        summary.totalImpressions += data.insights.impressions.value || 0
      }
      if (data.insights.engagement) {
        summary.totalEngagement += data.insights.engagement.value || 0
      }

      // Facebook metrics
      if (data.insights.page_reach) {
        summary.totalReach += data.insights.page_reach.value || 0
      }
      if (data.insights.page_impressions) {
        summary.totalImpressions += data.insights.page_impressions.value || 0
      }
      if (data.insights.page_post_engagements) {
        summary.totalEngagement += data.insights.page_post_engagements.value || 0
      }

      // Calculate engagement rate if we have both engagement and reach/impressions
      const engagement = data.insights.engagement?.value || data.insights.page_post_engagements?.value || 0
      const reach = data.insights.reach?.value || data.insights.page_reach?.value || 0
      const impressions = data.insights.impressions?.value || data.insights.page_impressions?.value || 0
      
      if (engagement > 0 && (reach > 0 || impressions > 0)) {
        const base = reach > 0 ? reach : impressions
        const engagementRate = (engagement / base) * 100
        totalEngagementRate += engagementRate
        engagementRateCount++
      }
    }
  }

  // Calculate average engagement rate
  if (engagementRateCount > 0) {
    summary.averageEngagementRate = totalEngagementRate / engagementRateCount
  }

  return summary
}

/**
 * API Route Configuration
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
