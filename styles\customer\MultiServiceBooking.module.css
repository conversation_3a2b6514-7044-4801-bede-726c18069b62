/* Multi-Service Booking Styles - Phase 8: Advanced Customer Experience */

.multiServiceBooking {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* Progress Indicator */
.progressIndicator {
  margin-bottom: 3rem;
}

.progressSteps {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  position: relative;
}

.progressSteps::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: #e9ecef;
  z-index: 1;
}

.progressStep {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  z-index: 2;
  background: white;
  padding: 0 1rem;
}

.stepNumber {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e9ecef;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s ease;
}

.progressStep.active .stepNumber {
  background: #4ECDC4;
  color: white;
}

.progressStep.completed .stepNumber {
  background: #28a745;
  color: white;
}

.stepLabel {
  font-size: 0.9rem;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.3s ease;
}

.progressStep.active .stepLabel,
.progressStep.completed .stepLabel {
  color: #333;
}

/* Step Content */
.stepContent {
  min-height: 500px;
  margin-bottom: 2rem;
}

.stepTitle {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.5rem;
  text-align: center;
}

.stepDescription {
  font-size: 1.1rem;
  color: #666;
  text-align: center;
  margin-bottom: 2rem;
}

.error {
  background: #fee;
  border: 1px solid #fcc;
  color: #c33;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  text-align: center;
}

/* Service Selection */
.serviceGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.serviceCard {
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.serviceCard:hover {
  border-color: #4ECDC4;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(78, 205, 196, 0.2);
}

.serviceCard.selected {
  border-color: #4ECDC4;
  background: rgba(78, 205, 196, 0.05);
}

.serviceHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.serviceName {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.servicePrice {
  font-size: 1.1rem;
  font-weight: 700;
  color: #4ECDC4;
}

.serviceDescription {
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.serviceMeta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.serviceDuration,
.serviceCategory {
  font-size: 0.9rem;
  color: #666;
  background: #f8f9fa;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
}

.serviceSelection {
  text-align: center;
}

.selectedIcon {
  color: #28a745;
  font-weight: 600;
}

.selectIcon {
  color: #4ECDC4;
  font-weight: 600;
}

/* Selection Summary */
.selectionSummary {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 2rem;
}

.selectionSummary h3 {
  margin-bottom: 1rem;
  color: #333;
}

.selectedList {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.selectedItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e9ecef;
}

.selectedItem:last-child {
  border-bottom: none;
}

.subtotal {
  text-align: right;
  padding-top: 1rem;
  border-top: 2px solid #e9ecef;
}

/* Date Selection */
.dateSelection {
  margin-bottom: 2rem;
}

.dateSelection label {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.dateInput {
  width: 100%;
  max-width: 300px;
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.dateInput:focus {
  outline: none;
  border-color: #4ECDC4;
}

/* Service Scheduling */
.serviceScheduling {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.serviceScheduleCard {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
}

.serviceScheduleCard h3 {
  margin-bottom: 0.5rem;
  color: #333;
}

.serviceScheduleCard p {
  color: #666;
  margin-bottom: 1rem;
}

.timeSlots {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.timeSlot {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.timeSlot:hover {
  border-color: #4ECDC4;
  transform: translateY(-1px);
}

.timeSlot.selected {
  border-color: #4ECDC4;
  background: rgba(78, 205, 196, 0.1);
}

.slotTime {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.slotArtist {
  font-size: 0.9rem;
  color: #666;
}

/* Review Section */
.reviewSummary {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.bookingDetails,
.servicesReview,
.pricingSummary {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
}

.bookingDetails h3,
.servicesReview h3 {
  margin-bottom: 1rem;
  color: #333;
}

.bookingDetails p {
  margin-bottom: 0.5rem;
  color: #666;
}

.serviceReviewItem {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem 0;
  border-bottom: 1px solid #e9ecef;
}

.serviceReviewItem:last-child {
  border-bottom: none;
}

.serviceInfo h4 {
  margin-bottom: 0.5rem;
  color: #333;
}

.serviceInfo p {
  margin-bottom: 0.25rem;
  color: #666;
  font-size: 0.9rem;
}

.servicePrice {
  font-weight: 600;
  color: #4ECDC4;
}

.pricingRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.pricingRow:last-child {
  border-top: 2px solid #e9ecef;
  padding-top: 1rem;
  margin-top: 1rem;
}

/* Navigation */
.navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 2px solid #e9ecef;
}

.navigationRight {
  display: flex;
  gap: 1rem;
}

.backButton,
.cancelButton,
.nextButton {
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.backButton {
  background: #6c757d;
  color: white;
}

.backButton:hover {
  background: #5a6268;
}

.cancelButton {
  background: transparent;
  color: #dc3545;
  border: 2px solid #dc3545;
}

.cancelButton:hover {
  background: #dc3545;
  color: white;
}

.nextButton {
  background: #4ECDC4;
  color: white;
}

.nextButton:hover {
  background: #44A08D;
}

.nextButton:disabled {
  background: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .multiServiceBooking {
    padding: 1rem;
    margin: 1rem;
  }

  .progressSteps {
    gap: 1rem;
  }

  .stepNumber {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
  }

  .stepLabel {
    font-size: 0.8rem;
  }

  .stepTitle {
    font-size: 1.5rem;
  }

  .serviceGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .timeSlots {
    grid-template-columns: 1fr;
  }

  .navigation {
    flex-direction: column;
    gap: 1rem;
  }

  .navigationRight {
    width: 100%;
    justify-content: center;
  }

  .backButton,
  .cancelButton,
  .nextButton {
    flex: 1;
    min-width: 120px;
  }
}

@media (max-width: 480px) {
  .progressSteps {
    gap: 0.5rem;
  }

  .progressStep {
    padding: 0 0.5rem;
  }

  .stepLabel {
    display: none;
  }

  .serviceHeader {
    flex-direction: column;
    gap: 0.5rem;
  }

  .serviceMeta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .serviceReviewItem {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }
}
