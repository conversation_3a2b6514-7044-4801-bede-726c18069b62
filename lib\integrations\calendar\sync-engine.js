/**
 * Calendar Synchronization Engine for Ocean Soul Sparkles
 * Handles two-way synchronization between Ocean Soul Sparkles and external calendars
 * 
 * Phase 7: Advanced Integrations & Ecosystem
 */

import CalendarManager from './calendar-manager'
import { AuditLogger } from '../security-utils'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * Calendar Sync Engine Class
 */
export class CalendarSyncEngine {
  constructor(userId) {
    this.userId = userId
    this.calendarManager = new CalendarManager(userId)
  }

  /**
   * Perform full synchronization
   */
  async performFullSync() {
    const syncId = `sync_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`
    
    try {
      await AuditLogger.logIntegrationActivity(
        this.userId,
        'calendar_sync',
        'full_sync_started',
        'info',
        { syncId }
      )

      // Get sync settings
      const syncSettings = await this.getSyncSettings()
      
      const results = {
        syncId,
        startTime: new Date().toISOString(),
        bookingsToCalendar: { success: 0, failed: 0, skipped: 0 },
        calendarToBookings: { success: 0, failed: 0, skipped: 0 },
        conflicts: [],
        errors: []
      }

      // Sync Ocean Soul Sparkles bookings to external calendars
      if (syncSettings.syncBookingsToCalendar) {
        const bookingSyncResults = await this.syncBookingsToCalendars()
        results.bookingsToCalendar = bookingSyncResults
      }

      // Sync external calendar events to Ocean Soul Sparkles (if enabled)
      if (syncSettings.syncCalendarToBookings) {
        const calendarSyncResults = await this.syncCalendarsToBookings()
        results.calendarToBookings = calendarSyncResults
      }

      // Check for conflicts
      const conflicts = await this.detectConflicts()
      results.conflicts = conflicts

      results.endTime = new Date().toISOString()
      results.duration = new Date(results.endTime) - new Date(results.startTime)

      await AuditLogger.logIntegrationActivity(
        this.userId,
        'calendar_sync',
        'full_sync_completed',
        'success',
        results
      )

      return results

    } catch (error) {
      console.error('Full sync failed:', error)
      
      await AuditLogger.logIntegrationActivity(
        this.userId,
        'calendar_sync',
        'full_sync_failed',
        'error',
        { syncId, error: error.message }
      )

      throw error
    }
  }

  /**
   * Sync Ocean Soul Sparkles bookings to external calendars
   */
  async syncBookingsToCalendars() {
    const results = { success: 0, failed: 0, skipped: 0, details: [] }

    try {
      // Get recent bookings that need syncing
      const { data: bookings, error } = await supabase
        .from('bookings')
        .select(`
          *,
          customers(name, email),
          services(name, duration),
          user_profiles(full_name)
        `)
        .eq('customer_id', this.userId) // Only sync user's own bookings
        .gte('booking_date', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // Last 30 days
        .order('booking_date', { ascending: false })

      if (error) {
        throw new Error(`Failed to fetch bookings: ${error.message}`)
      }

      for (const booking of bookings) {
        try {
          // Check if booking already synced
          if (booking.external_calendar_synced) {
            results.skipped++
            continue
          }

          // Sync to all connected calendars
          const syncResults = await this.calendarManager.syncBookingToCalendars(booking, 'create')
          
          // Update booking with sync status
          const successfulSyncs = syncResults.filter(r => r.success)
          if (successfulSyncs.length > 0) {
            await supabase
              .from('bookings')
              .update({
                external_calendar_synced: true,
                external_calendar_data: syncResults
              })
              .eq('id', booking.id)

            results.success++
          } else {
            results.failed++
          }

          results.details.push({
            bookingId: booking.id,
            syncResults
          })

        } catch (error) {
          console.error(`Failed to sync booking ${booking.id}:`, error)
          results.failed++
          results.details.push({
            bookingId: booking.id,
            error: error.message
          })
        }
      }

    } catch (error) {
      console.error('Failed to sync bookings to calendars:', error)
      throw error
    }

    return results
  }

  /**
   * Sync external calendar events to Ocean Soul Sparkles
   */
  async syncCalendarsToBookings() {
    const results = { success: 0, failed: 0, skipped: 0, details: [] }

    try {
      // Get events from external calendars
      const timeMin = new Date()
      const timeMax = new Date(Date.now() + 90 * 24 * 60 * 60 * 1000) // Next 90 days
      
      const externalEvents = await this.calendarManager.getAllEvents(timeMin, timeMax)
      
      for (const event of externalEvents) {
        try {
          // Skip events that are already Ocean Soul Sparkles bookings
          if (event.description?.includes('Ocean Soul Sparkles Booking')) {
            results.skipped++
            continue
          }

          // Check if event should be imported based on keywords or settings
          if (!this.shouldImportEvent(event)) {
            results.skipped++
            continue
          }

          // Check if event already exists in our system
          const existingBooking = await this.findExistingBooking(event)
          if (existingBooking) {
            results.skipped++
            continue
          }

          // Create booking from calendar event
          const booking = await this.createBookingFromEvent(event)
          
          if (booking) {
            results.success++
            results.details.push({
              eventId: event.id,
              bookingId: booking.id,
              action: 'created'
            })
          } else {
            results.failed++
          }

        } catch (error) {
          console.error(`Failed to import event ${event.id}:`, error)
          results.failed++
          results.details.push({
            eventId: event.id,
            error: error.message
          })
        }
      }

    } catch (error) {
      console.error('Failed to sync calendars to bookings:', error)
      throw error
    }

    return results
  }

  /**
   * Detect conflicts between Ocean Soul Sparkles bookings and external events
   */
  async detectConflicts() {
    const conflicts = []

    try {
      // Get upcoming bookings
      const { data: bookings, error } = await supabase
        .from('bookings')
        .select('*')
        .eq('customer_id', this.userId)
        .gte('booking_date', new Date().toISOString())
        .order('booking_date', { ascending: true })

      if (error) {
        throw new Error(`Failed to fetch bookings: ${error.message}`)
      }

      for (const booking of bookings) {
        try {
          // Calculate booking end time
          const startTime = new Date(booking.booking_date)
          const endTime = new Date(startTime.getTime() + (booking.duration || 60) * 60000)

          // Check for conflicts in external calendars
          const externalConflicts = await this.calendarManager.checkConflicts(
            startTime,
            endTime,
            [{ provider: 'ocean_soul_sparkles', eventId: booking.id }]
          )

          if (externalConflicts.length > 0) {
            conflicts.push({
              bookingId: booking.id,
              bookingDate: booking.booking_date,
              conflicts: externalConflicts
            })
          }

        } catch (error) {
          console.error(`Failed to check conflicts for booking ${booking.id}:`, error)
        }
      }

    } catch (error) {
      console.error('Failed to detect conflicts:', error)
    }

    return conflicts
  }

  /**
   * Get sync settings for the user
   */
  async getSyncSettings() {
    const { data: settings, error } = await supabase
      .from('integration_settings')
      .select('settings')
      .eq('user_id', this.userId)
      .eq('provider', 'calendar_sync')
      .single()

    if (error || !settings) {
      // Return default settings
      return {
        syncBookingsToCalendar: true,
        syncCalendarToBookings: false,
        conflictResolution: 'manual',
        syncFrequency: 'hourly',
        importKeywords: ['appointment', 'meeting', 'booking']
      }
    }

    return settings.settings
  }

  /**
   * Check if external event should be imported
   */
  shouldImportEvent(event) {
    const settings = this.getSyncSettings()
    const keywords = settings.importKeywords || []
    
    // Check if event summary or description contains import keywords
    const eventText = `${event.summary} ${event.description}`.toLowerCase()
    
    return keywords.some(keyword => eventText.includes(keyword.toLowerCase()))
  }

  /**
   * Find existing booking that matches external event
   */
  async findExistingBooking(event) {
    const { data: booking, error } = await supabase
      .from('bookings')
      .select('*')
      .eq('customer_id', this.userId)
      .eq('booking_date', event.start)
      .single()

    return error ? null : booking
  }

  /**
   * Create Ocean Soul Sparkles booking from external calendar event
   */
  async createBookingFromEvent(event) {
    try {
      // This would need to be implemented based on business logic
      // For now, we'll just log the attempt
      await AuditLogger.logIntegrationActivity(
        this.userId,
        'calendar_sync',
        'event_import_attempted',
        'info',
        { 
          eventId: event.id,
          summary: event.summary,
          start: event.start
        }
      )

      // Return null for now - actual implementation would create a booking
      return null

    } catch (error) {
      console.error('Failed to create booking from event:', error)
      return null
    }
  }
}

export default CalendarSyncEngine
