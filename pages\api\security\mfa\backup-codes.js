/**
 * MFA Backup Codes API Endpoint for Ocean Soul Sparkles
 * Regenerates backup codes for MFA
 * 
 * Phase 9.1: Enhanced Authentication System
 */

import { withAdminAuth } from '@/lib/admin-auth'
import { mfaManager } from '@/lib/security/mfa-manager'

async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const userId = req.user.id

    // Regenerate backup codes
    const backupCodes = await mfaManager.regenerateBackupCodes(userId)

    res.status(200).json({
      success: true,
      backupCodes,
      message: 'Backup codes regenerated successfully'
    })

  } catch (error) {
    console.error('Backup codes regeneration error:', error)
    res.status(500).json({
      error: 'Failed to regenerate backup codes',
      message: error.message
    })
  }
}

export default withAdminAuth(handler)
