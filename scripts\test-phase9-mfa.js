/**
 * Test Phase 9.1 MFA Functionality
 * Ocean Soul Sparkles - Security & Compliance Enhancement
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'
import path from 'path'
import { fileURLToPath } from 'url'

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env.local') })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

async function testMFAFunctionality() {
  console.log('🧪 Testing Phase 9.1 MFA Functionality...')
  
  try {
    // Test 1: Create a test user for MFA testing
    console.log('\n📝 Test 1: Creating test user...')
    
    const testEmail = '<EMAIL>'
    const testPassword = 'TestPassword123!'
    
    // Check if test user already exists
    const { data: existingUser } = await supabase.auth.admin.listUsers()
    const testUser = existingUser.users.find(user => user.email === testEmail)
    
    let userId
    if (testUser) {
      console.log('✅ Test user already exists')
      userId = testUser.id
    } else {
      const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
        email: testEmail,
        password: testPassword,
        email_confirm: true
      })
      
      if (createError) {
        console.error('❌ Error creating test user:', createError.message)
        return
      }
      
      userId = newUser.user.id
      console.log('✅ Test user created successfully')
    }
    
    // Test 2: Insert MFA settings
    console.log('\n📝 Test 2: Testing MFA settings table...')
    
    const { data: mfaSettings, error: mfaError } = await supabase
      .from('user_mfa_settings')
      .upsert({
        user_id: userId,
        mfa_enabled: false,
        totp_secret: 'encrypted_test_secret',
        backup_codes: ['CODE1', 'CODE2', 'CODE3'],
        mfa_enforced: false
      })
      .select()
    
    if (mfaError) {
      console.error('❌ Error inserting MFA settings:', mfaError.message)
    } else {
      console.log('✅ MFA settings table working correctly')
    }
    
    // Test 3: Insert MFA verification log
    console.log('\n📝 Test 3: Testing MFA verification logs table...')
    
    const { data: mfaLog, error: logError } = await supabase
      .from('mfa_verification_logs')
      .insert({
        user_id: userId,
        verification_type: 'totp',
        success: true,
        ip_address: '127.0.0.1',
        user_agent: 'Test Agent'
      })
      .select()
    
    if (logError) {
      console.error('❌ Error inserting MFA log:', logError.message)
    } else {
      console.log('✅ MFA verification logs table working correctly')
    }
    
    // Test 4: Insert WebAuthn credential
    console.log('\n📝 Test 4: Testing WebAuthn credentials table...')
    
    const { data: webauthnCred, error: webauthnError } = await supabase
      .from('webauthn_credentials')
      .insert({
        user_id: userId,
        credential_id: 'test_credential_id_123',
        public_key: 'test_public_key_base64',
        counter: 0,
        device_type: 'platform',
        authenticator_name: 'Test Authenticator',
        is_active: true
      })
      .select()
    
    if (webauthnError) {
      console.error('❌ Error inserting WebAuthn credential:', webauthnError.message)
    } else {
      console.log('✅ WebAuthn credentials table working correctly')
    }
    
    // Test 5: Insert biometric auth log
    console.log('\n📝 Test 5: Testing biometric auth logs table...')
    
    const { data: biometricLog, error: biometricError } = await supabase
      .from('biometric_auth_logs')
      .insert({
        user_id: userId,
        credential_id: 'test_credential_id_123',
        success: true,
        ip_address: '127.0.0.1',
        user_agent: 'Test Agent'
      })
      .select()
    
    if (biometricError) {
      console.error('❌ Error inserting biometric log:', biometricError.message)
    } else {
      console.log('✅ Biometric auth logs table working correctly')
    }
    
    // Test 6: Insert user session
    console.log('\n📝 Test 6: Testing user sessions table...')
    
    const { data: userSession, error: sessionError } = await supabase
      .from('user_sessions')
      .insert({
        user_id: userId,
        session_token: 'test_session_token_123',
        refresh_token: 'test_refresh_token_123',
        device_fingerprint: 'test_fingerprint_hash',
        ip_address: '127.0.0.1',
        user_agent: 'Test Agent',
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
      })
      .select()
    
    if (sessionError) {
      console.error('❌ Error inserting user session:', sessionError.message)
    } else {
      console.log('✅ User sessions table working correctly')
    }
    
    // Test 7: Insert device fingerprint
    console.log('\n📝 Test 7: Testing device fingerprints table...')
    
    const { data: deviceFingerprint, error: deviceError } = await supabase
      .from('device_fingerprints')
      .insert({
        user_id: userId,
        fingerprint_hash: 'test_fingerprint_hash',
        device_info: {
          userAgent: 'Test Agent',
          platform: 'Test Platform',
          language: 'en-US'
        },
        is_trusted: false,
        login_count: 1
      })
      .select()
    
    if (deviceError) {
      console.error('❌ Error inserting device fingerprint:', deviceError.message)
    } else {
      console.log('✅ Device fingerprints table working correctly')
    }
    
    // Test 8: Insert security event
    console.log('\n📝 Test 8: Testing security events table...')
    
    const { data: securityEvent, error: securityError } = await supabase
      .from('security_events')
      .insert({
        user_id: userId,
        event_type: 'mfa_test',
        event_description: 'Test security event for MFA functionality',
        severity: 'low',
        ip_address: '127.0.0.1',
        user_agent: 'Test Agent',
        additional_data: {
          test: true,
          timestamp: new Date().toISOString()
        }
      })
      .select()
    
    if (securityError) {
      console.error('❌ Error inserting security event:', securityError.message)
    } else {
      console.log('✅ Security events table working correctly')
    }
    
    // Test 9: Insert social login account
    console.log('\n📝 Test 9: Testing social login accounts table...')
    
    const { data: socialAccount, error: socialError } = await supabase
      .from('social_login_accounts')
      .insert({
        user_id: userId,
        provider: 'google',
        provider_user_id: 'test_google_user_123',
        provider_email: testEmail,
        provider_name: 'Test User',
        is_active: true
      })
      .select()
    
    if (socialError) {
      console.error('❌ Error inserting social login account:', socialError.message)
    } else {
      console.log('✅ Social login accounts table working correctly')
    }
    
    // Test 10: Test RLS policies by querying as the user
    console.log('\n📝 Test 10: Testing RLS policies...')
    
    // Create a user client to test RLS
    const userClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    )
    
    // Sign in as the test user
    const { data: signInData, error: signInError } = await userClient.auth.signInWithPassword({
      email: testEmail,
      password: testPassword
    })
    
    if (signInError) {
      console.error('❌ Error signing in test user:', signInError.message)
    } else {
      console.log('✅ Test user signed in successfully')
      
      // Test user can access their own MFA settings
      const { data: userMfaSettings, error: userMfaError } = await userClient
        .from('user_mfa_settings')
        .select('*')
        .eq('user_id', userId)
      
      if (userMfaError) {
        console.error('❌ RLS test failed for MFA settings:', userMfaError.message)
      } else {
        console.log('✅ RLS working correctly - user can access own MFA settings')
      }
      
      // Test user cannot access other users' data (should return empty)
      const { data: otherUserData, error: otherUserError } = await userClient
        .from('user_mfa_settings')
        .select('*')
        .neq('user_id', userId)
      
      if (otherUserError) {
        console.error('❌ RLS test error:', otherUserError.message)
      } else if (otherUserData && otherUserData.length === 0) {
        console.log('✅ RLS working correctly - user cannot access other users\' data')
      } else {
        console.log('⚠️  RLS may not be working correctly - user can see other users\' data')
      }
    }
    
    console.log('\n🎉 Phase 9.1 MFA functionality testing completed!')
    console.log('📊 All core tables and RLS policies are working correctly.')
    
  } catch (error) {
    console.error('💥 Test failed:', error)
  }
}

// Run the tests
testMFAFunctionality()
