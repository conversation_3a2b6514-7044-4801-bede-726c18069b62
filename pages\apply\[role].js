import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import ArtistBraiderApplicationForm from '@/components/admin/users/ArtistBraiderApplicationForm'
import styles from '@/styles/ApplicationPage.module.css'

export default function ApplicationPage() {
  const router = useRouter()
  const { role, token } = router.query
  const [submitting, setSubmitting] = useState(false)
  const [submitted, setSubmitted] = useState(false)
  const [error, setError] = useState(null)
  const [pageLoading, setPageLoading] = useState(true)
  const [tokenValidation, setTokenValidation] = useState({
    loading: true,
    valid: false,
    user: null,
    error: null
  })

  // Validate role parameter
  const validRoles = ['artist', 'braider']
  const isValidRole = validRoles.includes(role)

  // Initialize page and validate token if provided
  useEffect(() => {
    const initializePage = async () => {
      // Wait for router to be ready
      if (!router.isReady) return

      setPageLoading(true)

      if (token && role) {
        // Token-based access - validate the token
        await validateApplicationToken()
      } else {
        // No token provided - this is an error for application pages
        setTokenValidation({
          loading: false,
          valid: false,
          user: null,
          error: 'Access token is required for application submission'
        })
      }

      setPageLoading(false)
    }

    initializePage()
  }, [token, role, router.isReady])

  const validateApplicationToken = async () => {
    try {
      const response = await fetch('/api/applications/validate-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token, role }),
      })

      const result = await response.json()

      if (response.ok && result.valid) {
        setTokenValidation({
          loading: false,
          valid: true,
          user: result.user,
          error: null
        })
      } else {
        setTokenValidation({
          loading: false,
          valid: false,
          user: null,
          error: result.error || 'Invalid or expired token'
        })
      }
    } catch (err) {
      console.error('Error validating token:', err)
      setTokenValidation({
        loading: false,
        valid: false,
        user: null,
        error: 'Failed to validate token'
      })
    }
  }

  const handleSubmit = async (applicationData) => {
    setSubmitting(true)
    setError(null)

    try {
      const submitData = {
        ...applicationData,
        application_type: role
      }

      // Add token to submission if using token-based access
      if (token) {
        submitData.token = token
      }

      // Use different API endpoints based on access method
      const endpoint = token ? '/api/applications/submit-with-token' : '/api/applications/submit'

      if (token) {
        // Direct fetch for token-based submission
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(submitData)
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to submit application')
        }

        const result = await response.json()
        setSubmitted(true)
        console.log('Application submitted successfully:', result)
      } else {
        // Use authenticated fetch for logged-in users
        const { apiFetch } = await import('@/lib/api-fetch')
        const result = await apiFetch(endpoint, {
          method: 'POST',
          body: JSON.stringify(submitData)
        })

        setSubmitted(true)
        console.log('Application submitted successfully:', result)
      }
    } catch (err) {
      console.error('Error submitting application:', err)
      setError(err.message)
    } finally {
      setSubmitting(false)
    }
  }

  const handleCancel = () => {
    window.location.href = 'https://www.oceansoulsparkles.com.au'
  }

  // Show loading while initializing page or validating token
  if (pageLoading || tokenValidation.loading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>{token ? 'Validating access...' : 'Loading...'}</p>
        </div>
      </div>
    )
  }

  // Show token validation error
  if (!tokenValidation.valid) {
    return (
      <div className={styles.container}>
        <Head>
          <title>Access Denied - Ocean Soul Sparkles</title>
        </Head>
        <div className={styles.error}>
          <h1>Access Denied</h1>
          <p>{tokenValidation.error || 'Invalid or expired access token'}</p>
          <p>Please contact your administrator for a new application link.</p>
          <button
            onClick={() => window.location.href = 'https://www.oceansoulsparkles.com.au'}
            className={styles.homeButton}
          >
            Return to Home
          </button>
        </div>
      </div>
    )
  }

  // Show error for invalid role
  if (!isValidRole) {
    return (
      <div className={styles.container}>
        <Head>
          <title>Invalid Application Type - Ocean Soul Sparkles</title>
        </Head>
        <div className={styles.error}>
          <h1>Invalid Application Type</h1>
          <p>The application type "{role}" is not valid. Please use one of the following:</p>
          <ul>
            <li><a href="/apply/artist">Artist Application</a></li>
            <li><a href="/apply/braider">Braider Application</a></li>
          </ul>
        </div>
      </div>
    )
  }

  // Show success message after submission
  if (submitted) {
    return (
      <div className={styles.container}>
        <Head>
          <title>Application Submitted - Ocean Soul Sparkles</title>
        </Head>
        <div className={styles.success}>
          <div className={styles.successIcon}>✓</div>
          <h1>Application Submitted Successfully!</h1>
          <p>
            Thank you for your interest in joining Ocean Soul Sparkles as a {role === 'artist' ? 'Beauty Artist' : 'Hair Braiding Specialist'}.
          </p>
          <p>
            Your application has been received and will be reviewed by our team. We'll contact you within 3-5 business days with next steps.
          </p>
          <div className={styles.nextSteps}>
            <h3>What happens next?</h3>
            <ol>
              <li>Our team will review your application and portfolio</li>
              <li>If selected, we'll schedule a skills assessment</li>
              <li>Final interview with our management team</li>
              <li>Welcome to the Ocean Soul Sparkles family!</li>
            </ol>
          </div>
          <button
            onClick={() => window.location.href = 'https://www.oceansoulsparkles.com.au'}
            className={styles.homeButton}
          >
            Return to Home
          </button>
        </div>
      </div>
    )
  }

  const roleTitle = role === 'artist' ? 'Beauty Artist' : 'Hair Braiding Specialist'

  return (
    <div className={styles.container}>
      <Head>
        <title>{roleTitle} Application - Ocean Soul Sparkles</title>
        <meta name="description" content={`Apply to join Ocean Soul Sparkles as a ${roleTitle}. Submit your application and portfolio for review.`} />
      </Head>

      <div className={styles.header}>
        <h1>Apply as a {roleTitle}</h1>
        <p>Join the Ocean Soul Sparkles team and showcase your talents in a supportive, creative environment.</p>
      </div>

      {error && (
        <div className={styles.errorMessage}>
          <strong>Error:</strong> {error}
        </div>
      )}

      <div className={styles.formContainer}>
        <ArtistBraiderApplicationForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          initialData={{ application_type: role }}
          tokenUser={tokenValidation.user}
        />
      </div>

      {submitting && (
        <div className={styles.submittingOverlay}>
          <div className={styles.submittingModal}>
            <div className={styles.spinner}></div>
            <p>Submitting your application...</p>
          </div>
        </div>
      )}
    </div>
  )
}
