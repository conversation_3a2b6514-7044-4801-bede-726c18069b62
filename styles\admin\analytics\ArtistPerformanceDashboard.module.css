/* Artist Performance Dashboard Styles */

.dashboard {
  padding: 20px;
  background: #f8fafc;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header h2 {
  color: #1f2937;
  font-size: 28px;
  font-weight: 700;
  margin: 0;
}

.controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.timeframeSelect {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.timeframeSelect:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.forecastButton {
  padding: 8px 16px;
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.forecastButton:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.forecastButton.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

/* Loading and Error States */
.loadingContainer,
.errorContainer,
.noDataContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer h3,
.noDataContainer h3 {
  color: #dc2626;
  margin-bottom: 12px;
}

.retryButton {
  padding: 10px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 16px;
}

.retryButton:hover {
  background: #2563eb;
}

/* KPI Grid */
.kpiGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.kpiCard {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.kpiCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.kpiCard h3 {
  color: #6b7280;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0 0 8px 0;
}

.kpiValue {
  color: #1f2937;
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
  line-height: 1.2;
}

.kpiChange {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.kpiChange.positive {
  color: #059669;
}

.kpiChange.negative {
  color: #dc2626;
}

.kpiSubtext {
  color: #6b7280;
  font-size: 14px;
  margin-top: 4px;
}

/* Charts Grid */
.chartsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.chartCard {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.chartCard h3 {
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 20px 0;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.chartContainer {
  height: 300px;
  position: relative;
}

/* Insights Section */
.insightsSection {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.insightsSection h3 {
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 20px 0;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.insightsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.insightCard {
  background: #f9fafb;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.insightCard h4 {
  color: #374151;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.insightCard p {
  color: #6b7280;
  font-size: 14px;
  margin: 0 0 8px 0;
  line-height: 1.5;
}

.insightCard p:last-child {
  margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .chartsGrid {
    grid-template-columns: 1fr;
  }
  
  .chartContainer {
    height: 250px;
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .controls {
    justify-content: center;
  }

  .kpiGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .kpiCard {
    padding: 20px;
  }

  .kpiValue {
    font-size: 28px;
  }

  .chartsGrid {
    gap: 16px;
  }

  .chartCard {
    padding: 20px;
  }

  .chartContainer {
    height: 200px;
  }

  .insightsGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .insightCard {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .dashboard {
    padding: 12px;
  }

  .header h2 {
    font-size: 24px;
  }

  .kpiValue {
    font-size: 24px;
  }

  .chartCard h3 {
    font-size: 16px;
  }

  .chartContainer {
    height: 180px;
  }
}

/* Animation for data updates */
.kpiCard,
.chartCard,
.insightCard {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom scrollbar for overflow areas */
.dashboard::-webkit-scrollbar {
  width: 8px;
}

.dashboard::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.dashboard::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.dashboard::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Focus states for accessibility */
.timeframeSelect:focus,
.forecastButton:focus,
.retryButton:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .dashboard {
    background: white;
    padding: 0;
  }

  .controls {
    display: none;
  }

  .chartCard,
  .kpiCard,
  .insightCard {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #e5e7eb;
  }
}
