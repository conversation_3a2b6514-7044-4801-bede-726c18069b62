/**
 * MFA Setup API Endpoint for Ocean Soul Sparkles
 * Generates TOTP secret and QR code for MFA setup
 * 
 * Phase 9.1: Enhanced Authentication System
 */

import { withAdminAuth } from '@/lib/admin-auth'
import { mfaManager } from '@/lib/security/mfa-manager'

async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { userEmail, userName } = req.body
    const userId = req.user.id

    if (!userEmail) {
      return res.status(400).json({ 
        error: 'Missing required fields',
        message: 'User email is required'
      })
    }

    // Generate TOTP secret and QR code
    const result = await mfaManager.generateTOTPSecret(userId, userEmail)

    res.status(200).json({
      success: true,
      qrCode: result.qrCode,
      manualEntryKey: result.manualEntryKey,
      serviceName: result.serviceName
    })

  } catch (error) {
    console.error('MFA setup error:', error)
    res.status(500).json({
      error: 'MFA setup failed',
      message: error.message
    })
  }
}

export default withAdminAuth(handler)
