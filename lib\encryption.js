/**
 * Encryption utilities for secure credential storage
 * Uses AES-256-GCM encryption for sensitive data
 */

import crypto from 'crypto'

// Get encryption key from environment or generate a default one
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'ocean-soul-sparkles-default-key-32'
const ALGORITHM = 'aes-256-gcm'

/**
 * Encrypt sensitive data
 * @param {string} text - Text to encrypt
 * @returns {string} - Encrypted data with IV and auth tag
 */
export function encrypt(text) {
  if (!text || typeof text !== 'string') {
    return text
  }

  try {
    // Create a 32-byte key from the encryption key
    const key = crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32)
    
    // Generate a random IV
    const iv = crypto.randomBytes(16)
    
    // Create cipher
    const cipher = crypto.createCipher('aes-256-cbc', key)
    
    // Encrypt the text
    let encrypted = cipher.update(text, 'utf8', 'hex')
    encrypted += cipher.final('hex')
    
    // Combine IV and encrypted data
    return iv.toString('hex') + ':' + encrypted
  } catch (error) {
    console.error('Encryption error:', error)
    return text // Return original text if encryption fails
  }
}

/**
 * Decrypt sensitive data
 * @param {string} encryptedData - Encrypted data with IV
 * @returns {string} - Decrypted text
 */
export function decrypt(encryptedData) {
  if (!encryptedData || typeof encryptedData !== 'string') {
    return encryptedData
  }

  // Check if data is encrypted (contains ':')
  if (!encryptedData.includes(':')) {
    return encryptedData // Return as-is if not encrypted
  }

  try {
    // Create a 32-byte key from the encryption key
    const key = crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32)
    
    // Split IV and encrypted data
    const [ivHex, encrypted] = encryptedData.split(':')
    const iv = Buffer.from(ivHex, 'hex')
    
    // Create decipher
    const decipher = crypto.createDecipher('aes-256-cbc', key)
    
    // Decrypt the data
    let decrypted = decipher.update(encrypted, 'hex', 'utf8')
    decrypted += decipher.final('utf8')
    
    return decrypted
  } catch (error) {
    console.error('Decryption error:', error)
    return encryptedData // Return original data if decryption fails
  }
}

/**
 * Mask sensitive data for display
 * @param {string} value - Value to mask
 * @param {number} visibleChars - Number of characters to show at start/end
 * @returns {string} - Masked value
 */
export function maskValue(value, visibleChars = 4) {
  if (!value || typeof value !== 'string') {
    return value
  }

  if (value.length <= visibleChars * 2) {
    return '*'.repeat(value.length)
  }

  const start = value.substring(0, visibleChars)
  const end = value.substring(value.length - visibleChars)
  const middle = '*'.repeat(Math.max(8, value.length - visibleChars * 2))

  return `${start}${middle}${end}`
}

/**
 * Check if a value appears to be encrypted
 * @param {string} value - Value to check
 * @returns {boolean} - True if value appears encrypted
 */
export function isEncrypted(value) {
  if (!value || typeof value !== 'string') {
    return false
  }

  // Check for our encryption format (hex:hex)
  const parts = value.split(':')
  if (parts.length !== 2) {
    return false
  }

  // Check if both parts are valid hex
  const hexRegex = /^[0-9a-fA-F]+$/
  return hexRegex.test(parts[0]) && hexRegex.test(parts[1])
}

/**
 * Securely store a setting with encryption
 * @param {string} key - Setting key
 * @param {string} value - Setting value
 * @param {boolean} shouldEncrypt - Whether to encrypt the value
 * @returns {string} - Value to store (encrypted or plain)
 */
export function prepareForStorage(key, value, shouldEncrypt = true) {
  if (!value || typeof value !== 'string') {
    return value
  }

  // List of keys that should always be encrypted
  const sensitiveKeys = [
    'gmail_smtp_password',
    'gmail_smtp_app_password',
    'workspace_smtp_password',
    'onesignal_rest_api_key',
    'google_client_secret',
    'square_access_token',
    'square_webhook_signature_key'
  ]

  const needsEncryption = shouldEncrypt || sensitiveKeys.includes(key)

  if (needsEncryption && !isEncrypted(value)) {
    return encrypt(value)
  }

  return value
}

/**
 * Retrieve and decrypt a setting
 * @param {string} value - Stored value (may be encrypted)
 * @returns {string} - Decrypted value
 */
export function prepareFromStorage(value) {
  if (!value || typeof value !== 'string') {
    return value
  }

  if (isEncrypted(value)) {
    return decrypt(value)
  }

  return value
}

/**
 * Generate a secure random key for encryption
 * @param {number} length - Key length in bytes
 * @returns {string} - Random key
 */
export function generateSecureKey(length = 32) {
  return crypto.randomBytes(length).toString('hex')
}

export default {
  encrypt,
  decrypt,
  maskValue,
  isEncrypted,
  prepareForStorage,
  prepareFromStorage,
  generateSecureKey
}
