import { useState, useEffect, useCallback, useMemo } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { getAuthToken } from '@/lib/auth-token-manager';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import AdminLayout from '@/components/admin/AdminLayout';
import DashboardErrorBoundary from '@/components/admin/DashboardErrorBoundary';
import DashboardPerformanceMonitor from '@/components/admin/DashboardPerformanceMonitor';
// import AvailabilityCalendarCard from '@/components/admin/AvailabilityCalendarCard'; // Replaced
// import AvailabilitySettings from '@/components/admin/AvailabilitySettings'; // REMOVED
import AvailabilityStatusDisplayCard from '@/components/admin/AvailabilityStatusDisplayCard'; // IMPORT NEW CARD
import PerformanceMetricsCard from '@/components/admin/PerformanceMetricsCard';
import QuickActionsCard from '@/components/admin/QuickActionsCard';
import BookingListCard from '@/components/admin/BookingListCard'; // Import BookingListCard

// Phase 6: AI-Powered Features Integration
import AISchedulingAssistant from '@/components/admin/AISchedulingAssistant';
import AIInsightsDashboard from '@/components/admin/AIInsightsDashboard';
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization';

import { toast } from 'react-toastify';
import styles from '@/styles/admin/ArtistBraiderDashboard.module.css';


export default function ArtistBraiderDashboard() {
  const { user, loading: authLoading, supabaseClient, signOut } = useAuth(); // Add signOut from useAuth
  const router = useRouter();
  const [profile, setProfile] = useState(null);
  const [dashboardData, setDashboardData] = useState(null); // To store all data from dashboard-enhanced
  const [loading, setLoading] = useState(true); // For dashboard data loading specifically
  const [error, setError] = useState(null)
  const [isFirstTimeLogin, setIsFirstTimeLogin] = useState(false) // Track first-time login
  const [renderCount, setRenderCount] = useState(0) // Debug: Track re-renders

  // Phase 6: AI Features State Management
  const [showAIFeatures, setShowAIFeatures] = useState(true) // Toggle AI features visibility
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]) // Current date for AI optimization
  const [aiInsightsExpanded, setAIInsightsExpanded] = useState(false) // Control AI insights panel

  // Mobile optimization hook
  const {
    isMobile,
    isTablet,
    viewport,
    shouldReduceAnimations,
    isSmallScreen
  } = useMobileOptimization()

  // Debug: Track component re-renders
  useEffect(() => {
    setRenderCount(prev => prev + 1)
    console.log(`[ArtistDashboard] Component render #${renderCount + 1}`)
  })

  // Memoize the fetchDashboardData function to prevent unnecessary re-renders
  // Fixed: Removed dependencies that cause infinite loops
  const fetchDashboardData = useCallback(async () => {
    console.log("[ArtistDashboard] fetchDashboardData called - user:", !!user, "authLoading:", authLoading);

    if (!user) {
      console.log("[ArtistDashboard] No user, skipping fetch.");
      setLoading(false);
      if (!authLoading) router.push('/staff-login?redirect=/admin/artist-braider-dashboard');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      console.log('[ArtistDashboard] Fetching dashboard data...');
      console.log('[ArtistDashboard] User ID:', user.id, 'Email:', user.email);

      // Get the auth token using the proper method
      const authToken = await getAuthToken();
      console.log('[ArtistDashboard] Auth token retrieved:', !!authToken, 'Length:', authToken?.length);

      if (!authToken) {
        console.error('[ArtistDashboard] No auth token available');
        setError('Authentication token not available. Please sign in again.');
        setLoading(false);
        router.push('/staff-login?redirect=/admin/artist-braider-dashboard');
        return;
      }

      const response = await fetch('/api/artist/dashboard-enhanced', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('[ArtistDashboard] API response status:', response.status, 'ok:', response.ok);

      if (!response.ok) {
        const errData = await response.json();
        console.error('[ArtistDashboard] API error response:', errData);

        if (response.status === 403) {
          setError('Access denied. This dashboard is only available for artists and braiders.');
          toast.error('Access Denied.');
        } else if (response.status === 404 && errData.error === 'Artist profile not found') {
          console.warn('[ArtistDashboard] Artist profile not found via API, redirecting to complete profile.');
          toast.info('Please complete your profile to continue.');
          router.push('/admin/complete-profile');
          return;
        } else {
          setError(errData.error || 'Failed to fetch dashboard data');
          toast.error(errData.error || 'Failed to fetch dashboard data');
        }
        setLoading(false);
        return;
      }

      const data = await response.json();
      console.log('[ArtistDashboard] Dashboard data received:', data);

      // Check profile completion status
      const profileIncomplete = !data.profile ||
                               data.profile.is_profile_complete === false ||
                               (typeof data.profile.is_profile_complete === 'undefined' &&
                                (!data.profile.artist_name || !data.profile.display_name))

      if (profileIncomplete) {
        console.log('[ArtistDashboard] Profile incomplete, redirecting to /admin/complete-profile');

        // Show different messages for first-time vs returning users
        const isFirstTime = user.user_metadata?.account_activated &&
                           !localStorage.getItem(`oss_dashboard_visited_${user.id}`)
        if (isFirstTime) {
          toast.success('Welcome! Let\'s complete your profile to get started.');
        } else {
          toast.info('Please complete your profile to continue.');
        }

        router.push('/admin/complete-profile');
        setLoading(false);
        return;
      }

      // Mark that user has visited dashboard (for first-time detection)
      const isFirstTime = user.user_metadata?.account_activated &&
                         !localStorage.getItem(`oss_dashboard_visited_${user.id}`)
      if (isFirstTime && user?.id) {
        localStorage.setItem(`oss_dashboard_visited_${user.id}`, 'true')
        setIsFirstTimeLogin(false)
      }

      setProfile(data.profile);
      setDashboardData(data);

    } catch (error) {
      console.error('[ArtistDashboard] Error fetching dashboard data:', error);
      setError('Failed to load dashboard data. Please try again.');
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  }, [user?.id, user?.user_metadata?.account_activated, authLoading, router]);

  // Initialize first-time login status separately to avoid dependency loops
  useEffect(() => {
    if (user?.id) {
      const isFirstTime = user.user_metadata?.account_activated &&
                         !localStorage.getItem(`oss_dashboard_visited_${user.id}`)
      setIsFirstTimeLogin(isFirstTime)
    }
  }, [user?.id, user?.user_metadata?.account_activated])

  // Phase 6: AI Features Callback Functions
  const handleOptimizationComplete = useCallback((optimizationResult) => {
    console.log('[ArtistDashboard] AI optimization completed:', optimizationResult)

    if (optimizationResult) {
      toast.success(`Schedule optimized! ${optimizationResult.improvements?.efficiencyImprovement || 0}% efficiency improvement`)

      // Refresh dashboard data to reflect any changes
      fetchDashboardData()
    } else {
      // Optimization was applied/dismissed
      console.log('[ArtistDashboard] Optimization applied or dismissed')
    }
  }, [fetchDashboardData])

  const toggleAIFeatures = useCallback(() => {
    setShowAIFeatures(prev => !prev)
    console.log('[ArtistDashboard] AI features toggled:', !showAIFeatures)
  }, [showAIFeatures])

  const handleDateChange = useCallback((newDate) => {
    setSelectedDate(newDate)
    console.log('[ArtistDashboard] Selected date changed:', newDate)
  }, [])

  // Optimized useEffect with proper dependencies - Fixed infinite loop
  useEffect(() => {
    console.log('[ArtistDashboard] useEffect triggered - authLoading:', authLoading, 'user:', !!user, 'profile:', !!profile, 'dashboardData:', !!dashboardData);

    if (!authLoading && user) {
      // Only fetch if we don't already have data
      if (!profile || !dashboardData) {
        console.log('[ArtistDashboard] Fetching dashboard data because profile or dashboardData is missing');
        fetchDashboardData();
      } else {
        console.log('[ArtistDashboard] Dashboard data already loaded, skipping fetch');
      }
    } else if (!authLoading && !user) {
      console.log('[ArtistDashboard] No user found, redirecting to staff login');
      router.push('/staff-login?redirect=/admin/artist-braider-dashboard');
    }
  }, [user, authLoading, profile, dashboardData]); // Removed fetchDashboardData from dependencies



  // This function might not be directly needed if ProfileManagementCard is on a separate page
  // or if AvailabilitySettings handles its own data refreshes.
  // const handleProfileUpdate = () => {
  //   fetchDashboardData();
  // };

  // This can be removed if AvailabilitySettings handles its own internal state
  // const handleAvailabilityUpdate = (newStatus) => {
  //   setProfile(prev => ({
  //     ...prev,
  //     is_available_today: newStatus === 'available'
  //   }));
  // };

  if (authLoading || loading) { // Combined loading state
    return (
      <ProtectedRoute>
        <AdminLayout>
          <div className={styles.container}>
            <div className={styles.header}>
              <h1>Artist & Braider Dashboard</h1>
              <p>Loading your personalized dashboard...</p>
            </div>
            <div className={styles.loadingContainer}>
              <div className={styles.spinner}></div>
              <span>{authLoading ? 'Authenticating...' : 'Loading dashboard data...'}</span>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    );
  }

  if (error) {
    return (
      <ProtectedRoute>
        <AdminLayout>
          <div className={styles.container}>
            <div className={styles.header}>
              <h1>Artist & Braider Dashboard</h1>
            </div>
            <div className={styles.errorContainer}>
              <div className={styles.errorIcon}>⚠️</div>
              <h2>Access Restricted or Error</h2>
              <p>{error}</p>
              <button 
                onClick={fetchDashboardData}
                className={styles.retryButton}
              >
                Try Again
              </button>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    );
  }

  // If profile is still null after loading and no error, it might be due to redirection
  // or an edge case. It's safer to not render the main content.
  if (!profile && !error) { // Check !error as well to avoid showing this during error state
    return (
      <ProtectedRoute>
        <AdminLayout>
          <div className={styles.container}>
            <div className={styles.loadingContainer}>
                <div className={styles.spinner}></div>
                <span>Verifying profile status...</span>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <AdminLayout>
        <DashboardPerformanceMonitor>
          <DashboardErrorBoundary>
          <Head>
            <title>Artist Dashboard | Ocean Soul Sparkles</title>
            <script src="/js/dashboard-performance-optimizer.js" />
          </Head>
          <div className={`${styles.container} artist-dashboard-container ${!loading && !authLoading ? 'loaded' : ''}`}>
          <div className={styles.header}>
            <div className={styles.headerContent}>
              <h1>Artist & Braider Dashboard</h1>
              <p>Welcome back, {profile?.display_name || profile?.artist_name || user?.email || 'Artist'}!</p>
              {isFirstTimeLogin && (
                <div className={styles.welcomeBanner}>
                  <p>🎉 Welcome to your new dashboard! Complete your profile to get started.</p>
                </div>
              )}
            </div>
            <div className={styles.headerActions}>
              <button
                onClick={toggleAIFeatures}
                className={`${styles.aiToggleButton} ${showAIFeatures ? styles.active : ''}`}
                title={showAIFeatures ? "Hide AI Features" : "Show AI Features"}
              >
                🤖 AI {showAIFeatures ? 'ON' : 'OFF'}
              </button>
              <button
                onClick={fetchDashboardData}
                className={styles.refreshButton}
                title="Refresh Dashboard"
                disabled={loading || authLoading}
              >
                {loading ? 'Refreshing...' : '🔄 Refresh'}
              </button>
              <button
                onClick={async () => {
                  try {
                    await signOut()
                    router.push('/')
                  } catch (error) {
                    console.error('Logout error:', error)
                    toast.error('Failed to sign out')
                  }
                }}
                className={styles.signOutButton}
                title="Sign Out"
              >
                🚪 Sign Out
              </button>
            </div>
          </div>

          <div className={styles.dashboardGrid}>
            {/* Performance Metrics Card (MOVED TO TOP) */}
            <div className={`${styles.card} ${styles.metricsSection}`}>
              <PerformanceMetricsCard
                profile={profile} // Pass full profile
                stats={dashboardData?.stats} // Pass stats from dashboardData
              />
            </div>

            {/* Quick Actions Card */}
            <div className={`${styles.card} ${styles.quickActionsSection}`}>
              <QuickActionsCard 
                profile={profile}
                userPermissions={dashboardData?.permissions}
              />
            </div>

            {/* Availability Status Display Card (NEW) */}
            <div className={`${styles.card} ${styles.availabilityStatusSection}`}>
              {profile && dashboardData?.stats ? (
                <AvailabilityStatusDisplayCard
                  artistProfile={{ // Construct a focused prop
                    id: profile.id,
                    user_id: profile.user_id,
                    is_available_today: profile.is_available_today,
                    max_daily_bookings: profile.max_daily_bookings,
                    todays_bookings_count: dashboardData.stats.todaysBookings,
                  }}
                  onAvailabilityUpdate={fetchDashboardData}
                />
              ) : (
                <div className={styles.loadingContainer}> {/* Use existing loading style for consistency */}
                  <div className={styles.spinner}></div>
                  <p>Loading availability status...</p>
                </div>
              )}
            </div>

            {/* Booking List Card */}
            <div className={`${styles.card} ${styles.bookingsSection}`}>
              {profile && profile.id ? (
                <BookingListCard artistId={profile.id} />
              ) : (
                <p>Loading booking information...</p>
              )}
            </div>

            {/* Phase 6: AI Scheduling Assistant */}
            {showAIFeatures && profile?.id && (
              <div className={`${styles.card} ${styles.aiSchedulingSection}`}>
                <div className={styles.aiSectionHeader}>
                  <h3>🤖 AI Schedule Optimization</h3>
                  <div className={styles.aiControls}>
                    <input
                      type="date"
                      value={selectedDate}
                      onChange={(e) => handleDateChange(e.target.value)}
                      className={styles.dateInput}
                      title="Select date for optimization"
                    />
                    <button
                      onClick={toggleAIFeatures}
                      className={styles.toggleAIButton}
                      title="Hide AI features"
                    >
                      ❌
                    </button>
                  </div>
                </div>
                <AISchedulingAssistant
                  artistId={profile.id}
                  date={selectedDate}
                  onOptimizationComplete={handleOptimizationComplete}
                  showHeader={false}
                  className={isMobile ? styles.aiMobile : ''}
                />
              </div>
            )}

            {/* Phase 6: AI Insights Dashboard */}
            {showAIFeatures && (
              <div className={`${styles.card} ${styles.aiInsightsSection} ${aiInsightsExpanded ? styles.expanded : ''}`}>
                <div className={styles.aiSectionHeader}>
                  <h3>📊 AI Business Insights</h3>
                  <div className={styles.aiControls}>
                    <button
                      onClick={() => setAIInsightsExpanded(!aiInsightsExpanded)}
                      className={styles.expandButton}
                      title={aiInsightsExpanded ? "Collapse insights" : "Expand insights"}
                    >
                      {aiInsightsExpanded ? '🔽' : '🔼'}
                    </button>
                    <button
                      onClick={toggleAIFeatures}
                      className={styles.toggleAIButton}
                      title="Hide AI features"
                    >
                      ❌
                    </button>
                  </div>
                </div>
                {aiInsightsExpanded && (
                  <AIInsightsDashboard
                    autoRefresh={true}
                    refreshInterval={300000} // 5 minutes
                    className={isMobile ? styles.aiMobile : ''}
                  />
                )}
                {!aiInsightsExpanded && (
                  <div className={styles.aiPreview}>
                    <p>Click expand to view AI-generated business insights and recommendations</p>
                    <div className={styles.aiFeatures}>
                      <span>📈 Performance Analytics</span>
                      <span>🚨 Anomaly Detection</span>
                      <span>💡 Smart Recommendations</span>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Profile Summary - can be kept or simplified if data is in QuickActions or ProfileManagementCard */}
          <div className={`${styles.card} ${styles.profileSummary}`}>
            {/* <div className={styles.summaryCard}> // Redundant if .card is used */}
              <h3>Profile Summary</h3>
              <div className={styles.summaryGrid}>
                <div className={styles.summaryItem}>
                  <span className={styles.summaryLabel}>Role</span>
                  <span className={styles.summaryValue}>
                    {user?.role?.charAt(0).toUpperCase() + user?.role?.slice(1) || 'N/A'}
                  </span>
                </div>
                <div className={styles.summaryItem}>
                  <span className={styles.summaryLabel}>Specialties</span>
                  <span className={styles.summaryValue}>
                    {profile?.specializations?.join(', ') || 'Not set'}
                  </span>
                </div>
                {/* Removed Experience from summary as it's in ProfileManagementCard
                <div className={styles.summaryItem}>
                  <span className={styles.summaryLabel}>Experience</span>
                  <span className={styles.summaryValue}>
                    {profile?.experience_years || 0} years
                  </span>
                </div>
                */}
                <div className={styles.summaryItem}>
                  <span className={styles.summaryLabel}>Status Today</span>
                  <span 
                    className={styles.summaryValue}
                    style={{ 
                      color: profile?.is_available_today ? '#10b981' : '#ef4444',
                      fontWeight: 'bold'
                    }}
                  >
                    {profile?.is_available_today ? 'Available' : 'Unavailable'}
                  </span>
                </div>
                 <div className={styles.summaryItem}>
                  <span className={styles.summaryLabel}>Profile Complete</span>
                  <span className={styles.summaryValue} style={{color: profile?.is_profile_complete ? 'green' : 'orange'}}>
                    {profile?.is_profile_complete ? 'Yes' :
                      <Link href="/admin/complete-profile" legacyBehavior><a className={styles.completeProfileLink}>No, Complete Now</a></Link>
                    }
                  </span>
                </div>
              </div>
            {/* </div> */}
          </div>

          {/* Help Section - Updated Profile Link */}
          <div className={styles.helpSection}>
            <div className={styles.helpCard}>
              <h3>Need Help?</h3>
              <div className={styles.helpLinks}>
                <a href="/help/artist-guide" target="_blank" rel="noopener noreferrer" className={styles.helpLink}>
                  📖 Artist Guide
                </a>
                <a href="/help/booking-management" target="_blank" rel="noopener noreferrer" className={styles.helpLink}>
                  📅 Booking Management
                </a>
                {showAIFeatures && (
                  <a href="/help/ai-features" target="_blank" rel="noopener noreferrer" className={styles.helpLink}>
                    🤖 AI Features Guide
                  </a>
                )}
                <a href="/help/contact" target="_blank" rel="noopener noreferrer" className={styles.helpLink}>
                  💬 Contact Support
                </a>
                <Link href="/admin/my-profile" legacyBehavior>
                  <a className={styles.helpLink}>👤 Update Profile</a>
                </Link>
              </div>
            </div>

            {/* AI Features Status */}
            {showAIFeatures && (
              <div className={styles.aiStatusCard}>
                <h4>🤖 AI Features Active</h4>
                <div className={styles.aiStatusGrid}>
                  <div className={styles.aiStatusItem}>
                    <span className={styles.aiStatusIcon}>⚡</span>
                    <span className={styles.aiStatusText}>Schedule Optimization</span>
                  </div>
                  <div className={styles.aiStatusItem}>
                    <span className={styles.aiStatusIcon}>📊</span>
                    <span className={styles.aiStatusText}>Business Insights</span>
                  </div>
                  <div className={styles.aiStatusItem}>
                    <span className={styles.aiStatusIcon}>🎯</span>
                    <span className={styles.aiStatusText}>Smart Recommendations</span>
                  </div>
                  <div className={styles.aiStatusItem}>
                    <span className={styles.aiStatusIcon}>🗺️</span>
                    <span className={styles.aiStatusText}>Travel Optimization</span>
                  </div>
                </div>
                <p className={styles.aiStatusNote}>
                  AI features are powered by machine learning algorithms to optimize your schedule and provide business insights.
                </p>
              </div>
            )}
          </div>
          </div>
          </DashboardErrorBoundary>
        </DashboardPerformanceMonitor>
      </AdminLayout>
    </ProtectedRoute>
  )
}
