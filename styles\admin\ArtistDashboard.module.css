/* Artist Dashboard Page Styles */

.container {
  min-height: 100vh;
  background: #f7fafc;
  display: flex;
  flex-direction: column;
}

.header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.breadcrumbItem {
  color: #4a5568;
  font-weight: 500;
}

.breadcrumbItem:last-child {
  color: #1a202c;
  font-weight: 600;
}

.breadcrumbSeparator {
  color: #cbd5e0;
  font-weight: 400;
}

.headerActions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.profileButton,
.scheduleButton,
.paymentsButton,
.signOutButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: white;
  color: #4a5568;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.profileButton:hover,
.scheduleButton:hover,
.paymentsButton:hover {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.signOutButton {
  background: #fed7d7;
  border-color: #feb2b2;
  color: #c53030;
}

.signOutButton:hover {
  background: #c53030;
  color: white;
  border-color: #c53030;
}

.main {
  flex: 1;
  padding: 0;
}

.footer {
  background: white;
  border-top: 1px solid #e2e8f0;
  padding: 1rem 2rem;
  margin-top: auto;
}

.footerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
}

.footerContent p {
  margin: 0;
  color: #4a5568;
  font-size: 0.875rem;
}

.footerLinks {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
}

.footerLinks button {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  font-size: 0.875rem;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footerLinks button:hover {
  color: #764ba2;
  text-decoration: underline;
}

.separator {
  color: #cbd5e0;
}

/* Loading State */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  gap: 1rem;
  background: #f7fafc;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading p {
  color: #4a5568;
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .breadcrumb {
    justify-content: center;
  }

  .headerActions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .profileButton,
  .scheduleButton,
  .paymentsButton,
  .signOutButton {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }

  .footerContent {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .footerLinks {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .headerActions {
    grid-template-columns: 1fr 1fr;
    display: grid;
    gap: 0.5rem;
  }

  .signOutButton {
    grid-column: 1 / -1;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .header {
    border-bottom-color: #1a202c;
  }

  .profileButton,
  .scheduleButton,
  .paymentsButton {
    border-color: #1a202c;
  }

  .profileButton:hover,
  .scheduleButton:hover,
  .paymentsButton:hover {
    background: #1a202c;
    border-color: #1a202c;
  }
}

/* Print styles */
@media print {
  .header,
  .footer {
    display: none;
  }

  .container {
    background: white;
  }
}
