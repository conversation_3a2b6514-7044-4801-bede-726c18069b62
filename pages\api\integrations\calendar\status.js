/**
 * Calendar Status API Endpoint for Ocean Soul Sparkles
 * Provides calendar integration status and information
 * 
 * Phase 7: Advanced Integrations & Ecosystem
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { integrationRateLimit } from '@/lib/integrations/rate-limiter'
import { RequestValidator, AuditLogger } from '@/lib/integrations/security-utils'
import CalendarManager from '@/lib/integrations/calendar/calendar-manager'
import oauthManager from '@/lib/integrations/oauth-manager'

/**
 * Calendar Status Handler
 * GET /api/integrations/calendar/status - Get calendar integration status
 */
export default async function handler(req, res) {
  // Apply rate limiting
  await new Promise((resolve, reject) => {
    integrationRateLimit(req, res, (error) => {
      if (error) reject(error)
      else resolve()
    })
  })

  const startTime = Date.now()

  try {
    // Validate request method
    RequestValidator.validateEndpointAccess(req, ['GET'])

    // Authenticate user
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.success) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      })
    }

    const { user } = authResult
    const userId = user.id

    // Get integration status
    const integrationStatus = await oauthManager.getIntegrationStatus(userId)
    
    // Filter for calendar providers
    const calendarProviders = ['google_calendar', 'outlook', 'apple_calendar']
    const calendarIntegrations = integrationStatus.filter(integration => 
      calendarProviders.includes(integration.provider)
    )

    // Get detailed calendar information
    const calendarManager = new CalendarManager(userId)
    const connectedProviders = await calendarManager.getConnectedProviders()

    // Test connections
    const connectionTests = await calendarManager.testAllConnections()

    // Get sync status
    const syncStatus = await getSyncStatus(userId)

    // Combine all information
    const integrations = calendarIntegrations.map(integration => {
      const providerInfo = connectedProviders.find(p => p.provider === integration.provider)
      const connectionTest = connectionTests.find(t => t.provider === integration.provider)
      
      return {
        provider: integration.provider,
        name: getProviderName(integration.provider),
        connected: integration.connected,
        status: connectionTest?.success ? 'connected' : 'error',
        lastUpdated: integration.lastUpdated,
        needsRefresh: integration.needsRefresh,
        settings: providerInfo?.settings || {},
        lastSync: syncStatus.lastSync,
        error: connectionTest?.error || null
      }
    })

    await AuditLogger.logApiAccess(
      userId,
      req.url,
      req.method,
      200,
      Date.now() - startTime,
      { action: 'get_calendar_status', integrationCount: integrations.length }
    )

    return res.status(200).json({
      success: true,
      integrations,
      syncStatus,
      availableProviders: getAvailableProviders()
    })

  } catch (error) {
    console.error('Calendar status error:', error)

    await AuditLogger.logApiAccess(
      req.user?.id || null,
      req.url,
      req.method,
      500,
      Date.now() - startTime,
      { error: error.message }
    )

    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to get calendar status'
    })
  }
}

/**
 * Get sync status for user
 */
async function getSyncStatus(userId) {
  try {
    const { createClient } = await import('@supabase/supabase-js')
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )

    // Get latest sync status
    const { data: syncData, error } = await supabase
      .from('integration_sync_status')
      .select('*')
      .eq('user_id', userId)
      .in('sync_type', ['calendar', 'full'])
      .order('updated_at', { ascending: false })
      .limit(1)
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Failed to get sync status:', error)
    }

    // Get recent sync logs for statistics
    const { data: logs, error: logsError } = await supabase
      .from('integration_logs')
      .select('*')
      .eq('user_id', userId)
      .in('action', ['full_sync_completed', 'calendar_sync_completed'])
      .order('created_at', { ascending: false })
      .limit(5)

    if (logsError) {
      console.error('Failed to get sync logs:', logsError)
    }

    // Calculate statistics
    const stats = calculateStats(logs || [])

    return {
      lastSync: syncData?.last_sync_at || null,
      nextSync: syncData?.next_sync_at || null,
      status: syncData?.status || 'pending',
      stats
    }

  } catch (error) {
    console.error('Error getting sync status:', error)
    return {
      lastSync: null,
      nextSync: null,
      status: 'unknown',
      stats: {
        bookingsSynced: 0,
        eventsImported: 0,
        totalSyncs: 0
      }
    }
  }
}

/**
 * Calculate sync statistics
 */
function calculateStats(logs) {
  const stats = {
    bookingsSynced: 0,
    eventsImported: 0,
    totalSyncs: logs.length
  }

  logs.forEach(log => {
    if (log.details) {
      if (log.details.bookingsToCalendar?.success) {
        stats.bookingsSynced += log.details.bookingsToCalendar.success
      }
      if (log.details.calendarToBookings?.success) {
        stats.eventsImported += log.details.calendarToBookings.success
      }
    }
  })

  return stats
}

/**
 * Get provider display name
 */
function getProviderName(provider) {
  const names = {
    google_calendar: 'Google Calendar',
    outlook: 'Microsoft Outlook',
    apple_calendar: 'Apple Calendar'
  }
  return names[provider] || provider
}

/**
 * Get available calendar providers
 */
function getAvailableProviders() {
  return [
    {
      id: 'google_calendar',
      name: 'Google Calendar',
      description: 'Sync with your Google Calendar',
      features: ['Two-way sync', 'Conflict detection', 'Multiple calendars'],
      available: true
    },
    {
      id: 'outlook',
      name: 'Microsoft Outlook',
      description: 'Sync with your Outlook calendar',
      features: ['Two-way sync', 'Conflict detection'],
      available: false, // Not implemented yet
      comingSoon: true
    },
    {
      id: 'apple_calendar',
      name: 'Apple Calendar',
      description: 'Sync with your Apple Calendar via iCal',
      features: ['Import/Export', 'iCal compatibility'],
      available: false, // Not implemented yet
      comingSoon: true
    }
  ]
}

/**
 * API Route Configuration
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
