import { getAdminClient, supabase } from '@/lib/supabase'

/**
 * API endpoint for submitting artist/braider applications
 * This endpoint handles application form submissions from public users
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Get the current session from the request
    let user = null
    let token = null

    // Try to get the token from authorization header or X-Auth-Token header
    const authHeader = req.headers.authorization
    const xAuthToken = req.headers['x-auth-token']

    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7)
    } else if (xAuthToken) {
      token = xAuthToken
    }

    if (!token) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'You must be logged in to submit an application'
      })
    }

    // Verify the token with Supabase
    const { data: { user: sessionUser }, error: sessionError } = await supabase.auth.getUser(token)

    if (sessionError || !sessionUser) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'Invalid authentication token'
      })
    }

    user = sessionUser

    // Get admin client to bypass RLS policies
    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Failed to initialize admin client' })
    }

    // Get request body
    const {
      application_type,
      experience_years,
      portfolio_url,
      availability_preferences,
      service_specializations,
      previous_experience,
      references
    } = req.body

    // Validate required fields
    if (!application_type || !['artist', 'braider'].includes(application_type)) {
      return res.status(400).json({ 
        error: 'Invalid application type. Must be either "artist" or "braider"' 
      })
    }

    if (!experience_years || experience_years < 0) {
      return res.status(400).json({ 
        error: 'Experience years is required and must be a positive number' 
      })
    }

    if (!service_specializations || !Array.isArray(service_specializations) || service_specializations.length === 0) {
      return res.status(400).json({ 
        error: 'At least one service specialization is required' 
      })
    }

    if (!previous_experience || previous_experience.trim().length < 50) {
      return res.status(400).json({ 
        error: 'Previous experience description must be at least 50 characters' 
      })
    }

    // Check if user already has an application for this type
    const { data: existingApplication, error: checkError } = await adminClient
      .from('artist_braider_applications')
      .select('id, status')
      .eq('user_id', user.id)
      .eq('application_type', application_type)
      .single()

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error checking existing application:', checkError)
      return res.status(500).json({ error: 'Failed to check existing application' })
    }

    if (existingApplication) {
      // Update existing application instead of creating new one
      const { data: updatedApplication, error: updateError } = await adminClient
        .from('artist_braider_applications')
        .update({
          experience_years,
          portfolio_url: portfolio_url || null,
          availability_preferences: availability_preferences || {},
          service_specializations,
          previous_experience,
          professional_references: references || null,
          status: 'pending', // Reset to pending when resubmitted
          updated_at: new Date().toISOString()
        })
        .eq('id', existingApplication.id)
        .select()
        .single()

      if (updateError) {
        console.error('Error updating application:', updateError)
        return res.status(500).json({ error: 'Failed to update application' })
      }

      return res.status(200).json({
        success: true,
        message: 'Application updated successfully',
        application: updatedApplication,
        isUpdate: true
      })
    } else {
      // Create new application
      const { data: newApplication, error: createError } = await adminClient
        .from('artist_braider_applications')
        .insert([
          {
            user_id: user.id,
            application_type,
            experience_years,
            portfolio_url: portfolio_url || null,
            availability_preferences: availability_preferences || {},
            service_specializations,
            previous_experience,
            professional_references: references || null,
            status: 'pending'
          }
        ])
        .select()
        .single()

      if (createError) {
        console.error('Error creating application:', createError)
        return res.status(500).json({ error: 'Failed to create application' })
      }

      return res.status(201).json({
        success: true,
        message: 'Application submitted successfully',
        application: newApplication,
        isUpdate: false
      })
    }

  } catch (error) {
    console.error('Unexpected error in application submission:', error)
    return res.status(500).json({ error: 'An unexpected error occurred' })
  }
}
