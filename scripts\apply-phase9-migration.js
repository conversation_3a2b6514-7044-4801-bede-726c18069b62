/**
 * Apply Phase 9.1 Enhanced Authentication Migration
 * Ocean Soul Sparkles - Security & Compliance Enhancement
 */

import { createClient } from '@supabase/supabase-js'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import dotenv from 'dotenv'

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env.local') })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

async function applyMigration() {
  try {
    console.log('🚀 Starting Phase 9.1 Enhanced Authentication Migration...')
    
    // Read migration file
    const migrationPath = path.join(__dirname, '..', 'db', 'migrations', 'phase9_enhanced_auth.sql')
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')
    
    // Split into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && !stmt.startsWith('/*'))
    
    console.log(`📄 Found ${statements.length} SQL statements to execute.`)
    
    let successCount = 0
    let errorCount = 0
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i]
      console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`)
      
      try {
        const { error } = await supabase.rpc('execute_sql', {
          sql: statement + ';'
        })
        
        if (error) {
          console.error(`❌ Error in statement ${i + 1}:`, error.message)
          console.error('Statement:', statement.substring(0, 100) + '...')
          errorCount++
          
          // Continue with other statements unless it's a critical error
          if (error.code === 'PGRST202') {
            console.log('⚠️  Function not found, trying direct execution...')
            // Try direct execution for some statements
            continue
          }
        } else {
          successCount++
          console.log(`✅ Statement ${i + 1} executed successfully`)
        }
      } catch (err) {
        console.error(`❌ Exception in statement ${i + 1}:`, err.message)
        errorCount++
      }
    }
    
    console.log('\n📊 Migration Summary:')
    console.log(`✅ Successful statements: ${successCount}`)
    console.log(`❌ Failed statements: ${errorCount}`)
    
    if (errorCount === 0) {
      console.log('🎉 Phase 9.1 Enhanced Authentication Migration completed successfully!')
    } else {
      console.log('⚠️  Migration completed with some errors. Please review the logs.')
    }
    
    // Verify some key tables were created
    console.log('\n🔍 Verifying migration...')
    await verifyMigration()
    
  } catch (error) {
    console.error('💥 Migration failed:', error)
    process.exit(1)
  }
}

async function verifyMigration() {
  const tablesToCheck = [
    'user_mfa_settings',
    'mfa_verification_logs',
    'webauthn_credentials',
    'biometric_auth_logs',
    'user_sessions',
    'device_fingerprints',
    'security_events',
    'social_login_accounts'
  ]
  
  for (const table of tablesToCheck) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('count(*)')
        .limit(1)
      
      if (error) {
        console.log(`❌ Table ${table}: ${error.message}`)
      } else {
        console.log(`✅ Table ${table}: Created successfully`)
      }
    } catch (err) {
      console.log(`❌ Table ${table}: ${err.message}`)
    }
  }
}

// Run the migration
applyMigration()
