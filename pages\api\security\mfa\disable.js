/**
 * MFA Disable API Endpoint for Ocean Soul Sparkles
 * Disables MFA for the authenticated user
 * 
 * Phase 9.1: Enhanced Authentication System
 */

import { withAdminAuth } from '@/lib/admin-auth'
import { mfaManager } from '@/lib/security/mfa-manager'

async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const userId = req.user.id

    // Check if MFA is enforced (cannot be disabled)
    const status = await mfaManager.getMFAStatus(userId)
    if (status.enforced) {
      return res.status(403).json({
        error: 'M<PERSON> cannot be disabled',
        message: 'Multi-factor authentication is enforced by your administrator'
      })
    }

    // Disable MFA
    const success = await mfaManager.disableMFA(userId)

    if (!success) {
      return res.status(500).json({
        error: 'Failed to disable <PERSON><PERSON>',
        message: 'An error occurred while disabling M<PERSON>'
      })
    }

    res.status(200).json({
      success: true,
      message: 'M<PERSON> disabled successfully'
    })

  } catch (error) {
    console.error('MFA disable error:', error)
    res.status(500).json({
      error: 'Failed to disable M<PERSON>',
      message: error.message
    })
  }
}

export default withAdminAuth(handler)
