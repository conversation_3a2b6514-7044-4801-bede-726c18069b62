/* Personalized Recommendations Styles - Phase 8: Advanced Customer Experience */

.personalizedRecommendations {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.5rem;
}

.subtitle {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.5;
}

/* No Recommendations */
.noRecommendations {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.noRecommendationsIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.noRecommendationsTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.noRecommendationsDescription {
  color: #666;
  line-height: 1.5;
}

/* Recommendations Grid */
.recommendationsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.recommendationCard {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.recommendationCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border-color: #4ECDC4;
}

/* Recommendation Header */
.recommendationHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.recommendationIcon {
  font-size: 2rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  color: white;
  flex-shrink: 0;
}

.recommendationMeta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.recommendationType {
  background: #4ECDC4;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.confidenceScore {
  background: #f8f9fa;
  color: #666;
  padding: 0.25rem 0.5rem;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Recommendation Content */
.recommendationContent {
  margin-bottom: 1.5rem;
}

.recommendationTitle {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.75rem;
  line-height: 1.3;
}

.recommendationDescription {
  color: #666;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.recommendationReason {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(78, 205, 196, 0.1);
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.reasonIcon {
  font-size: 1rem;
  color: #4ECDC4;
}

.reasonText {
  font-size: 0.9rem;
  color: #333;
  font-weight: 500;
}

/* Recommendation Details */
.recommendationDetails {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.detailItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
}

.detailIcon {
  font-size: 1rem;
  color: #4ECDC4;
  width: 20px;
  text-align: center;
}

.detailLabel {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
  min-width: 80px;
}

.detailValue {
  font-size: 0.9rem;
  color: #333;
  font-weight: 600;
}

/* Special Offer */
.specialOffer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.offerIcon {
  font-size: 1.2rem;
}

.offerText {
  font-size: 0.9rem;
  font-weight: 600;
}

/* Recommendation Actions */
.recommendationActions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.bookButton,
.saveButton,
.dismissButton {
  flex: 1;
  min-width: 100px;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.bookButton {
  background: #4ECDC4;
  color: white;
}

.bookButton:hover {
  background: #44A08D;
  transform: translateY(-1px);
}

.saveButton {
  background: #667eea;
  color: white;
}

.saveButton:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.dismissButton {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e9ecef;
}

.dismissButton:hover {
  background: #e9ecef;
  color: #333;
}

.bookButton:disabled,
.saveButton:disabled,
.dismissButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* View More */
.viewMore {
  text-align: center;
  margin-top: 2rem;
}

.viewMoreButton {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  background: transparent;
  color: #4ECDC4;
  border: 2px solid #4ECDC4;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.viewMoreButton:hover {
  background: #4ECDC4;
  color: white;
  transform: translateY(-1px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .personalizedRecommendations {
    padding: 0.5rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .recommendationsGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .recommendationCard {
    padding: 1rem;
  }

  .recommendationHeader {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .recommendationMeta {
    align-items: flex-start;
    flex-direction: row;
    gap: 0.5rem;
  }

  .recommendationIcon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
  }

  .recommendationTitle {
    font-size: 1.1rem;
  }

  .recommendationActions {
    flex-direction: column;
  }

  .bookButton,
  .saveButton,
  .dismissButton {
    flex: none;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .header {
    margin-bottom: 1.5rem;
  }

  .title {
    font-size: 1.3rem;
  }

  .subtitle {
    font-size: 0.9rem;
  }

  .recommendationCard {
    padding: 0.75rem;
  }

  .recommendationTitle {
    font-size: 1rem;
  }

  .recommendationDescription {
    font-size: 0.9rem;
  }

  .detailItem {
    padding: 0.25rem 0;
  }

  .detailLabel {
    min-width: 60px;
    font-size: 0.8rem;
  }

  .detailValue {
    font-size: 0.8rem;
  }

  .reasonText {
    font-size: 0.8rem;
  }

  .offerText {
    font-size: 0.8rem;
  }

  .noRecommendations {
    padding: 2rem 1rem;
  }

  .noRecommendationsIcon {
    font-size: 3rem;
  }

  .noRecommendationsTitle {
    font-size: 1.3rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .recommendationCard {
    background: #2d2d2d;
    color: #fff;
  }

  .title,
  .recommendationTitle,
  .noRecommendationsTitle {
    color: #fff;
  }

  .subtitle,
  .recommendationDescription,
  .noRecommendationsDescription {
    color: #ccc;
  }

  .detailLabel {
    color: #aaa;
  }

  .detailValue,
  .reasonText {
    color: #fff;
  }

  .recommendationReason {
    background: rgba(78, 205, 196, 0.2);
  }

  .confidenceScore {
    background: #3d3d3d;
    color: #ccc;
  }

  .dismissButton {
    background: #3d3d3d;
    color: #ccc;
    border-color: #444;
  }

  .dismissButton:hover {
    background: #444;
    color: #fff;
  }

  .viewMoreButton {
    color: #4ECDC4;
    border-color: #4ECDC4;
  }

  .viewMoreButton:hover {
    background: #4ECDC4;
    color: #fff;
  }
}
