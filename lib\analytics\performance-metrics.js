/**
 * Performance Metrics Calculation Engine for Ocean Soul Sparkles
 * Provides comprehensive business intelligence and KPI calculations
 */

/**
 * Artist Performance Analyzer
 */
export class ArtistPerformanceAnalyzer {
  constructor() {
    this.metrics = {};
  }

  calculateArtistMetrics(artistData, bookingData, revenueData, timeframe = 'monthly') {
    const metrics = {
      artistId: artistData.id,
      name: artistData.name,
      timeframe,
      calculatedAt: new Date(),
      
      // Revenue Metrics
      revenue: this.calculateRevenueMetrics(revenueData),
      
      // Booking Metrics
      bookings: this.calculateBookingMetrics(bookingData),
      
      // Performance Metrics
      performance: this.calculatePerformanceMetrics(bookingData, revenueData),
      
      // Customer Metrics
      customers: this.calculateCustomerMetrics(bookingData),
      
      // Efficiency Metrics
      efficiency: this.calculateEfficiencyMetrics(bookingData, revenueData),
      
      // Growth Metrics
      growth: this.calculateGrowthMetrics(revenueData, bookingData, timeframe),
      
      // Quality Metrics
      quality: this.calculateQualityMetrics(bookingData),
      
      // Comparative Metrics
      comparative: this.calculateComparativeMetrics(artistData, bookingData, revenueData)
    };

    return metrics;
  }

  calculateRevenueMetrics(revenueData) {
    const totalRevenue = revenueData.reduce((sum, item) => sum + (item.amount || 0), 0);
    const averageRevenue = revenueData.length > 0 ? totalRevenue / revenueData.length : 0;
    
    // Calculate revenue by service type
    const revenueByService = {};
    revenueData.forEach(item => {
      const service = item.service || 'Unknown';
      revenueByService[service] = (revenueByService[service] || 0) + (item.amount || 0);
    });

    // Calculate monthly revenue trend
    const monthlyRevenue = this.groupByMonth(revenueData, 'date', 'amount');
    const revenueGrowthRate = this.calculateGrowthRate(Object.values(monthlyRevenue));

    return {
      total: totalRevenue,
      average: averageRevenue,
      byService: revenueByService,
      monthly: monthlyRevenue,
      growthRate: revenueGrowthRate,
      highestMonth: this.findHighestValue(monthlyRevenue),
      lowestMonth: this.findLowestValue(monthlyRevenue)
    };
  }

  calculateBookingMetrics(bookingData) {
    const totalBookings = bookingData.length;
    const completedBookings = bookingData.filter(b => b.status === 'completed').length;
    const cancelledBookings = bookingData.filter(b => b.status === 'cancelled').length;
    const noShowBookings = bookingData.filter(b => b.status === 'no_show').length;

    const completionRate = totalBookings > 0 ? (completedBookings / totalBookings) * 100 : 0;
    const cancellationRate = totalBookings > 0 ? (cancelledBookings / totalBookings) * 100 : 0;
    const noShowRate = totalBookings > 0 ? (noShowBookings / totalBookings) * 100 : 0;

    // Calculate booking frequency
    const bookingsByMonth = this.groupByMonth(bookingData, 'date');
    const averageBookingsPerMonth = Object.values(bookingsByMonth).reduce((sum, count) => sum + count, 0) / Object.keys(bookingsByMonth).length || 0;

    // Calculate peak booking times
    const bookingsByHour = this.groupByHour(bookingData, 'date');
    const peakHours = this.findPeakHours(bookingsByHour);

    // Calculate booking by service type
    const bookingsByService = {};
    bookingData.forEach(booking => {
      const service = booking.service || 'Unknown';
      bookingsByService[service] = (bookingsByService[service] || 0) + 1;
    });

    return {
      total: totalBookings,
      completed: completedBookings,
      cancelled: cancelledBookings,
      noShow: noShowBookings,
      completionRate,
      cancellationRate,
      noShowRate,
      averagePerMonth: averageBookingsPerMonth,
      byService: bookingsByService,
      byMonth: bookingsByMonth,
      peakHours,
      bookingTrend: this.calculateGrowthRate(Object.values(bookingsByMonth))
    };
  }

  calculatePerformanceMetrics(bookingData, revenueData) {
    const revenuePerBooking = bookingData.length > 0 ? 
      revenueData.reduce((sum, item) => sum + (item.amount || 0), 0) / bookingData.length : 0;

    // Calculate utilization rate (assuming 8-hour workday)
    const workingHoursPerDay = 8;
    const workingDaysPerMonth = 22;
    const totalAvailableHours = workingHoursPerMonth * workingDaysPerMonth;
    
    const bookedHours = bookingData.reduce((sum, booking) => {
      const duration = booking.duration || 1; // Default 1 hour if not specified
      return sum + duration;
    }, 0);

    const utilizationRate = totalAvailableHours > 0 ? (bookedHours / totalAvailableHours) * 100 : 0;

    // Calculate repeat customer rate
    const customerBookings = {};
    bookingData.forEach(booking => {
      const customerId = booking.customerId;
      customerBookings[customerId] = (customerBookings[customerId] || 0) + 1;
    });

    const repeatCustomers = Object.values(customerBookings).filter(count => count > 1).length;
    const totalCustomers = Object.keys(customerBookings).length;
    const repeatCustomerRate = totalCustomers > 0 ? (repeatCustomers / totalCustomers) * 100 : 0;

    return {
      revenuePerBooking,
      utilizationRate,
      repeatCustomerRate,
      averageBookingDuration: this.calculateAverageBookingDuration(bookingData),
      productivityScore: this.calculateProductivityScore(revenuePerBooking, utilizationRate, repeatCustomerRate)
    };
  }

  calculateCustomerMetrics(bookingData) {
    const customerData = {};
    
    bookingData.forEach(booking => {
      const customerId = booking.customerId;
      if (!customerData[customerId]) {
        customerData[customerId] = {
          bookings: 0,
          totalSpent: 0,
          firstBooking: booking.date,
          lastBooking: booking.date,
          services: new Set()
        };
      }
      
      customerData[customerId].bookings++;
      customerData[customerId].totalSpent += booking.amount || 0;
      customerData[customerId].services.add(booking.service);
      
      if (new Date(booking.date) < new Date(customerData[customerId].firstBooking)) {
        customerData[customerId].firstBooking = booking.date;
      }
      if (new Date(booking.date) > new Date(customerData[customerId].lastBooking)) {
        customerData[customerId].lastBooking = booking.date;
      }
    });

    const customers = Object.values(customerData);
    const totalCustomers = customers.length;
    const averageLifetimeValue = customers.reduce((sum, c) => sum + c.totalSpent, 0) / totalCustomers || 0;
    const averageBookingsPerCustomer = customers.reduce((sum, c) => sum + c.bookings, 0) / totalCustomers || 0;

    // Calculate customer retention
    const retentionPeriods = this.calculateCustomerRetention(customers);

    return {
      total: totalCustomers,
      averageLifetimeValue,
      averageBookingsPerCustomer,
      retention: retentionPeriods,
      newCustomersThisMonth: this.countNewCustomersThisMonth(customers),
      topCustomers: this.getTopCustomers(customers, 5)
    };
  }

  calculateEfficiencyMetrics(bookingData, revenueData) {
    const totalRevenue = revenueData.reduce((sum, item) => sum + (item.amount || 0), 0);
    const totalBookings = bookingData.length;
    const totalHours = bookingData.reduce((sum, booking) => sum + (booking.duration || 1), 0);

    return {
      revenuePerHour: totalHours > 0 ? totalRevenue / totalHours : 0,
      bookingsPerDay: this.calculateBookingsPerDay(bookingData),
      averageServiceTime: totalBookings > 0 ? totalHours / totalBookings : 0,
      efficiencyScore: this.calculateEfficiencyScore(totalRevenue, totalHours, totalBookings)
    };
  }

  calculateGrowthMetrics(revenueData, bookingData, timeframe) {
    const revenueByPeriod = this.groupByPeriod(revenueData, 'date', 'amount', timeframe);
    const bookingsByPeriod = this.groupByPeriod(bookingData, 'date', null, timeframe);

    const revenueGrowth = this.calculatePeriodOverPeriodGrowth(revenueByPeriod);
    const bookingGrowth = this.calculatePeriodOverPeriodGrowth(bookingsByPeriod);

    return {
      revenue: revenueGrowth,
      bookings: bookingGrowth,
      trend: this.determineTrend(revenueGrowth.rate, bookingGrowth.rate),
      forecast: this.generateSimpleForecast(revenueByPeriod, 3) // 3 periods ahead
    };
  }

  calculateQualityMetrics(bookingData) {
    const ratingsData = bookingData.filter(b => b.rating !== undefined);
    const averageRating = ratingsData.length > 0 ? 
      ratingsData.reduce((sum, b) => sum + b.rating, 0) / ratingsData.length : 0;

    const ratingDistribution = {};
    ratingsData.forEach(booking => {
      const rating = Math.floor(booking.rating);
      ratingDistribution[rating] = (ratingDistribution[rating] || 0) + 1;
    });

    const highRatings = ratingsData.filter(b => b.rating >= 4).length;
    const satisfactionRate = ratingsData.length > 0 ? (highRatings / ratingsData.length) * 100 : 0;

    return {
      averageRating,
      satisfactionRate,
      ratingDistribution,
      totalRatings: ratingsData.length,
      qualityScore: this.calculateQualityScore(averageRating, satisfactionRate)
    };
  }

  calculateComparativeMetrics(artistData, bookingData, revenueData) {
    // This would typically compare against other artists or industry benchmarks
    // For now, we'll provide relative performance indicators
    
    const totalRevenue = revenueData.reduce((sum, item) => sum + (item.amount || 0), 0);
    const totalBookings = bookingData.length;
    const averageRating = this.calculateAverageRating(bookingData);

    return {
      revenuePercentile: this.calculatePercentile(totalRevenue, 'revenue'),
      bookingPercentile: this.calculatePercentile(totalBookings, 'bookings'),
      ratingPercentile: this.calculatePercentile(averageRating, 'rating'),
      overallPerformanceScore: this.calculateOverallPerformanceScore(totalRevenue, totalBookings, averageRating)
    };
  }

  // Helper methods
  groupByMonth(data, dateField, valueField = null) {
    const grouped = {};
    data.forEach(item => {
      const date = new Date(item[dateField]);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      
      if (valueField) {
        grouped[monthKey] = (grouped[monthKey] || 0) + (item[valueField] || 0);
      } else {
        grouped[monthKey] = (grouped[monthKey] || 0) + 1;
      }
    });
    return grouped;
  }

  groupByHour(data, dateField) {
    const grouped = {};
    data.forEach(item => {
      const hour = new Date(item[dateField]).getHours();
      grouped[hour] = (grouped[hour] || 0) + 1;
    });
    return grouped;
  }

  groupByPeriod(data, dateField, valueField, timeframe) {
    // Implementation depends on timeframe (daily, weekly, monthly, yearly)
    return this.groupByMonth(data, dateField, valueField); // Simplified to monthly for now
  }

  calculateGrowthRate(values) {
    if (values.length < 2) return 0;
    const firstValue = values[0];
    const lastValue = values[values.length - 1];
    return firstValue > 0 ? ((lastValue - firstValue) / firstValue) * 100 : 0;
  }

  calculatePeriodOverPeriodGrowth(periodData) {
    const periods = Object.keys(periodData).sort();
    if (periods.length < 2) return { rate: 0, trend: 'insufficient_data' };

    const currentPeriod = periodData[periods[periods.length - 1]];
    const previousPeriod = periodData[periods[periods.length - 2]];
    
    const growthRate = previousPeriod > 0 ? ((currentPeriod - previousPeriod) / previousPeriod) * 100 : 0;
    
    return {
      rate: growthRate,
      current: currentPeriod,
      previous: previousPeriod,
      trend: growthRate > 5 ? 'growing' : growthRate < -5 ? 'declining' : 'stable'
    };
  }

  findHighestValue(data) {
    const entries = Object.entries(data);
    return entries.reduce((max, [key, value]) => value > max.value ? { key, value } : max, { key: null, value: 0 });
  }

  findLowestValue(data) {
    const entries = Object.entries(data);
    return entries.reduce((min, [key, value]) => value < min.value ? { key, value } : min, { key: null, value: Infinity });
  }

  findPeakHours(hourlyData) {
    const sortedHours = Object.entries(hourlyData)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([hour, count]) => ({ hour: parseInt(hour), count }));
    
    return sortedHours;
  }

  calculateAverageBookingDuration(bookingData) {
    const durationsWithData = bookingData.filter(b => b.duration);
    return durationsWithData.length > 0 ? 
      durationsWithData.reduce((sum, b) => sum + b.duration, 0) / durationsWithData.length : 1;
  }

  calculateProductivityScore(revenuePerBooking, utilizationRate, repeatCustomerRate) {
    // Weighted score combining multiple factors
    const revenueScore = Math.min(revenuePerBooking / 100, 1); // Normalize to max of 1
    const utilizationScore = utilizationRate / 100;
    const retentionScore = repeatCustomerRate / 100;
    
    return (revenueScore * 0.4 + utilizationScore * 0.3 + retentionScore * 0.3) * 100;
  }

  calculateCustomerRetention(customers) {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const ninetyDaysAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);

    const activeIn30Days = customers.filter(c => new Date(c.lastBooking) >= thirtyDaysAgo).length;
    const activeIn90Days = customers.filter(c => new Date(c.lastBooking) >= ninetyDaysAgo).length;

    return {
      thirtyDay: customers.length > 0 ? (activeIn30Days / customers.length) * 100 : 0,
      ninetyDay: customers.length > 0 ? (activeIn90Days / customers.length) * 100 : 0
    };
  }

  countNewCustomersThisMonth(customers) {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    
    return customers.filter(c => new Date(c.firstBooking) >= startOfMonth).length;
  }

  getTopCustomers(customers, limit) {
    return customers
      .sort((a, b) => b.totalSpent - a.totalSpent)
      .slice(0, limit)
      .map(c => ({
        customerId: c.customerId,
        totalSpent: c.totalSpent,
        bookings: c.bookings,
        averageSpend: c.totalSpent / c.bookings
      }));
  }

  calculateBookingsPerDay(bookingData) {
    const bookingsByDate = {};
    bookingData.forEach(booking => {
      const date = new Date(booking.date).toDateString();
      bookingsByDate[date] = (bookingsByDate[date] || 0) + 1;
    });
    
    const totalDays = Object.keys(bookingsByDate).length;
    const totalBookings = Object.values(bookingsByDate).reduce((sum, count) => sum + count, 0);
    
    return totalDays > 0 ? totalBookings / totalDays : 0;
  }

  calculateEfficiencyScore(totalRevenue, totalHours, totalBookings) {
    const revenuePerHour = totalHours > 0 ? totalRevenue / totalHours : 0;
    const bookingsPerHour = totalHours > 0 ? totalBookings / totalHours : 0;
    
    // Normalize and combine metrics (this would be calibrated based on industry standards)
    const revenueScore = Math.min(revenuePerHour / 50, 1); // Assuming $50/hour as benchmark
    const volumeScore = Math.min(bookingsPerHour / 2, 1); // Assuming 2 bookings/hour as benchmark
    
    return (revenueScore * 0.6 + volumeScore * 0.4) * 100;
  }

  determineTrend(revenueGrowth, bookingGrowth) {
    if (revenueGrowth > 10 && bookingGrowth > 10) return 'strong_growth';
    if (revenueGrowth > 0 && bookingGrowth > 0) return 'moderate_growth';
    if (revenueGrowth < -10 || bookingGrowth < -10) return 'declining';
    return 'stable';
  }

  generateSimpleForecast(historicalData, periodsAhead) {
    const values = Object.values(historicalData);
    if (values.length < 3) return [];
    
    // Simple linear trend forecast
    const trend = this.calculateGrowthRate(values) / 100;
    const lastValue = values[values.length - 1];
    
    const forecast = [];
    for (let i = 1; i <= periodsAhead; i++) {
      forecast.push(lastValue * Math.pow(1 + trend, i));
    }
    
    return forecast;
  }

  calculateAverageRating(bookingData) {
    const ratingsData = bookingData.filter(b => b.rating !== undefined);
    return ratingsData.length > 0 ? 
      ratingsData.reduce((sum, b) => sum + b.rating, 0) / ratingsData.length : 0;
  }

  calculateQualityScore(averageRating, satisfactionRate) {
    return (averageRating / 5) * 0.6 + (satisfactionRate / 100) * 0.4;
  }

  calculatePercentile(value, metric) {
    // This would typically compare against a database of all artists
    // For now, return a placeholder percentile based on value ranges
    const benchmarks = {
      revenue: [1000, 5000, 10000, 20000, 50000],
      bookings: [10, 50, 100, 200, 500],
      rating: [3.0, 3.5, 4.0, 4.5, 4.8]
    };
    
    const benchmark = benchmarks[metric] || [];
    let percentile = 0;
    
    for (let i = 0; i < benchmark.length; i++) {
      if (value >= benchmark[i]) {
        percentile = ((i + 1) / benchmark.length) * 100;
      }
    }
    
    return Math.min(percentile, 95); // Cap at 95th percentile
  }

  calculateOverallPerformanceScore(revenue, bookings, rating) {
    const revenueScore = this.calculatePercentile(revenue, 'revenue') / 100;
    const bookingScore = this.calculatePercentile(bookings, 'bookings') / 100;
    const ratingScore = this.calculatePercentile(rating, 'rating') / 100;
    
    return (revenueScore * 0.4 + bookingScore * 0.3 + ratingScore * 0.3) * 100;
  }
}

/**
 * Business Intelligence Calculator
 */
export class BusinessIntelligenceCalculator {
  constructor() {
    this.kpis = {};
  }

  calculateBusinessKPIs(businessData) {
    return {
      financial: this.calculateFinancialKPIs(businessData),
      operational: this.calculateOperationalKPIs(businessData),
      customer: this.calculateCustomerKPIs(businessData),
      growth: this.calculateGrowthKPIs(businessData),
      efficiency: this.calculateEfficiencyKPIs(businessData)
    };
  }

  calculateFinancialKPIs(data) {
    const totalRevenue = data.revenue.reduce((sum, item) => sum + item.amount, 0);
    const totalExpenses = data.expenses.reduce((sum, item) => sum + item.amount, 0);
    const netProfit = totalRevenue - totalExpenses;
    const profitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;

    return {
      totalRevenue,
      totalExpenses,
      netProfit,
      profitMargin,
      averageOrderValue: this.calculateAverageOrderValue(data.bookings),
      revenueGrowthRate: this.calculateRevenueGrowthRate(data.revenue),
      costPerAcquisition: this.calculateCostPerAcquisition(data.marketing, data.customers)
    };
  }

  calculateOperationalKPIs(data) {
    return {
      bookingConversionRate: this.calculateBookingConversionRate(data.inquiries, data.bookings),
      averageServiceTime: this.calculateAverageServiceTime(data.bookings),
      utilizationRate: this.calculateUtilizationRate(data.bookings, data.availability),
      cancellationRate: this.calculateCancellationRate(data.bookings),
      noShowRate: this.calculateNoShowRate(data.bookings)
    };
  }

  calculateCustomerKPIs(data) {
    return {
      customerLifetimeValue: this.calculateCustomerLifetimeValue(data.customers),
      customerRetentionRate: this.calculateCustomerRetentionRate(data.customers),
      repeatCustomerRate: this.calculateRepeatCustomerRate(data.customers),
      customerSatisfactionScore: this.calculateCustomerSatisfactionScore(data.reviews),
      netPromoterScore: this.calculateNetPromoterScore(data.reviews)
    };
  }

  calculateGrowthKPIs(data) {
    return {
      monthOverMonthGrowth: this.calculateMonthOverMonthGrowth(data.revenue),
      customerAcquisitionRate: this.calculateCustomerAcquisitionRate(data.customers),
      marketShare: this.calculateMarketShare(data.revenue, data.marketData),
      brandAwareness: this.calculateBrandAwareness(data.marketing)
    };
  }

  calculateEfficiencyKPIs(data) {
    return {
      revenuePerEmployee: this.calculateRevenuePerEmployee(data.revenue, data.employees),
      costEfficiencyRatio: this.calculateCostEfficiencyRatio(data.revenue, data.expenses),
      resourceUtilization: this.calculateResourceUtilization(data.resources),
      processEfficiency: this.calculateProcessEfficiency(data.processes)
    };
  }

  // Helper methods for KPI calculations
  calculateAverageOrderValue(bookings) {
    const totalValue = bookings.reduce((sum, booking) => sum + (booking.amount || 0), 0);
    return bookings.length > 0 ? totalValue / bookings.length : 0;
  }

  calculateRevenueGrowthRate(revenueData) {
    // Implementation for revenue growth rate calculation
    const monthlyRevenue = this.groupRevenueByMonth(revenueData);
    const months = Object.keys(monthlyRevenue).sort();
    
    if (months.length < 2) return 0;
    
    const currentMonth = monthlyRevenue[months[months.length - 1]];
    const previousMonth = monthlyRevenue[months[months.length - 2]];
    
    return previousMonth > 0 ? ((currentMonth - previousMonth) / previousMonth) * 100 : 0;
  }

  groupRevenueByMonth(revenueData) {
    const grouped = {};
    revenueData.forEach(item => {
      const date = new Date(item.date);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      grouped[monthKey] = (grouped[monthKey] || 0) + item.amount;
    });
    return grouped;
  }

  // Additional helper methods would be implemented here for each KPI calculation
  // This is a foundation that can be extended based on specific business requirements
}
