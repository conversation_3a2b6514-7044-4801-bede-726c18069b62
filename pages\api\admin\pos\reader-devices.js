/**
 * Reader Devices API - Detect and manage Square Reader device availability
 * Handles device detection, status monitoring, and compatibility checking
 */

import { supabaseAdmin } from '@/lib/supabase-admin'

export default async function handler(req, res) {
  // Generate unique request ID for tracking
  const requestId = `reader_devices_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`
  
  console.log(`[${requestId}] Reader Devices API request: ${req.method}`)

  // Only allow GET method for device detection
  if (req.method !== 'GET') {
    return res.status(405).json({ 
      error: 'Method not allowed',
      message: `${req.method} method is not supported for this endpoint`
    })
  }

  try {
    // Verify admin authentication
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Valid admin token required'
      })
    }

    const token = authHeader.split(' ')[1]
    
    // Verify token with Supabase
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token)
    
    if (authError || !user) {
      console.error(`[${requestId}] Authentication failed:`, authError)
      return res.status(401).json({
        error: 'Authentication failed',
        message: 'Invalid or expired token'
      })
    }

    // Get Square configuration
    const squareApplicationId = process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID
    const squareEnvironment = process.env.SQUARE_ENVIRONMENT || 'sandbox'

    if (!squareApplicationId) {
      return res.status(500).json({
        error: 'Configuration error',
        message: 'Square application ID not configured'
      })
    }

    // Detect available reader devices based on user agent and capabilities
    const userAgent = req.headers['user-agent'] || ''
    const devices = await detectReaderDevices(requestId, userAgent, squareEnvironment)

    console.log(`[${requestId}] Detected ${devices.length} reader device(s)`)

    return res.status(200).json({
      success: true,
      devices: devices,
      count: devices.length,
      environment: squareEnvironment,
      message: `Found ${devices.length} compatible reader device(s)`
    })

  } catch (error) {
    console.error(`[${requestId}] Reader devices API error:`, error)
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to detect reader devices',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

/**
 * Detect available Square Reader devices based on platform and capabilities
 */
async function detectReaderDevices(requestId, userAgent, environment) {
  const devices = []

  try {
    console.log(`[${requestId}] Detecting reader devices for user agent: ${userAgent}`)

    // Detect platform
    const isAndroid = /Android/i.test(userAgent)
    const isIOS = /iPad|iPhone|iPod/.test(userAgent)
    const isMobile = isAndroid || isIOS

    if (!isMobile) {
      console.log(`[${requestId}] Non-mobile platform detected, no reader devices available`)
      return devices
    }

    // Check for Square POS app availability (simulated - actual detection would require native capabilities)
    const hasSquarePOS = await checkSquarePOSAvailability(requestId, isAndroid, isIOS)

    if (!hasSquarePOS) {
      console.log(`[${requestId}] Square POS app not detected`)
      return devices
    }

    // Add platform-specific reader devices
    if (isAndroid) {
      devices.push(...getAndroidReaderDevices(environment))
    }

    if (isIOS) {
      devices.push(...getIOSReaderDevices(environment))
    }

    console.log(`[${requestId}] Successfully detected ${devices.length} reader devices`)
    return devices

  } catch (error) {
    console.error(`[${requestId}] Error detecting reader devices:`, error)
    return devices
  }
}

/**
 * Check if Square POS app is available (simulated check)
 * In a real implementation, this would use native device capabilities
 */
async function checkSquarePOSAvailability(requestId, isAndroid, isIOS) {
  try {
    // Simulate Square POS app detection
    // In production, this would check if the app is installed and accessible
    
    if (isAndroid) {
      // Check for Android Square POS app
      // This would typically use Android intent checking
      return true // Assume available for demo
    }

    if (isIOS) {
      // Check for iOS Square POS app
      // This would typically use iOS URL scheme checking
      return true // Assume available for demo
    }

    return false
  } catch (error) {
    console.error(`[${requestId}] Error checking Square POS availability:`, error)
    return false
  }
}

/**
 * Get available Android reader devices
 */
function getAndroidReaderDevices(environment) {
  const devices = [
    {
      id: 'android_contactless_reader',
      name: 'Square Contactless Reader',
      type: 'contactless',
      description: 'Contactless and chip card payments',
      status: 'Available',
      platform: 'android',
      capabilities: ['contactless', 'chip', 'magstripe'],
      environment: environment,
      icon: '📲',
      supportedTenderTypes: [
        'com.squareup.pos.TENDER_CARD',
        'com.squareup.pos.TENDER_CARD_ON_FILE'
      ]
    },
    {
      id: 'android_tap_to_pay',
      name: 'Tap to Pay on Android',
      type: 'tap_to_pay',
      description: 'Built-in NFC contactless payments',
      status: 'Available',
      platform: 'android',
      capabilities: ['contactless', 'nfc'],
      environment: environment,
      icon: '📱',
      supportedTenderTypes: [
        'com.squareup.pos.TENDER_CARD'
      ]
    },
    {
      id: 'android_square_stand',
      name: 'Square Stand',
      type: 'square_stand',
      description: 'All-in-one payment solution',
      status: 'Available',
      platform: 'android',
      capabilities: ['contactless', 'chip', 'magstripe', 'cash'],
      environment: environment,
      icon: '🖥️',
      supportedTenderTypes: [
        'com.squareup.pos.TENDER_CARD',
        'com.squareup.pos.TENDER_CARD_ON_FILE',
        'com.squareup.pos.TENDER_CASH'
      ]
    }
  ]

  return devices
}

/**
 * Get available iOS reader devices
 */
function getIOSReaderDevices(environment) {
  const devices = [
    {
      id: 'ios_contactless_reader',
      name: 'Square Contactless Reader',
      type: 'contactless',
      description: 'Contactless and chip card payments',
      status: 'Available',
      platform: 'ios',
      capabilities: ['contactless', 'chip', 'magstripe'],
      environment: environment,
      icon: '📲',
      supportedTenderTypes: [
        'CREDIT_CARD',
        'CARD_ON_FILE'
      ]
    },
    {
      id: 'ios_tap_to_pay',
      name: 'Tap to Pay on iPhone',
      type: 'tap_to_pay',
      description: 'Built-in NFC contactless payments',
      status: 'Available',
      platform: 'ios',
      capabilities: ['contactless', 'nfc'],
      environment: environment,
      icon: '📱',
      supportedTenderTypes: [
        'CREDIT_CARD'
      ]
    },
    {
      id: 'ios_square_stand',
      name: 'Square Stand',
      type: 'square_stand',
      description: 'All-in-one payment solution',
      status: 'Available',
      platform: 'ios',
      capabilities: ['contactless', 'chip', 'magstripe', 'cash'],
      environment: environment,
      icon: '🖥️',
      supportedTenderTypes: [
        'CREDIT_CARD',
        'CARD_ON_FILE',
        'CASH'
      ]
    }
  ]

  return devices
}

/**
 * Store reader device usage statistics
 */
async function logReaderDeviceUsage(requestId, deviceId, userAgent) {
  try {
    const { error } = await supabaseAdmin
      .from('reader_device_usage')
      .insert([{
        device_id: deviceId,
        user_agent: userAgent,
        detected_at: new Date().toISOString(),
        request_id: requestId
      }])

    if (error) {
      console.error(`[${requestId}] Error logging device usage:`, error)
    }
  } catch (error) {
    console.error(`[${requestId}] Error in device usage logging:`, error)
  }
}
