#!/usr/bin/env node

/**
 * Critical Onboarding Issues Fix Script
 * Addresses database relationship and email service failures
 */

import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'
import fs from 'fs'

// Load environment variables
config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      persistSession: false,
      autoRefreshToken: false
    }
  }
)

async function fixCriticalOnboardingIssues() {
  console.log('🚨 Fixing Critical Artist/Braider Onboarding Issues\n')

  let allFixed = true
  const results = []

  // Fix 1: Database Relationship Issues
  console.log('1. Fixing database relationship issues...')
  try {
    const relationshipSQL = fs.readFileSync('supabase/migrations/fix_application_tokens_relationships.sql', 'utf8')
    
    // Split SQL into individual statements for better error handling
    const statements = relationshipSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

    for (const statement of statements) {
      if (statement.includes('DO $$') || statement.includes('RAISE NOTICE')) {
        // Skip notice blocks for now
        continue
      }
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement + ';' })
        if (error && !error.message.includes('already exists') && !error.message.includes('does not exist')) {
          console.warn(`⚠️  SQL warning: ${error.message}`)
        }
      } catch (sqlError) {
        console.warn(`⚠️  SQL execution warning: ${sqlError.message}`)
      }
    }

    console.log('✅ Database relationship fixes applied')
    results.push({ fix: 'database_relationships', success: true })
  } catch (error) {
    console.error('❌ Database relationship fix failed:', error.message)
    allFixed = false
    results.push({ fix: 'database_relationships', success: false, error: error.message })
  }

  // Fix 2: Test Token Retrieval
  console.log('\n2. Testing token retrieval for user...')
  try {
    const testUserId = 'd29e3ede-8e90-49ee-8f10-af8539a60ccd'
    
    // Try direct query first
    const { data: tokens, error: tokenError } = await supabase
      .from('application_tokens')
      .select('*')
      .eq('user_id', testUserId)

    if (tokenError) {
      console.error('❌ Token retrieval failed:', tokenError.message)
      allFixed = false
      results.push({ fix: 'token_retrieval', success: false, error: tokenError.message })
    } else {
      console.log(`✅ Token retrieval working (found ${tokens?.length || 0} tokens)`)
      results.push({ fix: 'token_retrieval', success: true, count: tokens?.length || 0 })
    }
  } catch (error) {
    console.error('❌ Token retrieval test failed:', error.message)
    allFixed = false
    results.push({ fix: 'token_retrieval', success: false, error: error.message })
  }

  // Fix 3: Test Email Configuration
  console.log('\n3. Testing email configuration...')
  try {
    const { getEmailConfig } = await import('../lib/email-service-manager.js')
    const emailConfig = await getEmailConfig()
    
    const hasGmail = !!(emailConfig.gmail.user && emailConfig.gmail.password)
    const hasWorkspace = !!(emailConfig.workspace.user && emailConfig.workspace.password)
    const hasOneSignal = !!(emailConfig.onesignal.appId && emailConfig.onesignal.restApiKey)
    
    console.log(`   Gmail SMTP: ${hasGmail ? '✅ Configured' : '❌ Not configured'}`)
    console.log(`   Workspace SMTP: ${hasWorkspace ? '✅ Configured' : '❌ Not configured'}`)
    console.log(`   OneSignal: ${hasOneSignal ? '✅ Configured' : '❌ Not configured'}`)
    
    if (hasGmail || hasWorkspace || hasOneSignal) {
      console.log('✅ At least one email service is configured')
      results.push({ fix: 'email_configuration', success: true, services: { gmail: hasGmail, workspace: hasWorkspace, onesignal: hasOneSignal } })
    } else {
      console.log('⚠️  No email services are configured')
      results.push({ fix: 'email_configuration', success: false, error: 'No email services configured' })
    }
  } catch (error) {
    console.error('❌ Email configuration test failed:', error.message)
    results.push({ fix: 'email_configuration', success: false, error: error.message })
  }

  // Fix 4: Test Nodemailer Import
  console.log('\n4. Testing nodemailer import fix...')
  try {
    const nodemailer = await import('nodemailer')
    const createTransporter = nodemailer.default?.createTransporter || nodemailer.createTransporter
    
    if (!createTransporter) {
      throw new Error('createTransporter not found in nodemailer')
    }

    // Test creating a transporter (don't send email)
    const testTransporter = createTransporter({
      service: 'gmail',
      auth: {
        user: '<EMAIL>',
        pass: 'test-password'
      }
    })

    if (testTransporter) {
      console.log('✅ Nodemailer import and createTransporter working')
      results.push({ fix: 'nodemailer_import', success: true })
    }
  } catch (error) {
    console.error('❌ Nodemailer import test failed:', error.message)
    allFixed = false
    results.push({ fix: 'nodemailer_import', success: false, error: error.message })
  }

  // Fix 5: Test Token Generation
  console.log('\n5. Testing token generation...')
  try {
    const { data: generatedToken, error: generateError } = await supabase
      .rpc('generate_application_token')

    if (generateError) {
      console.error('❌ Token generation failed:', generateError.message)
      allFixed = false
      results.push({ fix: 'token_generation', success: false, error: generateError.message })
    } else if (!generatedToken) {
      console.error('❌ Token generation returned null')
      allFixed = false
      results.push({ fix: 'token_generation', success: false, error: 'Token generation returned null' })
    } else {
      console.log(`✅ Token generation working: ${generatedToken.substring(0, 8)}...`)
      results.push({ fix: 'token_generation', success: true })
    }
  } catch (error) {
    console.error('❌ Token generation test failed:', error.message)
    allFixed = false
    results.push({ fix: 'token_generation', success: false, error: error.message })
  }

  // Fix 6: Test Token Validation Function
  console.log('\n6. Testing token validation function...')
  try {
    const { data: validationResult, error: validationError } = await supabase
      .rpc('validate_application_token', { token_value: 'test-invalid-token' })

    if (validationError) {
      console.error('❌ Token validation function failed:', validationError.message)
      allFixed = false
      results.push({ fix: 'token_validation_function', success: false, error: validationError.message })
    } else {
      console.log('✅ Token validation function working')
      results.push({ fix: 'token_validation_function', success: true })
    }
  } catch (error) {
    console.error('❌ Token validation function test failed:', error.message)
    allFixed = false
    results.push({ fix: 'token_validation_function', success: false, error: error.message })
  }

  // Summary
  console.log('\n📊 Fix Summary:')
  const successCount = results.filter(r => r.success).length
  const totalCount = results.length
  
  console.log(`   Successful fixes: ${successCount}/${totalCount}`)
  
  results.forEach(result => {
    const status = result.success ? '✅' : '❌'
    const error = result.error ? ` (${result.error})` : ''
    console.log(`   ${status} ${result.fix}${error}`)
  })

  console.log(`\n   Overall status: ${allFixed ? '✅ ALL ISSUES FIXED' : '❌ SOME ISSUES REMAIN'}`)

  if (!allFixed) {
    console.log('\n🔧 Manual Steps Required:')
    console.log('   1. Run the database migration SQL in Supabase SQL Editor')
    console.log('   2. Configure email credentials in /admin/secure-settings')
    console.log('   3. Test the complete onboarding flow')
    console.log('   4. Check Vercel environment variables if needed')
  } else {
    console.log('\n🎉 All critical issues fixed!')
    console.log('   The Artist/Braider onboarding system should now work correctly.')
    console.log('\n📋 Next Steps:')
    console.log('   1. Configure email credentials in /admin/secure-settings')
    console.log('   2. Test creating a new Artist/Braider user')
    console.log('   3. Verify email delivery and token validation')
    console.log('   4. Test the complete application workflow')
  }

  return allFixed
}

// Run the fix script
fixCriticalOnboardingIssues()
  .then(success => {
    if (success) {
      console.log('\n✅ Critical onboarding issues fix completed successfully!')
      process.exit(0)
    } else {
      console.log('\n❌ Critical onboarding issues fix completed with errors!')
      process.exit(1)
    }
  })
  .catch(error => {
    console.error('\n💥 Fix script error:', error)
    process.exit(1)
  })
