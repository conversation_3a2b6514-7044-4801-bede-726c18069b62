import { authenticateAdminRequest } from '@/lib/admin-auth'
import { createClient } from '@supabase/supabase-js'
import { hasBookingPermission, BOOKING_PERMISSIONS, getBookingQueryFilters } from '@/lib/artist-booking-permissions'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

/**
 * Enhanced Artist/Braider Dashboard API
 * Provides comprehensive dashboard data including bookings, availability, and analytics
 */
export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] Enhanced Artist Dashboard API called`)

  try {
    // Authenticate the request
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.authorized) {
      return res.status(401).json({ error: 'Unauthorized' })
    }

    const { user, role } = authResult

    // Verify user is artist or braider
    if (!['artist', 'braider'].includes(role)) {
      return res.status(403).json({ error: 'Access denied. Artist/Braider role required.' })
    }

    console.log(`[${requestId}] Fetching enhanced dashboard for ${role}: ${user.email}`)

    // Get artist profile
    const { data: artistProfile, error: profileError } = await supabase
      .from('artist_profiles')
      .select('*')
      .eq('user_id', user.id)
      .single()

    if (profileError && profileError.code !== 'PGRST116') {
      console.error(`[${requestId}] Error fetching artist profile:`, profileError)
      return res.status(500).json({ error: 'Failed to fetch profile' })
    }

    // Get booking query filters based on permissions
    const bookingFilters = getBookingQueryFilters(user.id, role)

    // Fetch upcoming bookings (next 7 days)
    const nextWeek = new Date()
    nextWeek.setDate(nextWeek.getDate() + 7)

    let upcomingBookingsQuery = supabase
      .from('bookings')
      .select(`
        id,
        start_time,
        end_time,
        status,
        location,
        notes,
        customers (
          name,
          email,
          phone
        ),
        services (
          name,
          color,
          duration
        )
      `)
      .gte('start_time', new Date().toISOString())
      .lte('start_time', nextWeek.toISOString())
      .neq('status', 'canceled')
      .order('start_time', { ascending: true })

    // Apply role-based filters
    if (bookingFilters.or) {
      upcomingBookingsQuery = upcomingBookingsQuery.or(bookingFilters.or)
    }

    const { data: upcomingBookings, error: bookingsError } = await upcomingBookingsQuery

    if (bookingsError) {
      console.error(`[${requestId}] Error fetching upcoming bookings:`, bookingsError)
    }

    // Fetch today's schedule
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    let todaysScheduleQuery = supabase
      .from('bookings')
      .select(`
        id,
        start_time,
        end_time,
        status,
        customers (name),
        services (name, color)
      `)
      .gte('start_time', today.toISOString().split('T')[0])
      .lt('start_time', tomorrow.toISOString().split('T')[0])
      .neq('status', 'canceled')
      .order('start_time', { ascending: true })

    if (bookingFilters.or) {
      todaysScheduleQuery = todaysScheduleQuery.or(bookingFilters.or)
    }

    const { data: todaysSchedule, error: scheduleError } = await todaysScheduleQuery

    if (scheduleError) {
      console.error(`[${requestId}] Error fetching today's schedule:`, scheduleError)
    }

    // Fetch recent payments (last 30 days) if user has permission
    let recentPayments = []
    if (hasBookingPermission(role, BOOKING_PERMISSIONS.VIEW_OWN_EARNINGS)) {
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const { data: payments, error: paymentsError } = await supabase
        .from('payments')
        .select(`
          id,
          amount,
          commission_amount,
          created_at,
          booking_id,
          bookings (
            services (name),
            customers (name)
          )
        `)
        .eq('artist_id', artistProfile?.id)
        .gte('created_at', thirtyDaysAgo.toISOString())
        .order('created_at', { ascending: false })
        .limit(10)

      if (paymentsError) {
        console.error(`[${requestId}] Error fetching recent payments:`, paymentsError)
      } else {
        recentPayments = payments || []
      }
    }

    // Calculate enhanced statistics
    const stats = await calculateEnhancedStats(supabase, artistProfile?.id, user.id, role, requestId)

    // Get availability status
    const availabilityStatus = getAvailabilityStatus(artistProfile, todaysSchedule)

    // Format response data
    const formattedUpcomingBookings = (upcomingBookings || []).map(booking => ({
      id: booking.id,
      start_time: booking.start_time,
      end_time: booking.end_time,
      status: booking.status,
      location: booking.location,
      customer_name: booking.customers?.name,
      customer_email: booking.customers?.email,
      customer_phone: booking.customers?.phone,
      service_name: booking.services?.name,
      service_color: booking.services?.color,
      service_duration: booking.services?.duration
    }))

    const formattedTodaysSchedule = (todaysSchedule || []).map(booking => ({
      id: booking.id,
      start_time: booking.start_time,
      end_time: booking.end_time,
      status: booking.status,
      customer_name: booking.customers?.name,
      service_name: booking.services?.name,
      service_color: booking.services?.color
    }))

    const formattedRecentPayments = recentPayments.map(payment => ({
      id: payment.id,
      amount: parseFloat(payment.amount || 0),
      commission_amount: parseFloat(payment.commission_amount || 0),
      created_at: payment.created_at,
      service_name: payment.bookings?.services?.name,
      customer_name: payment.bookings?.customers?.name
    }))

    console.log(`[${requestId}] Enhanced dashboard data fetched successfully`)

    return res.status(200).json({
      profile: artistProfile || {
        artist_name: user.email,
        display_name: user.email,
        bio: '',
        specializations: [],
        skill_level: 'intermediate',
        is_active: true,
        is_available_today: true
      },
      upcomingBookings: formattedUpcomingBookings,
      todaysSchedule: formattedTodaysSchedule,
      recentPayments: formattedRecentPayments,
      availabilityStatus,
      stats,
      permissions: {
        canViewEarnings: hasBookingPermission(role, BOOKING_PERMISSIONS.VIEW_OWN_EARNINGS),
        canManageAvailability: hasBookingPermission(role, BOOKING_PERMISSIONS.MANAGE_OWN_AVAILABILITY),
        canEditBookings: hasBookingPermission(role, BOOKING_PERMISSIONS.EDIT_OWN_BOOKINGS)
      }
    })

  } catch (error) {
    console.error(`[${requestId}] Unexpected error in enhanced artist dashboard:`, error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}

/**
 * Calculate enhanced statistics for the artist
 */
async function calculateEnhancedStats(supabase, artistId, userId, role, requestId) {
  const stats = {
    totalBookings: 0,
    totalEarnings: 0,
    averageRating: 0,
    completedServices: 0,
    thisWeekBookings: 0,
    thisMonthEarnings: 0,
    todaysBookings: 0,
    upcomingBookings: 0
  }

  try {
    if (!artistId) return stats

    // Get date ranges
    const today = new Date()
    const weekStart = new Date(today)
    weekStart.setDate(today.getDate() - today.getDay()) // Start of week
    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1)

    // Total bookings count
    const { count: totalBookingsCount } = await supabase
      .from('bookings')
      .select('*', { count: 'exact', head: true })
      .eq('assigned_artist_id', artistId)
      .neq('status', 'canceled')

    stats.totalBookings = totalBookingsCount || 0

    // This week's bookings
    const { count: thisWeekCount } = await supabase
      .from('bookings')
      .select('*', { count: 'exact', head: true })
      .eq('assigned_artist_id', artistId)
      .gte('start_time', weekStart.toISOString())
      .neq('status', 'canceled')

    stats.thisWeekBookings = thisWeekCount || 0

    // Today's bookings
    const { count: todaysCount } = await supabase
      .from('bookings')
      .select('*', { count: 'exact', head: true })
      .eq('assigned_artist_id', artistId)
      .gte('start_time', today.toISOString().split('T')[0])
      .lt('start_time', new Date(today.getTime() + 24*60*60*1000).toISOString().split('T')[0])
      .neq('status', 'canceled')

    stats.todaysBookings = todaysCount || 0

    // Upcoming bookings (next 7 days)
    const nextWeek = new Date()
    nextWeek.setDate(nextWeek.getDate() + 7)

    const { count: upcomingCount } = await supabase
      .from('bookings')
      .select('*', { count: 'exact', head: true })
      .eq('assigned_artist_id', artistId)
      .gte('start_time', new Date().toISOString())
      .lte('start_time', nextWeek.toISOString())
      .neq('status', 'canceled')

    stats.upcomingBookings = upcomingCount || 0

    // Earnings calculations (if permitted)
    if (hasBookingPermission(role, BOOKING_PERMISSIONS.VIEW_OWN_EARNINGS)) {
      // Total earnings
      const { data: allPayments } = await supabase
        .from('payments')
        .select('amount, commission_amount')
        .eq('artist_id', artistId)

      if (allPayments) {
        stats.totalEarnings = allPayments.reduce((sum, payment) => 
          sum + (parseFloat(payment.amount || 0) - parseFloat(payment.commission_amount || 0)), 0)
      }

      // This month's earnings
      const { data: monthPayments } = await supabase
        .from('payments')
        .select('amount, commission_amount')
        .eq('artist_id', artistId)
        .gte('created_at', monthStart.toISOString())

      if (monthPayments) {
        stats.thisMonthEarnings = monthPayments.reduce((sum, payment) => 
          sum + (parseFloat(payment.amount || 0) - parseFloat(payment.commission_amount || 0)), 0)
      }
    }

    // Completed services count
    const { count: completedCount } = await supabase
      .from('bookings')
      .select('*', { count: 'exact', head: true })
      .eq('assigned_artist_id', artistId)
      .eq('status', 'completed')

    stats.completedServices = completedCount || 0

  } catch (error) {
    console.error(`[${requestId}] Error calculating enhanced stats:`, error)
  }

  return stats
}

/**
 * Determine availability status based on profile and schedule
 */
function getAvailabilityStatus(profile, todaysSchedule) {
  if (!profile?.is_active) return 'inactive'
  if (!profile?.is_available_today) return 'unavailable'
  
  const maxBookings = profile?.max_daily_bookings || 8
  const currentBookings = todaysSchedule?.length || 0
  
  if (currentBookings >= maxBookings) return 'fully_booked'
  if (currentBookings >= maxBookings * 0.8) return 'busy'
  
  return 'available'
}
