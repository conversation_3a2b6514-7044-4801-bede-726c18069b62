/**
 * AI Insights Generation API Endpoint
 * Provides automated business intelligence and actionable recommendations
 */

import { authenticateAdminRequest } from '@/lib/admin-auth'
import { AIInsightsGenerator } from '@/lib/ai/insights-generator'

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ 
      success: false,
      error: 'Method not allowed. Use GET.' 
    })
  }

  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] AI Insights Generation API called`)

  try {
    // Authenticate admin user
    const user = await authenticateAdminRequest(req)
    if (!user) {
      console.log(`[${requestId}] Authentication failed`)
      return res.status(401).json({ 
        success: false,
        error: 'Unauthorized. Admin access required.' 
      })
    }

    console.log(`[${requestId}] Authenticated user: ${user.email} (${user.role})`)

    // Parse query parameters
    const { date, type = 'daily', format = 'json' } = req.query

    // Validate and parse date
    let targetDate = new Date()
    if (date) {
      targetDate = new Date(date)
      if (isNaN(targetDate.getTime())) {
        console.log(`[${requestId}] Invalid date format: ${date}`)
        return res.status(400).json({ 
          success: false,
          error: 'Invalid date format. Use ISO 8601 format (YYYY-MM-DD)' 
        })
      }
    }

    // Validate insight type
    const validTypes = ['daily', 'weekly', 'monthly']
    if (!validTypes.includes(type)) {
      console.log(`[${requestId}] Invalid insight type: ${type}`)
      return res.status(400).json({ 
        success: false,
        error: `Invalid insight type. Must be one of: ${validTypes.join(', ')}` 
      })
    }

    console.log(`[${requestId}] Generating ${type} insights for ${targetDate.toISOString().split('T')[0]}`)

    // Initialize insights generator
    const generator = new AIInsightsGenerator()
    
    const startTime = Date.now()
    let insights

    // Generate insights based on type
    switch (type) {
      case 'daily':
        insights = await generator.generateDailyInsights(targetDate)
        break
      case 'weekly':
        // For now, use daily insights - can be extended later
        insights = await generator.generateDailyInsights(targetDate)
        insights.type = 'weekly'
        insights.note = 'Weekly insights currently use daily analysis - full weekly analysis coming soon'
        break
      case 'monthly':
        // For now, use daily insights - can be extended later
        insights = await generator.generateDailyInsights(targetDate)
        insights.type = 'monthly'
        insights.note = 'Monthly insights currently use daily analysis - full monthly analysis coming soon'
        break
      default:
        insights = await generator.generateDailyInsights(targetDate)
    }

    const processingTime = Date.now() - startTime

    console.log(`[${requestId}] Insights generated in ${processingTime}ms`)

    // Prepare response
    const response = {
      success: true,
      requestId,
      processingTimeMs: processingTime,
      insights,
      metadata: {
        generatedAt: new Date().toISOString(),
        insightType: type,
        targetDate: targetDate.toISOString().split('T')[0],
        apiVersion: '1.0.0',
        dataQuality: insights.metadata?.confidenceLevel || 0.5
      }
    }

    // Log insights summary
    console.log(`[${requestId}] Insights summary:`, {
      recommendations: insights.recommendations?.length || 0,
      alerts: insights.alerts?.length || 0,
      totalBookings: insights.summary?.totalBookings || 0,
      totalRevenue: insights.summary?.totalRevenue || 0,
      confidenceLevel: insights.metadata?.confidenceLevel || 0
    })

    // Handle different response formats
    if (format === 'csv') {
      // Convert insights to CSV format for export
      const csvData = convertInsightsToCSV(insights)
      res.setHeader('Content-Type', 'text/csv')
      res.setHeader('Content-Disposition', `attachment; filename="insights-${targetDate.toISOString().split('T')[0]}.csv"`)
      return res.status(200).send(csvData)
    } else if (format === 'summary') {
      // Return only summary data for quick overview
      return res.status(200).json({
        success: true,
        requestId,
        summary: insights.summary,
        keyRecommendations: insights.recommendations?.slice(0, 3) || [],
        criticalAlerts: insights.alerts?.filter(a => a.severity === 'high') || [],
        metadata: response.metadata
      })
    }

    // Default JSON response
    res.status(200).json(response)

  } catch (error) {
    console.error(`[${requestId}] Insights generation API error:`, error)
    
    // Determine error type and status code
    let statusCode = 500
    let errorMessage = 'Internal server error during insights generation'
    
    if (error.message.includes('Failed to fetch')) {
      statusCode = 503
      errorMessage = 'Unable to fetch data from database for analysis'
    } else if (error.message.includes('Database error')) {
      statusCode = 503
      errorMessage = 'Database connection error'
    } else if (error.message.includes('Insufficient data')) {
      statusCode = 422
      errorMessage = 'Insufficient data available for meaningful insights'
    }

    res.status(statusCode).json({ 
      success: false,
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
      requestId,
      timestamp: new Date().toISOString(),
      suggestions: [
        'Ensure sufficient booking data exists for the target date',
        'Check database connectivity',
        'Try a different date range'
      ]
    })
  }
}

/**
 * Convert insights to CSV format
 * @param {Object} insights - Insights data
 * @returns {string} CSV formatted data
 */
function convertInsightsToCSV(insights) {
  const lines = []
  
  // Header
  lines.push('Ocean Soul Sparkles - Business Insights Report')
  lines.push(`Generated: ${insights.generatedAt}`)
  lines.push(`Date: ${insights.date}`)
  lines.push('')
  
  // Summary
  lines.push('EXECUTIVE SUMMARY')
  lines.push(`Total Bookings,${insights.summary?.totalBookings || 0}`)
  lines.push(`Completed Bookings,${insights.summary?.completedBookings || 0}`)
  lines.push(`Completion Rate,${insights.summary?.completionRate || 0}%`)
  lines.push(`Total Revenue,$${insights.summary?.totalRevenue || 0}`)
  lines.push(`Average Booking Value,$${insights.summary?.avgBookingValue || 0}`)
  lines.push(`Revenue Growth,${insights.summary?.revenueGrowth || 0}%`)
  lines.push('')
  
  // Recommendations
  if (insights.recommendations?.length > 0) {
    lines.push('RECOMMENDATIONS')
    lines.push('Priority,Category,Title,Description,Impact')
    insights.recommendations.forEach(rec => {
      lines.push(`${rec.priority},${rec.category},"${rec.title}","${rec.description}","${rec.impact}"`)
    })
    lines.push('')
  }
  
  // Alerts
  if (insights.alerts?.length > 0) {
    lines.push('ALERTS')
    lines.push('Severity,Category,Message,Recommendation')
    insights.alerts.forEach(alert => {
      lines.push(`${alert.severity},${alert.category},"${alert.message}","${alert.recommendation}"`)
    })
    lines.push('')
  }
  
  // Popular Services
  if (insights.bookingInsights?.popularServices?.length > 0) {
    lines.push('POPULAR SERVICES')
    lines.push('Service,Bookings,Percentage,Revenue,Average Value')
    insights.bookingInsights.popularServices.forEach(service => {
      lines.push(`"${service.service}",${service.count},${service.percentage}%,$${service.revenue || 0},$${service.avgValue || 0}`)
    })
  }
  
  return lines.join('\n')
}

/**
 * API Documentation
 * 
 * GET /api/ai/generate-insights
 * 
 * Query Parameters:
 * - date: Target date in YYYY-MM-DD format (optional, defaults to today)
 * - type: Insight type - 'daily', 'weekly', 'monthly' (optional, defaults to 'daily')
 * - format: Response format - 'json', 'csv', 'summary' (optional, defaults to 'json')
 * 
 * Examples:
 * - GET /api/ai/generate-insights
 * - GET /api/ai/generate-insights?date=2025-01-15
 * - GET /api/ai/generate-insights?date=2025-01-15&type=weekly
 * - GET /api/ai/generate-insights?date=2025-01-15&format=csv
 * - GET /api/ai/generate-insights?format=summary
 * 
 * Response (JSON format):
 * {
 *   "success": true,
 *   "requestId": "abc123",
 *   "processingTimeMs": 850,
 *   "insights": {
 *     "date": "2025-01-15",
 *     "generatedAt": "2025-01-15T08:30:00Z",
 *     "summary": {
 *       "totalBookings": 12,
 *       "completedBookings": 10,
 *       "completionRate": "83.3",
 *       "totalRevenue": 1250.00,
 *       "avgBookingValue": 125.00,
 *       "revenueGrowth": 15.5,
 *       "narrative": "Strong performance with excellent growth momentum"
 *     },
 *     "recommendations": [...],
 *     "alerts": [...],
 *     "bookingInsights": {...},
 *     "metadata": {...}
 *   },
 *   "metadata": {
 *     "generatedAt": "2025-01-15T08:30:00Z",
 *     "insightType": "daily",
 *     "targetDate": "2025-01-15",
 *     "apiVersion": "1.0.0",
 *     "dataQuality": 0.85
 *   }
 * }
 * 
 * Response (Summary format):
 * {
 *   "success": true,
 *   "requestId": "abc123",
 *   "summary": {...},
 *   "keyRecommendations": [...],
 *   "criticalAlerts": [...],
 *   "metadata": {...}
 * }
 * 
 * Response (CSV format):
 * Content-Type: text/csv
 * Content-Disposition: attachment; filename="insights-2025-01-15.csv"
 * 
 * Error Response:
 * {
 *   "success": false,
 *   "error": "Error message",
 *   "details": "Detailed error (development only)",
 *   "requestId": "abc123",
 *   "timestamp": "2025-01-15T08:30:00Z",
 *   "suggestions": [...]
 * }
 * 
 * Status Codes:
 * - 200: Success
 * - 400: Bad Request (invalid parameters)
 * - 401: Unauthorized (authentication required)
 * - 405: Method Not Allowed (use GET)
 * - 422: Unprocessable Entity (insufficient data)
 * - 503: Service Unavailable (database issues)
 * - 500: Internal Server Error
 * 
 * Authentication:
 * - Requires admin authentication
 * - Include JWT token in Authorization header: "Bearer <token>"
 * 
 * Rate Limiting:
 * - Results are cached for 30 minutes per date/type combination
 * - No specific rate limits, but analysis is computationally intensive
 * 
 * Data Requirements:
 * - Requires booking data for meaningful insights
 * - Confidence level decreases with limited data
 * - Minimum 5 bookings recommended for reliable insights
 */
