#!/usr/bin/env node

/**
 * Test script to verify the Artist/Braider onboarding flow
 * This script tests token generation, email template creation, and URL validation
 */

import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'
import { generateWelcomeEmail } from '../lib/email-templates.js'

// Load environment variables
config({ path: '.env.local' })

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      persistSession: false,
      autoRefreshToken: false
    }
  }
)

async function testOnboardingFlow() {
  console.log('🧪 Testing Artist/Braider Onboarding Flow\n')

  // Test 1: Token Generation
  console.log('1. Testing token generation...')
  try {
    const { data: generatedToken, error: tokenError } = await supabase
      .rpc('generate_application_token')

    if (tokenError) {
      console.error('❌ Token generation failed:', tokenError)
      return false
    }

    if (!generatedToken) {
      console.error('❌ No token returned from generation function')
      return false
    }

    console.log('✅ Token generated successfully:', generatedToken.substring(0, 8) + '...')
  } catch (error) {
    console.error('❌ Token generation error:', error)
    return false
  }

  // Test 2: Email Template Generation
  console.log('\n2. Testing email template generation...')
  try {
    const testUser = {
      name: 'Test Artist',
      email: '<EMAIL>',
      role: 'artist',
      applicationToken: 'test-token-123456789'
    }

    const emailTemplate = generateWelcomeEmail(testUser)

    if (!emailTemplate.subject || !emailTemplate.htmlBody) {
      console.error('❌ Email template generation failed - missing subject or body')
      return false
    }

    // Check if the application link is correctly formatted
    const expectedLink = `https://www.oceansoulsparkles.com.au/apply/artist?token=${testUser.applicationToken}`
    if (!emailTemplate.htmlBody.includes(expectedLink)) {
      console.error('❌ Email template missing correct application link')
      console.log('Expected:', expectedLink)
      console.log('Email body contains:', emailTemplate.htmlBody.includes('apply/artist'))
      return false
    }

    console.log('✅ Email template generated successfully')
    console.log('✅ Application link correctly formatted:', expectedLink)
  } catch (error) {
    console.error('❌ Email template generation error:', error)
    return false
  }

  // Test 3: Database Functions
  console.log('\n3. Testing database functions...')
  try {
    // Test token validation function exists
    const { data: functionExists, error: functionError } = await supabase
      .rpc('validate_application_token', { token_value: 'test-token' })

    // We expect this to fail with invalid token, but function should exist
    if (functionError && !functionError.message.includes('invalid') && !functionError.message.includes('expired')) {
      console.error('❌ Database function validate_application_token not found:', functionError)
      return false
    }

    console.log('✅ Database functions are accessible')
  } catch (error) {
    console.error('❌ Database function test error:', error)
    return false
  }

  // Test 4: URL Validation
  console.log('\n4. Testing URL validation...')
  const testUrls = [
    'https://www.oceansoulsparkles.com.au/apply/artist?token=test123',
    'https://www.oceansoulsparkles.com.au/apply/braider?token=test456'
  ]

  for (const url of testUrls) {
    try {
      const urlObj = new URL(url)
      const token = urlObj.searchParams.get('token')
      const role = urlObj.pathname.split('/').pop()

      if (!token || !['artist', 'braider'].includes(role)) {
        console.error('❌ Invalid URL format:', url)
        return false
      }

      console.log('✅ URL format valid:', url)
    } catch (error) {
      console.error('❌ URL validation error:', url, error)
      return false
    }
  }

  console.log('\n🎉 All onboarding flow tests passed!')
  return true
}

// Run the test
testOnboardingFlow()
  .then(success => {
    if (success) {
      console.log('\n✅ Onboarding flow is working correctly')
      process.exit(0)
    } else {
      console.log('\n❌ Onboarding flow has issues that need to be fixed')
      process.exit(1)
    }
  })
  .catch(error => {
    console.error('\n💥 Test script error:', error)
    process.exit(1)
  })
