/* Availability Calendar Card Styles */
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid #e5e7eb;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.statusIndicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.statusDot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #10b981;
}

.statusText {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Availability Section */
.availabilitySection h4 {
  margin: 0 0 12px 0;
  color: #374151;
  font-size: 1rem;
  font-weight: 600;
}

.toggleGroup {
  display: flex;
  gap: 8px;
}

.toggleButton {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #6b7280;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toggleButton:hover {
  border-color: #d1d5db;
  background: #f9fafb;
}

.toggleButton.active {
  border-color: #10b981;
  background: #10b981;
  color: white;
}

.toggleButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Schedule Section */
.scheduleSection h4 {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 1rem;
  font-weight: 600;
}

.scheduleList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.scheduleItem {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.timeSlot {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  min-width: 120px;
}

.bookingDetails {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.serviceName {
  font-weight: 500;
  color: #1f2937;
  font-size: 0.875rem;
}

.customerName {
  font-size: 0.75rem;
  color: #6b7280;
}

.statusBadge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  color: white;
  text-transform: capitalize;
}

.emptyState {
  text-align: center;
  padding: 32px 16px;
  color: #6b7280;
  font-style: italic;
}

/* Weekly Section */
.weeklySection h4 {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 1rem;
  font-weight: 600;
}

.weeklyGrid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
}

.dayCard {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px 8px;
  text-align: center;
}

.dayHeader {
  margin-bottom: 8px;
}

.dayName {
  display: block;
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
}

.dayDate {
  display: block;
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin-top: 4px;
}

.dayBookings {
  font-size: 0.75rem;
}

.bookingCount {
  color: #059669;
  font-weight: 500;
}

.noBookings {
  color: #6b7280;
}

/* Stats Section */
.statsSection {
  display: flex;
  gap: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.statItem {
  flex: 1;
  text-align: center;
}

.statLabel {
  display: block;
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  font-weight: 500;
  margin-bottom: 4px;
}

.statValue {
  display: block;
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
}

/* Actions Section */
.actionsSection {
  display: flex;
  gap: 12px;
}

.actionButton {
  flex: 1;
  padding: 12px 16px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  color: #374151;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.actionButton:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card {
    padding: 16px;
    margin-bottom: 16px;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .weeklyGrid {
    grid-template-columns: repeat(4, 1fr);
    gap: 6px;
  }

  .dayCard {
    padding: 8px 4px;
  }

  .dayDate {
    font-size: 1rem;
  }

  .statsSection {
    flex-direction: column;
    gap: 16px;
  }

  .actionsSection {
    flex-direction: column;
  }

  .scheduleItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .timeSlot {
    min-width: auto;
  }
}
